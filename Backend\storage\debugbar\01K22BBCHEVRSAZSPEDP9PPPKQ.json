{"__meta": {"id": "01K22BBCHEVRSAZSPEDP9PPPKQ", "datetime": "2025-08-07 14:25:56", "utime": **********.911082, "method": "GET", "uri": "/cache/medium/theme/14/uaLpRQEE11Mf2POHgzrrxSWw1HZ2Ut9h2UnAlZG3.webp", "ip": "127.0.0.1"}, "modules": {"count": 0, "modules": []}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.694565, "end": **********.92086, "duration": 0.22629499435424805, "duration_str": "226ms", "measures": [{"label": "Booting", "start": **********.694565, "relative_start": 0, "end": **********.891288, "relative_end": **********.891288, "duration": 0.*****************, "duration_str": "197ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.891298, "relative_start": 0.****************, "end": **********.920862, "relative_end": 1.9073486328125e-06, "duration": 0.*****************, "duration_str": "29.56ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.901976, "relative_start": 0.****************, "end": **********.905452, "relative_end": **********.905452, "duration": 0.0034759044647216797, "duration_str": "3.48ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.909494, "relative_start": 0.*****************, "end": **********.909592, "relative_end": **********.909592, "duration": 9.799003601074219e-05, "duration_str": "98μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.909605, "relative_start": 0.*****************, "end": **********.909617, "relative_end": **********.909617, "duration": 1.1920928955078125e-05, "duration_str": "12μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "32MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.2.0", "Environment": "local", "Debug Mode": "Enabled", "URL": "mlk.test", "Timezone": "Africa/Lagos", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 0, "nb_statements": 0, "nb_visible_statements": 0, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://mlk.test/cache/medium/theme/14/uaLpRQEE11Mf2POHgzrrxSWw1HZ2Ut9h2UnAlZG3.webp", "action_name": "imagecache", "controller_action": "Webkul\\Core\\ImageCache\\Controller@getResponse", "uri": "GET cache/{template}/{filename}", "controller": "Webkul\\Core\\ImageCache\\Controller@getResponse<a href=\"phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FImageCache%2FController.php&line=34\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FImageCache%2FController.php&line=34\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">packages/Webkul/Core/src/ImageCache/Controller.php:34-46</a>", "duration": "227ms", "peak_memory": "36MB", "response": "image/webp", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-682844857 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-682844857\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-371083775 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-371083775\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-525708620 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"743 characters\">sidebar_collapsed=0; dark_mode=0; XSRF-TOKEN=eyJpdiI6ImVqZWZnallTQnBZM0I3WDh5K3drNnc9PSIsInZhbHVlIjoiMWNwOWlYVlVVWjFFbXJhYmtQd1hiMWtVMG5iUm54SUlIWXBYbjM1SVRNc2JuTzhFeTRuRkpwKzBpU1pwVmsxL2dnVVVSNjdyMXplWlZ1d2FTa1ljWnl1QVZuU3hzU0V6ZXprUWJaYUJSVW0xcHBCbitTbUgrdUJTbGZSMFpqSk8iLCJtYWMiOiJkODU5YmY2YWI4Y2E3NzJiNDk5MWE1YjZmOGQyMzQ2MDQ1N2QyNWFkY2JjODAzYmE1OGVlMThkNDlkZTdkMTZhIiwidGFnIjoiIn0%3D; mlk_session=eyJpdiI6IkNubUdIYm9ML0h5TFVtL2NqS0VBa3c9PSIsInZhbHVlIjoiZTk0TnJxdGswdVRaQXdhY3R3cjMxYUVkbjcyQ0huMUZCZnFqOGY1SHRzSTVGY2JKTS85R0dDOGdhYXNSNUNRUUpDSlVXQ2V5Ykk5aitROGtIWUVZY05RMzdVWGIvdFY1cHFqQ1J2bmlTQ0w0SEFFOVdyZTNNdUM0Ti9tY1VQSnYiLCJtYWMiOiJhZmNlZGVmNGEyZTViMDZiMGM3NDg0NzMyNWUyZmNjZDk4NzY1ZTliMDIxZmNhYmQ5NTZiOTIzZGFkN2MwOTU2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">zh-CN,zh;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://mlk.test/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">mlk.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-525708620\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1978500145 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>sidebar_collapsed</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>dark_mode</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6ImVqZWZnallTQnBZM0I3WDh5K3drNnc9PSIsInZhbHVlIjoiMWNwOWlYVlVVWjFFbXJhYmtQd1hiMWtVMG5iUm54SUlIWXBYbjM1SVRNc2JuTzhFeTRuRkpwKzBpU1pwVmsxL2dnVVVSNjdyMXplWlZ1d2FTa1ljWnl1QVZuU3hzU0V6ZXprUWJaYUJSVW0xcHBCbitTbUgrdUJTbGZSMFpqSk8iLCJtYWMiOiJkODU5YmY2YWI4Y2E3NzJiNDk5MWE1YjZmOGQyMzQ2MDQ1N2QyNWFkY2JjODAzYmE1OGVlMThkNDlkZTdkMTZhIiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>mlk_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IkNubUdIYm9ML0h5TFVtL2NqS0VBa3c9PSIsInZhbHVlIjoiZTk0TnJxdGswdVRaQXdhY3R3cjMxYUVkbjcyQ0huMUZCZnFqOGY1SHRzSTVGY2JKTS85R0dDOGdhYXNSNUNRUUpDSlVXQ2V5Ykk5aitROGtIWUVZY05RMzdVWGIvdFY1cHFqQ1J2bmlTQ0w0SEFFOVdyZTNNdUM0Ti9tY1VQSnYiLCJtYWMiOiJhZmNlZGVmNGEyZTViMDZiMGM3NDg0NzMyNWUyZmNjZDk4NzY1ZTliMDIxZmNhYmQ5NTZiOTIzZGFkN2MwOTU2IiwidGFnIjoiIn0=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1978500145\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-842400344 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">image/webp</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">max-age=31536000, public</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">26416</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>etag</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">72941995539bc8921b595b3466a68011</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 07 Aug 2025 13:25:56 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-842400344\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1080297209 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1080297209\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://mlk.test/cache/medium/theme/14/uaLpRQEE11Mf2POHgzrrxSWw1HZ2Ut9h2UnAlZG3.webp", "action_name": "imagecache", "controller_action": "Webkul\\Core\\ImageCache\\Controller@getResponse"}, "badge": null}}