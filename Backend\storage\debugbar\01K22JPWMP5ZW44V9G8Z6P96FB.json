{"__meta": {"id": "01K22JPWMP5ZW44V9G8Z6P96FB", "datetime": "2025-08-07 16:34:33", "utime": **********.879118, "method": "POST", "uri": "/admin/settings/themes/edit/42", "ip": "127.0.0.1"}, "modules": {"count": 3, "modules": [{"name": "Webkul\\Core", "models": ["Webkul\\Core\\Models\\Channel (1)", "Webkul\\Core\\Models\\Locale (5)", "Webkul\\Core\\Models\\Currency (1)"], "views": [], "queries": [{"sql": "select * from `channels` where `hostname` in ('mlk.test', 'http://mlk.test', 'https://mlk.test')", "duration": 2.43, "duration_str": "2.43s", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 2.14, "duration_str": "2.14s", "connection": "mlk"}, {"sql": "select * from `currencies` where `currencies`.`id` = 2 limit 1", "duration": 1.89, "duration_str": "1.89s", "connection": "mlk"}]}, {"name": "Webkul\\Theme", "models": ["Webkul\\Theme\\Models\\ThemeCustomization (1)", "Webkul\\Theme\\Models\\ThemeCustomizationTranslation (5)"], "views": [], "queries": [{"sql": "select * from `theme_customizations` where `theme_customizations`.`id` = 42 limit 1", "duration": 1.87, "duration_str": "1.87s", "connection": "mlk"}, {"sql": "select * from `theme_customization_translations` where `theme_customization_translations`.`theme_customization_id` in (42)", "duration": 2.05, "duration_str": "2.05s", "connection": "mlk"}, {"sql": "update `theme_customizations` set `status` = 1, `theme_customizations`.`updated_at` = '2025-08-07 16:34:33' where `id` = 42", "duration": 2.4, "duration_str": "2.4s", "connection": "mlk"}, {"sql": "update `theme_customization_translations` set `options` = '{\\\"html\\\":\\\"<section class=\\\\\"shop-by-brand-section\\\\\">\\r\\n        <div class=\\\\\"container\\\\\">\\r\\n            <h2 class=\\\\\"section-title\\\\\">\\r\\n                SHOP BY BRAND\\r\\n            <\\/h2>\\r\\n\\r\\n            <div class=\\\\\"brand-grid\\\\\">\\r\\n                <a href=\\\\\"#\\\\\" class=\\\\\"brand-logo-link\\\\\">\\r\\n                    <img src=\\\\\"\\/storage\\/theme\\/42\\/wG6bHeSQVQVxYO67SU6nS1K3eQXLaRVdHMLpreCC.webp\\\\\" alt=\\\\\"Apple\\\\\" class=\\\\\"brand-logo logo-apple\\\\\">\\r\\n                <\\/a>\\r\\n                <a href=\\\\\"#\\\\\" class=\\\\\"brand-logo-link\\\\\">\\r\\n                    <img src=\\\\\"\\/storage\\/theme\\/42\\/lEBIAJiOip7tcMCDZn6dXngX4ieMbpqct900OgYp.webp\\\\\" alt=\\\\\"Samsung\\\\\" class=\\\\\"brand-logo logo-samsung\\\\\">\\r\\n                <\\/a>\\r\\n                <a href=\\\\\"#\\\\\" class=\\\\\"brand-logo-link\\\\\">\\r\\n                    <img src=\\\\\"\\/storage\\/theme\\/42\\/ZlhWdHlUNqBbsr9ap91NemQT8Vaas46AiwVEbOYF.webp\\\\\" alt=\\\\\"Motorola\\\\\" class=\\\\\"brand-logo logo-motorola\\\\\">\\r\\n                <\\/a>\\r\\n                <a href=\\\\\"#\\\\\" class=\\\\\"brand-logo-link\\\\\">\\r\\n                    <img src=\\\\\"\\/storage\\/theme\\/42\\/mKjaC7o96snLqlvrNiWUhre6ToYNnCrRQ5iJAAkn.webp\\\\\" alt=\\\\\"Huawei\\\\\" class=\\\\\"brand-logo logo-huawei\\\\\">\\r\\n                <\\/a>\\r\\n                <a href=\\\\\"#\\\\\" class=\\\\\"brand-logo-link\\\\\">\\r\\n                    <img src=\\\\\"\\/storage\\/theme\\/42\\/aouSOFFs541Cv4Bu7YKER4I418y8GkVpA7jr9owz.webp\\\\\" alt=\\\\\"Xiaomi\\\\\" class=\\\\\"brand-logo logo-xiaomi\\\\\">\\r\\n                <\\/a>\\r\\n                <a href=\\\\\"#\\\\\" class=\\\\\"brand-logo-link\\\\\">\\r\\n                    <img src=\\\\\"\\/storage\\/theme\\/42\\/8EKdYRB1UZgov1PO1rbI9Jrj2hZR5xEn8XkShRCo.webp\\\\\" alt=\\\\\"Oppo\\\\\" class=\\\\\"brand-logo logo-oppo\\\\\">\\r\\n                <\\/a>\\r\\n            <\\/div>\\r\\n        <\\/div>\\r\\n    <\\/section>\\\",\\\"css\\\":\\\"\\/* \\u533a\\u57df\\u5bb9\\u5668\\u6837\\u5f0f *\\/\\r\\n        .shop-by-brand-section {\\r\\n            background-color: #ffffff;\\r\\n            padding-top: 3rem; \\/* \\u5bf9\\u5e94 py-12 *\\/\\r\\n            padding-bottom: 3rem; \\/* \\u5bf9\\u5e94 py-12 *\\/\\r\\n        }\\r\\n\\r\\n        \\/* \\u5185\\u5bb9\\u4e3b\\u5bb9\\u5668 *\\/\\r\\n        .container {\\r\\n            max-width: 80rem; \\/* \\u5bf9\\u5e94 max-w-7xl *\\/\\r\\n            margin-left: auto;\\r\\n            margin-right: auto;\\r\\n            padding-left: 1.5rem; \\/* \\u5bf9\\u5e94 px-6 *\\/\\r\\n            padding-right: 1.5rem; \\/* \\u5bf9\\u5e94 px-6 *\\/\\r\\n        }\\r\\n\\r\\n        \\/* \\u6807\\u9898\\u6837\\u5f0f *\\/\\r\\n        .section-title {\\r\\n            text-align: center; \\/* \\u5bf9\\u5e94 text-center *\\/\\r\\n            font-size: 1.25rem; \\/* \\u5bf9\\u5e94 text-xl *\\/\\r\\n            font-weight: 700;   \\/* \\u5bf9\\u5e94 font-bold *\\/\\r\\n            letter-spacing: 0.1em; \\/* \\u5bf9\\u5e94 tracking-widest *\\/\\r\\n            color: rgb(31 41 55);  \\/* \\u5bf9\\u5e94 text-gray-800 *\\/\\r\\n        }\\r\\n\\r\\n        \\/* \\u54c1\\u724cLogo\\u7f51\\u683c\\u5e03\\u5c40 *\\/\\r\\n        .brand-grid {\\r\\n            margin-top: 2.5rem; \\/* \\u5bf9\\u5e94 mt-10 *\\/\\r\\n            display: grid;\\r\\n            grid-template-columns: repeat(2, 1fr); \\/* \\u5bf9\\u5e94 grid-cols-2 *\\/\\r\\n            align-items: center; \\/* \\u5bf9\\u5e94 items-center *\\/\\r\\n            column-gap: 2rem; \\/* \\u5bf9\\u5e94 gap-x-8 *\\/\\r\\n            row-gap: 2.5rem;  \\/* \\u5bf9\\u5e94 gap-y-10 *\\/\\r\\n            max-width: 32rem; \\/* \\u5bf9\\u5e94 max-w-lg *\\/\\r\\n            margin-left: auto;\\r\\n            margin-right: auto;\\r\\n        }\\r\\n\\r\\n        \\/* Logo\\u94fe\\u63a5\\u6837\\u5f0f *\\/\\r\\n        .brand-logo-link {\\r\\n            display: flex;\\r\\n            justify-content: center; \\/* \\u5bf9\\u5e94 justify-center *\\/\\r\\n            padding: 1rem; \\/* \\u5bf9\\u5e94 p-4 *\\/\\r\\n            transition: opacity 0.15s ease-in-out; \\/* \\u5bf9\\u5e94 transition *\\/\\r\\n        }\\r\\n\\r\\n        \\/* \\u9f20\\u6807\\u60ac\\u505c\\u6548\\u679c *\\/\\r\\n        .brand-logo-link:hover {\\r\\n            opacity: 0.75; \\/* \\u5bf9\\u5e94 hover:opacity-75 *\\/\\r\\n        }\\r\\n\\r\\n        \\/* \\u56fe\\u7247\\u6837\\u5f0f *\\/\\r\\n        .brand-logo {\\r\\n            object-fit: contain; \\/* \\u5bf9\\u5e94 object-contain *\\/\\r\\n        }\\r\\n\\r\\n        \\/* \\u4e0d\\u540cLogo\\u7684\\u9ad8\\u5ea6 *\\/\\r\\n        .logo-apple, .logo-motorola, .logo-huawei { height: 2.5rem; } \\/* h-10 *\\/\\r\\n        .logo-samsung, .logo-oppo { height: 2rem; } \\/* h-8 *\\/\\r\\n        .logo-xiaomi { height: 2.25rem; } \\/* h-9 *\\/\\r\\n\\r\\n        \\/* * \\u54cd\\u5e94\\u5f0f\\u8bbe\\u8ba1: \\u5a92\\u4f53\\u67e5\\u8be2\\r\\n         *\\/\\r\\n\\r\\n        \\/* \\u4e2d\\u7b49\\u5c4f\\u5e55 (640px\\u53ca\\u4ee5\\u4e0a), \\u5bf9\\u5e94 sm: \\u524d\\u7f00 *\\/\\r\\n        @media (min-width: 640px) {\\r\\n            .shop-by-brand-section {\\r\\n                padding-top: 4rem; \\/* \\u5bf9\\u5e94 sm:py-16 *\\/\\r\\n                padding-bottom: 4rem;\\r\\n            }\\r\\n            .brand-grid {\\r\\n                max-width: 36rem; \\/* \\u5bf9\\u5e94 sm:max-w-xl *\\/\\r\\n                grid-template-columns: repeat(3, 1fr); \\/* \\u5bf9\\u5e94 sm:grid-cols-3 *\\/\\r\\n                column-gap: 2.5rem; \\/* \\u5bf9\\u5e94 sm:gap-x-10 *\\/\\r\\n            }\\r\\n        }\\r\\n\\r\\n        \\/* \\u5927\\u5c4f\\u5e55 (1024px\\u53ca\\u4ee5\\u4e0a), \\u5bf9\\u5e94 lg: \\u524d\\u7f00 *\\/\\r\\n        @media (min-width: 1024px) {\\r\\n            .container {\\r\\n                padding-left: 2rem; \\/* \\u5bf9\\u5e94 lg:px-8 *\\/\\r\\n                padding-right: 2rem;\\r\\n            }\\r\\n            .brand-grid {\\r\\n                max-width: none; \\/* \\u5bf9\\u5e94 lg:max-w-none *\\/\\r\\n                margin-left: 0;  \\/* \\u5bf9\\u5e94 lg:mx-0 *\\/\\r\\n                margin-right: 0;\\r\\n                grid-template-columns: repeat(6, 1fr); \\/* \\u5bf9\\u5e94 lg:grid-cols-6 *\\/\\r\\n            }\\r\\n        }\\\"}' where `id` = 181", "duration": 4.53, "duration_str": "4.53s", "connection": "mlk"}]}, {"name": "Webkul\\User", "models": ["Webkul\\User\\Models\\Admin (1)", "Webkul\\User\\Models\\Role (1)"], "views": [], "queries": [{"sql": "select count(*) as aggregate from `admins`", "duration": 0.23, "duration_str": "230ms", "connection": "mlk"}, {"sql": "select count(*) as aggregate from `admins`", "duration": 0.19, "duration_str": "190ms", "connection": "mlk"}, {"sql": "select * from `admins` where `id` = 1 limit 1", "duration": 0.24, "duration_str": "240ms", "connection": "mlk"}, {"sql": "select * from `roles` where `roles`.`id` = 1 limit 1", "duration": 1.95, "duration_str": "1.95s", "connection": "mlk"}]}]}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.544992, "end": **********.886009, "duration": 0.3410170078277588, "duration_str": "341ms", "measures": [{"label": "Booting", "start": **********.544992, "relative_start": 0, "end": **********.772712, "relative_end": **********.772712, "duration": 0.*****************, "duration_str": "228ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.772724, "relative_start": 0.*****************, "end": **********.88601, "relative_end": 9.5367431640625e-07, "duration": 0.*****************, "duration_str": "113ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.785932, "relative_start": 0.*****************, "end": **********.788273, "relative_end": **********.788273, "duration": 0.002341032028198242, "duration_str": "2.34ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.87734, "relative_start": 0.***************, "end": **********.877631, "relative_end": **********.877631, "duration": 0.00029087066650390625, "duration_str": "291μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "39MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.2.0", "Environment": "local", "Debug Mode": "Enabled", "URL": "mlk.test", "Timezone": "Africa/Lagos", "Locale": "zh_CN"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 13, "nb_statements": 13, "nb_visible_statements": 13, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.02069, "accumulated_duration_str": "20.69ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select exists (select 1 from information_schema.tables where table_schema = 'mlk' and table_name = 'admins' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 32}, {"index": 14, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 44}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 1004}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 832}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 1078}], "start": **********.80132, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "DatabaseManager.php:32", "source": {"index": 13, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 32}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FInstaller%2Fsrc%2FHelpers%2FDatabaseManager.php&line=32", "ajax": false, "filename": "DatabaseManager.php", "line": "32"}, "connection": "mlk", "explain": null, "start_percent": 0, "width_percent": 2.223}, {"sql": "select count(*) as aggregate from `admins`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 38}, {"index": 16, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 44}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 1004}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 832}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 1078}], "start": **********.805539, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "DatabaseManager.php:38", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 38}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FInstaller%2Fsrc%2FHelpers%2FDatabaseManager.php&line=38", "ajax": false, "filename": "DatabaseManager.php", "line": "38"}, "connection": "mlk", "explain": null, "start_percent": 2.223, "width_percent": 1.112}, {"sql": "select * from `channels` where `hostname` in ('mlk.test', 'http://mlk.test', 'https://mlk.test')", "type": "query", "params": [], "bindings": ["mlk.test", "http://mlk.test", "https://mlk.test"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 578}, {"index": 16, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Core.php", "line": 143}, {"index": 17, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 127}, {"index": 18, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 45}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 1004}], "start": **********.812612, "duration": 0.0024300000000000003, "duration_str": "2.43ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:578", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 578}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=578", "ajax": false, "filename": "BaseRepository.php", "line": "578"}, "connection": "mlk", "explain": null, "start_percent": 3.335, "width_percent": 11.745}, {"sql": "select exists (select 1 from information_schema.tables where table_schema = 'mlk' and table_name = 'admins' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 32}, {"index": 14, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 59}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 16, "namespace": "middleware", "name": "admin_locale", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Middleware\\AdminLocale.php", "line": 46}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.817356, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "DatabaseManager.php:32", "source": {"index": 13, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 32}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FInstaller%2Fsrc%2FHelpers%2FDatabaseManager.php&line=32", "ajax": false, "filename": "DatabaseManager.php", "line": "32"}, "connection": "mlk", "explain": null, "start_percent": 15.08, "width_percent": 1.498}, {"sql": "select count(*) as aggregate from `admins`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 38}, {"index": 16, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 18, "namespace": "middleware", "name": "admin_locale", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Middleware\\AdminLocale.php", "line": 46}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.818789, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "DatabaseManager.php:38", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 38}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FInstaller%2Fsrc%2FHelpers%2FDatabaseManager.php&line=38", "ajax": false, "filename": "DatabaseManager.php", "line": "38"}, "connection": "mlk", "explain": null, "start_percent": 16.578, "width_percent": 0.918}, {"sql": "select * from `admins` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "admin", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\User\\src\\Http\\Middleware\\Bouncer.php", "line": 18}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.8225129, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "mlk", "explain": null, "start_percent": 17.496, "width_percent": 1.16}, {"sql": "select * from `roles` where `roles`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": "middleware", "name": "admin", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\User\\src\\Http\\Middleware\\Bouncer.php", "line": 54}, {"index": 22, "namespace": "middleware", "name": "admin", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\User\\src\\Http\\Middleware\\Bouncer.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 24, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 119}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.825404, "duration": 0.00195, "duration_str": "1.95ms", "memory": 0, "memory_str": null, "filename": "admin:54", "source": {"index": 21, "namespace": "middleware", "name": "admin", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\User\\src\\Http\\Middleware\\Bouncer.php", "line": 54}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FUser%2Fsrc%2FHttp%2FMiddleware%2FBouncer.php&line=54", "ajax": false, "filename": "Bouncer.php", "line": "54"}, "connection": "mlk", "explain": null, "start_percent": 18.656, "width_percent": 9.425}, {"sql": "select * from `theme_customizations` where `theme_customizations`.`id` = 42 limit 1", "type": "query", "params": [], "bindings": [42], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, {"index": 21, "namespace": null, "name": "packages/Webkul/Theme/src/Repositories/ThemeCustomizationRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Theme\\src\\Repositories\\ThemeCustomizationRepository.php", "line": 42}, {"index": 22, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Settings/ThemeController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Settings\\ThemeController.php", "line": 117}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.8308182, "duration": 0.0018700000000000001, "duration_str": "1.87ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:695", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=695", "ajax": false, "filename": "BaseRepository.php", "line": "695"}, "connection": "mlk", "explain": null, "start_percent": 28.081, "width_percent": 9.038}, {"sql": "select * from `theme_customization_translations` where `theme_customization_translations`.`theme_customization_id` in (42)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 25, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, {"index": 26, "namespace": null, "name": "packages/Webkul/Theme/src/Repositories/ThemeCustomizationRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Theme\\src\\Repositories\\ThemeCustomizationRepository.php", "line": 42}, {"index": 27, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Settings/ThemeController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Settings\\ThemeController.php", "line": 117}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.834738, "duration": 0.0020499999999999997, "duration_str": "2.05ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:695", "source": {"index": 25, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=695", "ajax": false, "filename": "BaseRepository.php", "line": "695"}, "connection": "mlk", "explain": null, "start_percent": 37.119, "width_percent": 9.908}, {"sql": "update `theme_customizations` set `status` = 1, `theme_customizations`.`updated_at` = '2025-08-07 16:34:33' where `id` = 42", "type": "query", "params": [], "bindings": [1, "2025-08-07 16:34:33", 42], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 700}, {"index": 15, "namespace": null, "name": "packages/Webkul/Theme/src/Repositories/ThemeCustomizationRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Theme\\src\\Repositories\\ThemeCustomizationRepository.php", "line": 42}, {"index": 16, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Settings/ThemeController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Settings\\ThemeController.php", "line": 117}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.848211, "duration": 0.0024, "duration_str": "2.4ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:700", "source": {"index": 14, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 700}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=700", "ajax": false, "filename": "BaseRepository.php", "line": "700"}, "connection": "mlk", "explain": null, "start_percent": 47.028, "width_percent": 11.6}, {"sql": "update `theme_customization_translations` set `options` = '{\\\"html\\\":\\\"<section class=\\\\\\\"shop-by-brand-section\\\\\\\">\\\\r\\\\n        <div class=\\\\\\\"container\\\\\\\">\\\\r\\\\n            <h2 class=\\\\\\\"section-title\\\\\\\">\\\\r\\\\n                SHOP BY BRAND\\\\r\\\\n            <\\\\/h2>\\\\r\\\\n\\\\r\\\\n            <div class=\\\\\\\"brand-grid\\\\\\\">\\\\r\\\\n                <a href=\\\\\\\"#\\\\\\\" class=\\\\\\\"brand-logo-link\\\\\\\">\\\\r\\\\n                    <img src=\\\\\\\"\\\\/storage\\\\/theme\\\\/42\\\\/wG6bHeSQVQVxYO67SU6nS1K3eQXLaRVdHMLpreCC.webp\\\\\\\" alt=\\\\\\\"Apple\\\\\\\" class=\\\\\\\"brand-logo logo-apple\\\\\\\">\\\\r\\\\n                <\\\\/a>\\\\r\\\\n                <a href=\\\\\\\"#\\\\\\\" class=\\\\\\\"brand-logo-link\\\\\\\">\\\\r\\\\n                    <img src=\\\\\\\"\\\\/storage\\\\/theme\\\\/42\\\\/lEBIAJiOip7tcMCDZn6dXngX4ieMbpqct900OgYp.webp\\\\\\\" alt=\\\\\\\"Samsung\\\\\\\" class=\\\\\\\"brand-logo logo-samsung\\\\\\\">\\\\r\\\\n                <\\\\/a>\\\\r\\\\n                <a href=\\\\\\\"#\\\\\\\" class=\\\\\\\"brand-logo-link\\\\\\\">\\\\r\\\\n                    <img src=\\\\\\\"\\\\/storage\\\\/theme\\\\/42\\\\/ZlhWdHlUNqBbsr9ap91NemQT8Vaas46AiwVEbOYF.webp\\\\\\\" alt=\\\\\\\"Motorola\\\\\\\" class=\\\\\\\"brand-logo logo-motorola\\\\\\\">\\\\r\\\\n                <\\\\/a>\\\\r\\\\n                <a href=\\\\\\\"#\\\\\\\" class=\\\\\\\"brand-logo-link\\\\\\\">\\\\r\\\\n                    <img src=\\\\\\\"\\\\/storage\\\\/theme\\\\/42\\\\/mKjaC7o96snLqlvrNiWUhre6ToYNnCrRQ5iJAAkn.webp\\\\\\\" alt=\\\\\\\"Huawei\\\\\\\" class=\\\\\\\"brand-logo logo-huawei\\\\\\\">\\\\r\\\\n                <\\\\/a>\\\\r\\\\n                <a href=\\\\\\\"#\\\\\\\" class=\\\\\\\"brand-logo-link\\\\\\\">\\\\r\\\\n                    <img src=\\\\\\\"\\\\/storage\\\\/theme\\\\/42\\\\/aouSOFFs541Cv4Bu7YKER4I418y8GkVpA7jr9owz.webp\\\\\\\" alt=\\\\\\\"Xiaomi\\\\\\\" class=\\\\\\\"brand-logo logo-xiaomi\\\\\\\">\\\\r\\\\n                <\\\\/a>\\\\r\\\\n                <a href=\\\\\\\"#\\\\\\\" class=\\\\\\\"brand-logo-link\\\\\\\">\\\\r\\\\n                    <img src=\\\\\\\"\\\\/storage\\\\/theme\\\\/42\\\\/8EKdYRB1UZgov1PO1rbI9Jrj2hZR5xEn8XkShRCo.webp\\\\\\\" alt=\\\\\\\"Oppo\\\\\\\" class=\\\\\\\"brand-logo logo-oppo\\\\\\\">\\\\r\\\\n                <\\\\/a>\\\\r\\\\n            <\\\\/div>\\\\r\\\\n        <\\\\/div>\\\\r\\\\n    <\\\\/section>\\\",\\\"css\\\":\\\"\\\\/* \\\\u533a\\\\u57df\\\\u5bb9\\\\u5668\\\\u6837\\\\u5f0f *\\\\/\\\\r\\\\n        .shop-by-brand-section {\\\\r\\\\n            background-color: #ffffff;\\\\r\\\\n            padding-top: 3rem; \\\\/* \\\\u5bf9\\\\u5e94 py-12 *\\\\/\\\\r\\\\n            padding-bottom: 3rem; \\\\/* \\\\u5bf9\\\\u5e94 py-12 *\\\\/\\\\r\\\\n        }\\\\r\\\\n\\\\r\\\\n        \\\\/* \\\\u5185\\\\u5bb9\\\\u4e3b\\\\u5bb9\\\\u5668 *\\\\/\\\\r\\\\n        .container {\\\\r\\\\n            max-width: 80rem; \\\\/* \\\\u5bf9\\\\u5e94 max-w-7xl *\\\\/\\\\r\\\\n            margin-left: auto;\\\\r\\\\n            margin-right: auto;\\\\r\\\\n            padding-left: 1.5rem; \\\\/* \\\\u5bf9\\\\u5e94 px-6 *\\\\/\\\\r\\\\n            padding-right: 1.5rem; \\\\/* \\\\u5bf9\\\\u5e94 px-6 *\\\\/\\\\r\\\\n        }\\\\r\\\\n\\\\r\\\\n        \\\\/* \\\\u6807\\\\u9898\\\\u6837\\\\u5f0f *\\\\/\\\\r\\\\n        .section-title {\\\\r\\\\n            text-align: center; \\\\/* \\\\u5bf9\\\\u5e94 text-center *\\\\/\\\\r\\\\n            font-size: 1.25rem; \\\\/* \\\\u5bf9\\\\u5e94 text-xl *\\\\/\\\\r\\\\n            font-weight: 700;   \\\\/* \\\\u5bf9\\\\u5e94 font-bold *\\\\/\\\\r\\\\n            letter-spacing: 0.1em; \\\\/* \\\\u5bf9\\\\u5e94 tracking-widest *\\\\/\\\\r\\\\n            color: rgb(31 41 55);  \\\\/* \\\\u5bf9\\\\u5e94 text-gray-800 *\\\\/\\\\r\\\\n        }\\\\r\\\\n\\\\r\\\\n        \\\\/* \\\\u54c1\\\\u724cLogo\\\\u7f51\\\\u683c\\\\u5e03\\\\u5c40 *\\\\/\\\\r\\\\n        .brand-grid {\\\\r\\\\n            margin-top: 2.5rem; \\\\/* \\\\u5bf9\\\\u5e94 mt-10 *\\\\/\\\\r\\\\n            display: grid;\\\\r\\\\n            grid-template-columns: repeat(2, 1fr); \\\\/* \\\\u5bf9\\\\u5e94 grid-cols-2 *\\\\/\\\\r\\\\n            align-items: center; \\\\/* \\\\u5bf9\\\\u5e94 items-center *\\\\/\\\\r\\\\n            column-gap: 2rem; \\\\/* \\\\u5bf9\\\\u5e94 gap-x-8 *\\\\/\\\\r\\\\n            row-gap: 2.5rem;  \\\\/* \\\\u5bf9\\\\u5e94 gap-y-10 *\\\\/\\\\r\\\\n            max-width: 32rem; \\\\/* \\\\u5bf9\\\\u5e94 max-w-lg *\\\\/\\\\r\\\\n            margin-left: auto;\\\\r\\\\n            margin-right: auto;\\\\r\\\\n        }\\\\r\\\\n\\\\r\\\\n        \\\\/* Logo\\\\u94fe\\\\u63a5\\\\u6837\\\\u5f0f *\\\\/\\\\r\\\\n        .brand-logo-link {\\\\r\\\\n            display: flex;\\\\r\\\\n            justify-content: center; \\\\/* \\\\u5bf9\\\\u5e94 justify-center *\\\\/\\\\r\\\\n            padding: 1rem; \\\\/* \\\\u5bf9\\\\u5e94 p-4 *\\\\/\\\\r\\\\n            transition: opacity 0.15s ease-in-out; \\\\/* \\\\u5bf9\\\\u5e94 transition *\\\\/\\\\r\\\\n        }\\\\r\\\\n\\\\r\\\\n        \\\\/* \\\\u9f20\\\\u6807\\\\u60ac\\\\u505c\\\\u6548\\\\u679c *\\\\/\\\\r\\\\n        .brand-logo-link:hover {\\\\r\\\\n            opacity: 0.75; \\\\/* \\\\u5bf9\\\\u5e94 hover:opacity-75 *\\\\/\\\\r\\\\n        }\\\\r\\\\n\\\\r\\\\n        \\\\/* \\\\u56fe\\\\u7247\\\\u6837\\\\u5f0f *\\\\/\\\\r\\\\n        .brand-logo {\\\\r\\\\n            object-fit: contain; \\\\/* \\\\u5bf9\\\\u5e94 object-contain *\\\\/\\\\r\\\\n        }\\\\r\\\\n\\\\r\\\\n        \\\\/* \\\\u4e0d\\\\u540cLogo\\\\u7684\\\\u9ad8\\\\u5ea6 *\\\\/\\\\r\\\\n        .logo-apple, .logo-motorola, .logo-huawei { height: 2.5rem; } \\\\/* h-10 *\\\\/\\\\r\\\\n        .logo-samsung, .logo-oppo { height: 2rem; } \\\\/* h-8 *\\\\/\\\\r\\\\n        .logo-xiaomi { height: 2.25rem; } \\\\/* h-9 *\\\\/\\\\r\\\\n\\\\r\\\\n        \\\\/* * \\\\u54cd\\\\u5e94\\\\u5f0f\\\\u8bbe\\\\u8ba1: \\\\u5a92\\\\u4f53\\\\u67e5\\\\u8be2\\\\r\\\\n         *\\\\/\\\\r\\\\n\\\\r\\\\n        \\\\/* \\\\u4e2d\\\\u7b49\\\\u5c4f\\\\u5e55 (640px\\\\u53ca\\\\u4ee5\\\\u4e0a), \\\\u5bf9\\\\u5e94 sm: \\\\u524d\\\\u7f00 *\\\\/\\\\r\\\\n        @media (min-width: 640px) {\\\\r\\\\n            .shop-by-brand-section {\\\\r\\\\n                padding-top: 4rem; \\\\/* \\\\u5bf9\\\\u5e94 sm:py-16 *\\\\/\\\\r\\\\n                padding-bottom: 4rem;\\\\r\\\\n            }\\\\r\\\\n            .brand-grid {\\\\r\\\\n                max-width: 36rem; \\\\/* \\\\u5bf9\\\\u5e94 sm:max-w-xl *\\\\/\\\\r\\\\n                grid-template-columns: repeat(3, 1fr); \\\\/* \\\\u5bf9\\\\u5e94 sm:grid-cols-3 *\\\\/\\\\r\\\\n                column-gap: 2.5rem; \\\\/* \\\\u5bf9\\\\u5e94 sm:gap-x-10 *\\\\/\\\\r\\\\n            }\\\\r\\\\n        }\\\\r\\\\n\\\\r\\\\n        \\\\/* \\\\u5927\\\\u5c4f\\\\u5e55 (1024px\\\\u53ca\\\\u4ee5\\\\u4e0a), \\\\u5bf9\\\\u5e94 lg: \\\\u524d\\\\u7f00 *\\\\/\\\\r\\\\n        @media (min-width: 1024px) {\\\\r\\\\n            .container {\\\\r\\\\n                padding-left: 2rem; \\\\/* \\\\u5bf9\\\\u5e94 lg:px-8 *\\\\/\\\\r\\\\n                padding-right: 2rem;\\\\r\\\\n            }\\\\r\\\\n            .brand-grid {\\\\r\\\\n                max-width: none; \\\\/* \\\\u5bf9\\\\u5e94 lg:max-w-none *\\\\/\\\\r\\\\n                margin-left: 0;  \\\\/* \\\\u5bf9\\\\u5e94 lg:mx-0 *\\\\/\\\\r\\\\n                margin-right: 0;\\\\r\\\\n                grid-template-columns: repeat(6, 1fr); \\\\/* \\\\u5bf9\\\\u5e94 lg:grid-cols-6 *\\\\/\\\\r\\\\n            }\\\\r\\\\n        }\\\"}' where `id` = 181", "type": "query", "params": [], "bindings": ["{\"html\":\"<section class=\\\"shop-by-brand-section\\\">\\r\\n        <div class=\\\"container\\\">\\r\\n            <h2 class=\\\"section-title\\\">\\r\\n                SHOP BY BRAND\\r\\n            <\\/h2>\\r\\n\\r\\n            <div class=\\\"brand-grid\\\">\\r\\n                <a href=\\\"#\\\" class=\\\"brand-logo-link\\\">\\r\\n                    <img src=\\\"\\/storage\\/theme\\/42\\/wG6bHeSQVQVxYO67SU6nS1K3eQXLaRVdHMLpreCC.webp\\\" alt=\\\"Apple\\\" class=\\\"brand-logo logo-apple\\\">\\r\\n                <\\/a>\\r\\n                <a href=\\\"#\\\" class=\\\"brand-logo-link\\\">\\r\\n                    <img src=\\\"\\/storage\\/theme\\/42\\/lEBIAJiOip7tcMCDZn6dXngX4ieMbpqct900OgYp.webp\\\" alt=\\\"Samsung\\\" class=\\\"brand-logo logo-samsung\\\">\\r\\n                <\\/a>\\r\\n                <a href=\\\"#\\\" class=\\\"brand-logo-link\\\">\\r\\n                    <img src=\\\"\\/storage\\/theme\\/42\\/ZlhWdHlUNqBbsr9ap91NemQT8Vaas46AiwVEbOYF.webp\\\" alt=\\\"Motorola\\\" class=\\\"brand-logo logo-motorola\\\">\\r\\n                <\\/a>\\r\\n                <a href=\\\"#\\\" class=\\\"brand-logo-link\\\">\\r\\n                    <img src=\\\"\\/storage\\/theme\\/42\\/mKjaC7o96snLqlvrNiWUhre6ToYNnCrRQ5iJAAkn.webp\\\" alt=\\\"Huawei\\\" class=\\\"brand-logo logo-huawei\\\">\\r\\n                <\\/a>\\r\\n                <a href=\\\"#\\\" class=\\\"brand-logo-link\\\">\\r\\n                    <img src=\\\"\\/storage\\/theme\\/42\\/aouSOFFs541Cv4Bu7YKER4I418y8GkVpA7jr9owz.webp\\\" alt=\\\"Xiaomi\\\" class=\\\"brand-logo logo-xiaomi\\\">\\r\\n                <\\/a>\\r\\n                <a href=\\\"#\\\" class=\\\"brand-logo-link\\\">\\r\\n                    <img src=\\\"\\/storage\\/theme\\/42\\/8EKdYRB1UZgov1PO1rbI9Jrj2hZR5xEn8XkShRCo.webp\\\" alt=\\\"Oppo\\\" class=\\\"brand-logo logo-oppo\\\">\\r\\n                <\\/a>\\r\\n            <\\/div>\\r\\n        <\\/div>\\r\\n    <\\/section>\",\"css\":\"\\/* \\u533a\\u57df\\u5bb9\\u5668\\u6837\\u5f0f *\\/\\r\\n        .shop-by-brand-section {\\r\\n            background-color: #ffffff;\\r\\n            padding-top: 3rem; \\/* \\u5bf9\\u5e94 py-12 *\\/\\r\\n            padding-bottom: 3rem; \\/* \\u5bf9\\u5e94 py-12 *\\/\\r\\n        }\\r\\n\\r\\n        \\/* \\u5185\\u5bb9\\u4e3b\\u5bb9\\u5668 *\\/\\r\\n        .container {\\r\\n            max-width: 80rem; \\/* \\u5bf9\\u5e94 max-w-7xl *\\/\\r\\n            margin-left: auto;\\r\\n            margin-right: auto;\\r\\n            padding-left: 1.5rem; \\/* \\u5bf9\\u5e94 px-6 *\\/\\r\\n            padding-right: 1.5rem; \\/* \\u5bf9\\u5e94 px-6 *\\/\\r\\n        }\\r\\n\\r\\n        \\/* \\u6807\\u9898\\u6837\\u5f0f *\\/\\r\\n        .section-title {\\r\\n            text-align: center; \\/* \\u5bf9\\u5e94 text-center *\\/\\r\\n            font-size: 1.25rem; \\/* \\u5bf9\\u5e94 text-xl *\\/\\r\\n            font-weight: 700;   \\/* \\u5bf9\\u5e94 font-bold *\\/\\r\\n            letter-spacing: 0.1em; \\/* \\u5bf9\\u5e94 tracking-widest *\\/\\r\\n            color: rgb(31 41 55);  \\/* \\u5bf9\\u5e94 text-gray-800 *\\/\\r\\n        }\\r\\n\\r\\n        \\/* \\u54c1\\u724cLogo\\u7f51\\u683c\\u5e03\\u5c40 *\\/\\r\\n        .brand-grid {\\r\\n            margin-top: 2.5rem; \\/* \\u5bf9\\u5e94 mt-10 *\\/\\r\\n            display: grid;\\r\\n            grid-template-columns: repeat(2, 1fr); \\/* \\u5bf9\\u5e94 grid-cols-2 *\\/\\r\\n            align-items: center; \\/* \\u5bf9\\u5e94 items-center *\\/\\r\\n            column-gap: 2rem; \\/* \\u5bf9\\u5e94 gap-x-8 *\\/\\r\\n            row-gap: 2.5rem;  \\/* \\u5bf9\\u5e94 gap-y-10 *\\/\\r\\n            max-width: 32rem; \\/* \\u5bf9\\u5e94 max-w-lg *\\/\\r\\n            margin-left: auto;\\r\\n            margin-right: auto;\\r\\n        }\\r\\n\\r\\n        \\/* Logo\\u94fe\\u63a5\\u6837\\u5f0f *\\/\\r\\n        .brand-logo-link {\\r\\n            display: flex;\\r\\n            justify-content: center; \\/* \\u5bf9\\u5e94 justify-center *\\/\\r\\n            padding: 1rem; \\/* \\u5bf9\\u5e94 p-4 *\\/\\r\\n            transition: opacity 0.15s ease-in-out; \\/* \\u5bf9\\u5e94 transition *\\/\\r\\n        }\\r\\n\\r\\n        \\/* \\u9f20\\u6807\\u60ac\\u505c\\u6548\\u679c *\\/\\r\\n        .brand-logo-link:hover {\\r\\n            opacity: 0.75; \\/* \\u5bf9\\u5e94 hover:opacity-75 *\\/\\r\\n        }\\r\\n\\r\\n        \\/* \\u56fe\\u7247\\u6837\\u5f0f *\\/\\r\\n        .brand-logo {\\r\\n            object-fit: contain; \\/* \\u5bf9\\u5e94 object-contain *\\/\\r\\n        }\\r\\n\\r\\n        \\/* \\u4e0d\\u540cLogo\\u7684\\u9ad8\\u5ea6 *\\/\\r\\n        .logo-apple, .logo-motorola, .logo-huawei { height: 2.5rem; } \\/* h-10 *\\/\\r\\n        .logo-samsung, .logo-oppo { height: 2rem; } \\/* h-8 *\\/\\r\\n        .logo-xiaomi { height: 2.25rem; } \\/* h-9 *\\/\\r\\n\\r\\n        \\/* * \\u54cd\\u5e94\\u5f0f\\u8bbe\\u8ba1: \\u5a92\\u4f53\\u67e5\\u8be2\\r\\n         *\\/\\r\\n\\r\\n        \\/* \\u4e2d\\u7b49\\u5c4f\\u5e55 (640px\\u53ca\\u4ee5\\u4e0a), \\u5bf9\\u5e94 sm: \\u524d\\u7f00 *\\/\\r\\n        @media (min-width: 640px) {\\r\\n            .shop-by-brand-section {\\r\\n                padding-top: 4rem; \\/* \\u5bf9\\u5e94 sm:py-16 *\\/\\r\\n                padding-bottom: 4rem;\\r\\n            }\\r\\n            .brand-grid {\\r\\n                max-width: 36rem; \\/* \\u5bf9\\u5e94 sm:max-w-xl *\\/\\r\\n                grid-template-columns: repeat(3, 1fr); \\/* \\u5bf9\\u5e94 sm:grid-cols-3 *\\/\\r\\n                column-gap: 2.5rem; \\/* \\u5bf9\\u5e94 sm:gap-x-10 *\\/\\r\\n            }\\r\\n        }\\r\\n\\r\\n        \\/* \\u5927\\u5c4f\\u5e55 (1024px\\u53ca\\u4ee5\\u4e0a), \\u5bf9\\u5e94 lg: \\u524d\\u7f00 *\\/\\r\\n        @media (min-width: 1024px) {\\r\\n            .container {\\r\\n                padding-left: 2rem; \\/* \\u5bf9\\u5e94 lg:px-8 *\\/\\r\\n                padding-right: 2rem;\\r\\n            }\\r\\n            .brand-grid {\\r\\n                max-width: none; \\/* \\u5bf9\\u5e94 lg:max-w-none *\\/\\r\\n                margin-left: 0;  \\/* \\u5bf9\\u5e94 lg:mx-0 *\\/\\r\\n                margin-right: 0;\\r\\n                grid-template-columns: repeat(6, 1fr); \\/* \\u5bf9\\u5e94 lg:grid-cols-6 *\\/\\r\\n            }\\r\\n        }\"}", 181], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 387}, {"index": 15, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 36}, {"index": 22, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 700}, {"index": 23, "namespace": null, "name": "packages/Webkul/Theme/src/Repositories/ThemeCustomizationRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Theme\\src\\Repositories\\ThemeCustomizationRepository.php", "line": 42}, {"index": 24, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Settings/ThemeController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Settings\\ThemeController.php", "line": 117}], "start": **********.851708, "duration": 0.00453, "duration_str": "4.53ms", "memory": 0, "memory_str": null, "filename": "Translatable.php:387", "source": {"index": 14, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 387}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=387", "ajax": false, "filename": "Translatable.php", "line": "387"}, "connection": "mlk", "explain": null, "start_percent": 58.627, "width_percent": 21.895}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "packages/Webkul/Theme/src/Repositories/ThemeCustomizationRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Theme\\src\\Repositories\\ThemeCustomizationRepository.php", "line": 149}, {"index": 22, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Settings/ThemeController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Settings\\ThemeController.php", "line": 120}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.86171, "duration": 0.00214, "duration_str": "2.14ms", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 80.522, "width_percent": 10.343}, {"sql": "select * from `currencies` where `currencies`.`id` = 2 limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 22, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Core.php", "line": 390}, {"index": 23, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Core.php", "line": 433}, {"index": 24, "namespace": null, "name": "packages/Webkul/FPC/src/Hasher/DefaultHasher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\FPC\\src\\Hasher\\DefaultHasher.php", "line": 29}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-responsecache/src/Hasher/DefaultHasher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\spatie\\laravel-responsecache\\src\\Hasher\\DefaultHasher.php", "line": 18}], "start": **********.8698092, "duration": 0.00189, "duration_str": "1.89ms", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 20, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 90.865, "width_percent": 9.135}]}, "models": {"data": {"Webkul\\Theme\\Models\\ThemeCustomizationTranslation": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FTheme%2Fsrc%2FModels%2FThemeCustomizationTranslation.php&line=1", "ajax": false, "filename": "ThemeCustomizationTranslation.php", "line": "?"}}, "Webkul\\Core\\Models\\Locale": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FModels%2FLocale.php&line=1", "ajax": false, "filename": "Locale.php", "line": "?"}}, "Webkul\\Core\\Models\\Channel": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FModels%2FChannel.php&line=1", "ajax": false, "filename": "Channel.php", "line": "?"}}, "Webkul\\User\\Models\\Admin": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FUser%2Fsrc%2FModels%2FAdmin.php&line=1", "ajax": false, "filename": "Admin.php", "line": "?"}}, "Webkul\\User\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FUser%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "Webkul\\Theme\\Models\\ThemeCustomization": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FTheme%2Fsrc%2FModels%2FThemeCustomization.php&line=1", "ajax": false, "filename": "ThemeCustomization.php", "line": "?"}}, "Webkul\\Core\\Models\\Currency": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FModels%2FCurrency.php&line=1", "ajax": false, "filename": "Currency.php", "line": "?"}}}, "count": 15, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "302 Found", "full_url": "http://mlk.test/admin/settings/themes/edit/42", "action_name": "admin.settings.themes.update", "controller_action": "Webkul\\Admin\\Http\\Controllers\\Settings\\ThemeController@update", "uri": "POST admin/settings/themes/edit/{id}", "controller": "Webkul\\Admin\\Http\\Controllers\\Settings\\ThemeController@update<a href=\"phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FHttp%2FControllers%2FSettings%2FThemeController.php&line=90\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "admin/settings/themes", "file": "<a href=\"phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FHttp%2FControllers%2FSettings%2FThemeController.php&line=90\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">packages/Webkul/Admin/src/Http/Controllers/Settings/ThemeController.php:90-127</a>", "middleware": "web, admin_locale, Webkul\\Core\\Http\\Middleware\\PreventRequestsDuringMaintenance, admin, Webkul\\Core\\Http\\Middleware\\NoCacheMiddleware", "duration": "343ms", "peak_memory": "42MB", "response": "Redirect to http://mlk.test/admin/settings/themes", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-862189629 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-862189629\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1034416459 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">RL8eYWcLOX1nKpLrVKRAtWfTglgDAXuESTadrPQu</span>\"\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>en</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>options</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>html</span>\" => \"\"\"\n        <span class=sf-dump-str title=\"1556 characters\">&lt;section class=&quot;shop-by-brand-section&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"1556 characters\">        &lt;div class=&quot;container&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"1556 characters\">            &lt;h2 class=&quot;section-title&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"1556 characters\">                SHOP BY BRAND<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"1556 characters\">            &lt;/h2&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"1556 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"1556 characters\">            &lt;div class=&quot;brand-grid&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"1556 characters\">                &lt;a href=&quot;#&quot; class=&quot;brand-logo-link&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"1556 characters\">                    &lt;img src=&quot;/storage/theme/42/wG6bHeSQVQVxYO67SU6nS1K3eQXLaRVdHMLpreCC.webp&quot; alt=&quot;Apple&quot; class=&quot;brand-logo logo-apple&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"1556 characters\">                &lt;/a&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"1556 characters\">                &lt;a href=&quot;#&quot; class=&quot;brand-logo-link&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"1556 characters\">                    &lt;img src=&quot;/storage/theme/42/lEBIAJiOip7tcMCDZn6dXngX4ieMbpqct900OgYp.webp&quot; alt=&quot;Samsung&quot; class=&quot;brand-logo logo-samsung&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"1556 characters\">                &lt;/a&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"1556 characters\">                &lt;a href=&quot;#&quot; class=&quot;brand-logo-link&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"1556 characters\">                    &lt;img src=&quot;/storage/theme/42/ZlhWdHlUNqBbsr9ap91NemQT8Vaas46AiwVEbOYF.webp&quot; alt=&quot;Motorola&quot; class=&quot;brand-logo logo-motorola&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"1556 characters\">                &lt;/a&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"1556 characters\">                &lt;a href=&quot;#&quot; class=&quot;brand-logo-link&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"1556 characters\">                    &lt;img src=&quot;/storage/theme/42/mKjaC7o96snLqlvrNiWUhre6ToYNnCrRQ5iJAAkn.webp&quot; alt=&quot;Huawei&quot; class=&quot;brand-logo logo-huawei&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"1556 characters\">                &lt;/a&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"1556 characters\">                &lt;a href=&quot;#&quot; class=&quot;brand-logo-link&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"1556 characters\">                    &lt;img src=&quot;/storage/theme/42/aouSOFFs541Cv4Bu7YKER4I418y8GkVpA7jr9owz.webp&quot; alt=&quot;Xiaomi&quot; class=&quot;brand-logo logo-xiaomi&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"1556 characters\">                &lt;/a&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"1556 characters\">                &lt;a href=&quot;#&quot; class=&quot;brand-logo-link&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"1556 characters\">                    &lt;img src=&quot;/storage/theme/42/8EKdYRB1UZgov1PO1rbI9Jrj2hZR5xEn8XkShRCo.webp&quot; alt=&quot;Oppo&quot; class=&quot;brand-logo logo-oppo&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"1556 characters\">                &lt;/a&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"1556 characters\">            &lt;/div&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"1556 characters\">        &lt;/div&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"1556 characters\">    &lt;/section&gt;</span>\n        \"\"\"\n      \"<span class=sf-dump-key>css</span>\" => \"\"\"\n        <span class=sf-dump-str title=\"3012 characters\">/* &#21306;&#22495;&#23481;&#22120;&#26679;&#24335; */<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3012 characters\">        .shop-by-brand-section {<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3012 characters\">            background-color: #ffffff;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3012 characters\">            padding-top: 3rem; /* &#23545;&#24212; py-12 */<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3012 characters\">            padding-bottom: 3rem; /* &#23545;&#24212; py-12 */<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3012 characters\">        }<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3012 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3012 characters\">        /* &#20869;&#23481;&#20027;&#23481;&#22120; */<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3012 characters\">        .container {<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3012 characters\">            max-width: 80rem; /* &#23545;&#24212; max-w-7xl */<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3012 characters\">            margin-left: auto;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3012 characters\">            margin-right: auto;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3012 characters\">            padding-left: 1.5rem; /* &#23545;&#24212; px-6 */<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3012 characters\">            padding-right: 1.5rem; /* &#23545;&#24212; px-6 */<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3012 characters\">        }<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3012 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3012 characters\">        /* &#26631;&#39064;&#26679;&#24335; */<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3012 characters\">        .section-title {<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3012 characters\">            text-align: center; /* &#23545;&#24212; text-center */<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3012 characters\">            font-size: 1.25rem; /* &#23545;&#24212; text-xl */<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3012 characters\">            font-weight: 700;   /* &#23545;&#24212; font-bold */<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3012 characters\">            letter-spacing: 0.1em; /* &#23545;&#24212; tracking-widest */<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3012 characters\">            color: rgb(31 41 55);  /* &#23545;&#24212; text-gray-800 */<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3012 characters\">        }<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3012 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3012 characters\">        /* &#21697;&#29260;Logo&#32593;&#26684;&#24067;&#23616; */<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3012 characters\">        .brand-grid {<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3012 characters\">            margin-top: 2.5rem; /* &#23545;&#24212; mt-10 */<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3012 characters\">            display: grid;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3012 characters\">            grid-template-columns: repeat(2, 1fr); /* &#23545;&#24212; grid-cols-2 */<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3012 characters\">            align-items: center; /* &#23545;&#24212; items-center */<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3012 characters\">            column-gap: 2rem; /* &#23545;&#24212; gap-x-8 */<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3012 characters\">            row-gap: 2.5rem;  /* &#23545;&#24212; gap-y-10 */<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3012 characters\">            max-width: 32rem; /* &#23545;&#24212; max-w-lg */<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3012 characters\">            margin-left: auto;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3012 characters\">            margin-right: auto;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3012 characters\">        }<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3012 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3012 characters\">        /* Logo&#38142;&#25509;&#26679;&#24335; */<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3012 characters\">        .brand-logo-link {<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3012 characters\">            display: flex;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3012 characters\">            justify-content: center; /* &#23545;&#24212; justify-center */<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3012 characters\">            padding: 1rem; /* &#23545;&#24212; p-4 */<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3012 characters\">            transition: opacity 0.15s ease-in-out; /* &#23545;&#24212; transition */<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3012 characters\">        }<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3012 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3012 characters\">        /* &#40736;&#26631;&#24748;&#20572;&#25928;&#26524; */<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3012 characters\">        .brand-logo-link:hover {<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3012 characters\">            opacity: 0.75; /* &#23545;&#24212; hover:opacity-75 */<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3012 characters\">        }<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3012 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3012 characters\">        /* &#22270;&#29255;&#26679;&#24335; */<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3012 characters\">        .brand-logo {<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3012 characters\">            object-fit: contain; /* &#23545;&#24212; object-contain */<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3012 characters\">        }<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3012 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3012 characters\">        /* &#19981;&#21516;Logo&#30340;&#39640;&#24230; */<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3012 characters\">        .logo-apple, .logo-motorola, .logo-huawei { height: 2.5rem; } /* h-10 */<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3012 characters\">        .logo-samsung, .logo-oppo { height: 2rem; } /* h-8 */<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3012 characters\">        .logo-xiaomi { height: 2.25rem; } /* h-9 */<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3012 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3012 characters\">        /* * &#21709;&#24212;&#24335;&#35774;&#35745;: &#23186;&#20307;&#26597;&#35810;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3012 characters\">         */<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3012 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3012 characters\">        /* &#20013;&#31561;&#23631;&#24149; (640px&#21450;&#20197;&#19978;), &#23545;&#24212; sm: &#21069;&#32512; */<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3012 characters\">        @media (min-width: 640px) {<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3012 characters\">            .shop-by-brand-section {<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3012 characters\">                padding-top: 4rem; /* &#23545;&#24212; sm:py-16 */<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3012 characters\">                padding-bottom: 4rem;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3012 characters\">            }<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3012 characters\">            .brand-grid {<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3012 characters\">                max-width: 36rem; /* &#23545;&#24212; sm:max-w-xl */<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3012 characters\">                grid-template-columns: repeat(3, 1fr); /* &#23545;&#24212; sm:grid-cols-3 */<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3012 characters\">                column-gap: 2.5rem; /* &#23545;&#24212; sm:gap-x-10 */<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3012 characters\">            }<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3012 characters\">        }<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3012 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3012 characters\">        /* &#22823;&#23631;&#24149; (1024px&#21450;&#20197;&#19978;), &#23545;&#24212; lg: &#21069;&#32512; */<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3012 characters\">        @media (min-width: 1024px) {<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3012 characters\">            .container {<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3012 characters\">                padding-left: 2rem; /* &#23545;&#24212; lg:px-8 */<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3012 characters\">                padding-right: 2rem;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3012 characters\">            }<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3012 characters\">            .brand-grid {<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3012 characters\">                max-width: none; /* &#23545;&#24212; lg:max-w-none */<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3012 characters\">                margin-left: 0;  /* &#23545;&#24212; lg:mx-0 */<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3012 characters\">                margin-right: 0;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3012 characters\">                grid-template-columns: repeat(6, 1fr); /* &#23545;&#24212; lg:grid-cols-6 */<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3012 characters\">            }<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3012 characters\">        }</span>\n        \"\"\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"14 characters\">static_content</span>\"\n  \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"5 characters\">brand</span>\"\n  \"<span class=sf-dump-key>sort_order</span>\" => \"<span class=sf-dump-str>5</span>\"\n  \"<span class=sf-dump-key>channel_id</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>theme_code</span>\" => \"<span class=sf-dump-str title=\"7 characters\">default</span>\"\n  \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"2 characters\">on</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1034416459\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-387339911 data-indent-pad=\"  \"><span class=sf-dump-note>array:13</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1018 characters\">admin_locale=eyJpdiI6InlVTlI4cytSTEhwU1oxNXhFNmszTHc9PSIsInZhbHVlIjoiL1djczhIanIvN1ZXTlhGdmpwMWJhNkpqMnNiMHcxMjBic21GQ0ltRzRZV25DczdBcXZaVUNJWlFEakx1bjV6byIsIm1hYyI6IjhmNjlhYzdkYWNjMTVkNjA5ZThmMzlhYmViM2UwYTFiZDQxZDY4ZjEwZTg3NGVjZGNhOTAzODA3ODhhNjllMzQiLCJ0YWciOiIifQ%3D%3D; sidebar_collapsed=0; dark_mode=0; XSRF-TOKEN=eyJpdiI6Ijg2ODgrRWgxRWN4YVpPMmN4WHpOZ0E9PSIsInZhbHVlIjoiOXBSY0NMSlVRaWdtZCtBelkvbXZIRCtYbEltcjFLUzRNUjg4am9tUHltOTFyTjVKem9zZDNtQnJrd0ZmTkt6Y2EyakZVOVZraHlEZk9LRUVlMkVUTElyL3ZvZy9LSE4rb3ZPaHlVU21mbUFOY0JyUTlQWTc2bkkxZjBaelg2c1YiLCJtYWMiOiJhYWM5NDA3OTQ5MTg0NzUwNGYzNTIzYzhhMzgzNDQ2MTgwNTY3OTQzZWFmZGU1ZjE5YTE5MWUwZDMzMDA3OTk5IiwidGFnIjoiIn0%3D; mlk_session=eyJpdiI6IlF3MEsyRDNwQTJjQ1lNSUxWd3JqMHc9PSIsInZhbHVlIjoiK2l2Qng3K1o4ZlhDMzZCKzdHS0RwODdsZnMyNTgwMHAyQ1lJc0ZaaHd1ZCt5UXR4SmdjNWh6N2ZNSUkrNVJlQmhOZlNFbEVRdzR1WmRqckVRcXB6dEt1SjU4REdQNlo0MGprZFZIS1F2NmloWFloTktOd05ZaGpVcERmQzB2blIiLCJtYWMiOiJlMTE4ZDEwYzM1ZWY1OGExZTI5ZGIwN2IwZWU3NzBkOWJlNGRkYWY3MzNhNmNhZDgyNmRjYTUwZDcwMDk2MTJmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">zh-CN,zh;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"71 characters\">http://mlk.test/admin/settings/themes/edit/42?channel=default&amp;locale=en</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">multipart/form-data; boundary=----WebKitFormBoundary2T3AdtBulXrCQFa8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"15 characters\">http://mlk.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">6063</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">mlk.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-387339911\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1601513337 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>admin_locale</span>\" => \"<span class=sf-dump-str title=\"5 characters\">zh_CN</span>\"\n  \"<span class=sf-dump-key>sidebar_collapsed</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>dark_mode</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">RL8eYWcLOX1nKpLrVKRAtWfTglgDAXuESTadrPQu</span>\"\n  \"<span class=sf-dump-key>mlk_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Zao9ptAmXhjVm7WD2j4izotzmKLg3wYf4OgogoiC</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1601513337\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-696347385 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 07 Aug 2025 15:34:33 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://mlk.test/admin/settings/themes</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>0</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-696347385\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-582207769 data-indent-pad=\"  \"><span class=sf-dump-note>array:10</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">RL8eYWcLOX1nKpLrVKRAtWfTglgDAXuESTadrPQu</span>\"\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">it</span>\"\n  \"<span class=sf-dump-key>currency</span>\" => \"<span class=sf-dump-str title=\"3 characters\">EUR</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"71 characters\">http://mlk.test/admin/settings/themes/edit/42?channel=default&amp;locale=en</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>admin_locale</span>\" => \"<span class=sf-dump-str title=\"5 characters\">zh_CN</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>success</span>\" => \"<span class=sf-dump-str title=\"6 characters\">&#25104;&#21151;&#26356;&#26032;&#20027;&#39064;</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-582207769\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "302 Found", "full_url": "http://mlk.test/admin/settings/themes/edit/42", "action_name": "admin.settings.themes.update", "controller_action": "Webkul\\Admin\\Http\\Controllers\\Settings\\ThemeController@update"}, "badge": "302 Found"}}