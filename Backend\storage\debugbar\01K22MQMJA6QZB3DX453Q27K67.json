{"__meta": {"id": "01K22MQMJA6QZB3DX453Q27K67", "datetime": "2025-08-07 17:09:55", "utime": **********.530451, "method": "POST", "uri": "/admin/settings/themes/edit/46", "ip": "127.0.0.1"}, "modules": {"count": 3, "modules": [{"name": "Webkul\\Core", "models": ["Webkul\\Core\\Models\\Channel (1)", "Webkul\\Core\\Models\\Locale (5)", "Webkul\\Core\\Models\\Currency (1)"], "views": [], "queries": [{"sql": "select * from `channels` where `hostname` in ('mlk.test', 'http://mlk.test', 'https://mlk.test')", "duration": 0.49, "duration_str": "490ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.42, "duration_str": "420ms", "connection": "mlk"}, {"sql": "select * from `currencies` where `currencies`.`id` = 2 limit 1", "duration": 0.26, "duration_str": "260ms", "connection": "mlk"}]}, {"name": "Webkul\\Theme", "models": ["Webkul\\Theme\\Models\\ThemeCustomization (1)", "Webkul\\Theme\\Models\\ThemeCustomizationTranslation (5)"], "views": [], "queries": [{"sql": "select * from `theme_customizations` where `theme_customizations`.`id` = 46 limit 1", "duration": 0.24, "duration_str": "240ms", "connection": "mlk"}, {"sql": "select * from `theme_customization_translations` where `theme_customization_translations`.`theme_customization_id` in (46)", "duration": 0.33, "duration_str": "330ms", "connection": "mlk"}, {"sql": "update `theme_customizations` set `status` = 1, `theme_customizations`.`updated_at` = '2025-08-07 17:09:55' where `id` = 46", "duration": 3.26, "duration_str": "3.26s", "connection": "mlk"}, {"sql": "update `theme_customization_translations` set `options` = '{\\\"html\\\":\\\"<div class=\\\\\"carousel\\\\\">\\r\\n        <div class=\\\\\"carousel-track\\\\\">\\r\\n            <div class=\\\\\"slide current-slide\\\\\" style=\\\\\"background-image: url(\\'https:\\/\\/images.unsplash.com\\/photo-1506744038136-46273834b3fb?q=80&w=2070&auto=format&fit=crop\\');\\\\\">\\r\\n                <div class=\\\\\"hero-text\\\\\">\\r\\n                    <h1>Like gentle sunshine<\\/h1>\\r\\n                    <p>Discover our eco-friendly collection, crafted with natural materials that feel as gentle as a ray of sunshine on a quiet morning.<\\/p>\\r\\n                <\\/div>\\r\\n            <\\/div>\\r\\n\\r\\n            <div class=\\\\\"slide\\\\\" style=\\\\\"background-image: url(\\'https:\\/\\/images.unsplash.com\\/photo-1470770841072-f978cf4d019e?q=80&w=2070&auto=format&fit=crop\\');\\\\\">\\r\\n                <div class=\\\\\"hero-text\\\\\">\\r\\n                    <h1>Whispers of the Forest<\\/h1>\\r\\n                    <p>Journey through our latest designs, inspired by the deep greens and untold stories whispered through the ancient forest canopy.<\\/p>\\r\\n                <\\/div>\\r\\n            <\\/div>\\r\\n\\r\\n            <div class=\\\\\"slide\\\\\" style=\\\\\"background-image: url(\\'https:\\/\\/images.unsplash.com\\/photo-1439405326853-58f2724f4d31?q=80&w=2070&auto=format&fit=crop\\');\\\\\">\\r\\n                <div class=\\\\\"hero-text\\\\\">\\r\\n                    <h1>Echoes of the Ocean<\\/h1>\\r\\n                    <p>Embrace the calm and power of the sea with our new line, featuring serene colors and fluid designs that capture the rhythm of the waves.<\\/p>\\r\\n                <\\/div>\\r\\n            <\\/div>\\r\\n        <\\/div>\\r\\n\\r\\n        <button class=\\\\\"arrow left-arrow\\\\\">&#10094;<\\/button>\\r\\n        <button class=\\\\\"arrow right-arrow\\\\\">&#10095;<\\/button>\\r\\n        \\r\\n        <div class=\\\\\"carousel-dots\\\\\">\\r\\n        <\\/div>\\r\\n    <\\/div>\\\",\\\"css\\\":\\\".carousel {\\r\\n    margin-top:14rem;\\r\\n    position: relative;\\r\\n    width: 100%;\\r\\n    height: 60vh;\\r\\n    min-height: 420px;\\r\\n    overflow: hidden;\\r\\n    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);\\r\\n}\\r\\n.carousel-track {\\r\\n    position: relative;\\r\\n    height: 100%;\\r\\n    display: flex;\\r\\n    transition: transform 0.7s cubic-bezier(0.65, 0, 0.35, 1);\\r\\n}\\r\\n.slide {\\r\\n    position: relative;\\r\\n    flex: 1 0 100%;\\r\\n    width: 100%;\\r\\n    height: 100%;\\r\\n    background-size: cover;\\r\\n    background-position: center;\\r\\n    display: flex;\\r\\n    justify-content: center;\\r\\n    align-items: center;\\r\\n    color: white;\\r\\n}\\r\\n.slide::before {\\r\\n    content:\\'\\';\\r\\n    position: absolute;\\r\\n    top: 0;\\r\\n    left: 0;\\r\\n    width: 100%;\\r\\n    height: 100%;\\r\\n    background-color: rgba(0, 0, 0, 0.25);\\r\\n    z-index: 1;\\r\\n}\\r\\n.hero-text {\\r\\n    position: relative;\\r\\n    z-index: 2;\\r\\n    text-align: center;\\r\\n    max-width: 90%;\\r\\n    padding: 20px;\\r\\n    text-shadow: 0 2px 15px rgba(0, 0, 0, 0.4);\\r\\n    opacity: 0;\\r\\n    transform: translateY(25px);\\r\\n    transition: opacity 0.6s cubic-bezier(0.22, 1, 0.36, 1) 0.3s, transform 0.6s cubic-bezier(0.22, 1, 0.36, 1) 0.3s;\\r\\n}\\r\\n.current-slide .hero-text {\\r\\n    opacity: 1;\\r\\n    transform: translateY(0);\\r\\n}\\r\\n.hero-text h1 {\\r\\n    font-size: 3rem;\\r\\n    font-weight: 700;\\r\\n    margin-bottom: 0.75rem;\\r\\n    letter-spacing: 1px;\\r\\n}\\r\\n.hero-text p {\\r\\n    font-size: 1.2rem;\\r\\n    font-weight: 300;\\r\\n    max-width: 650px;\\r\\n    margin: 0 auto;\\r\\n    line-height: 1.6;\\r\\n}\\r\\n.arrow {\\r\\n    position: absolute;\\r\\n    top: 50%;\\r\\n    transform: translateY(-50%);\\r\\n    background: rgba(0, 0, 0, 0.2);\\r\\n    border: none;\\r\\n    color: rgba(255, 255, 255, 0.9);\\r\\n    font-size: 1.5rem;\\r\\n    padding: 10px;\\r\\n    width: 50px;\\r\\n    height: 50px;\\r\\n    border-radius: 4px;\\r\\n    cursor: pointer;\\r\\n    z-index: 10;\\r\\n    display: flex;\\r\\n    justify-content: center;\\r\\n    align-items: center;\\r\\n    transition: all 0.2s ease-in-out;\\r\\n}\\r\\n.arrow:hover {\\r\\n    background-color: rgba(0, 0, 0, 0.5);\\r\\n    transform: translateY(-50%) scale(1.05);\\r\\n}\\r\\n.left-arrow {\\r\\n    left: 30px;\\r\\n}\\r\\n.right-arrow {\\r\\n    right: 30px;\\r\\n}\\r\\n.carousel-dots {\\r\\n    position: absolute;\\r\\n    bottom: 25px;\\r\\n    left: 50%;\\r\\n    transform: translateX(-50%);\\r\\n    display: flex;\\r\\n    gap: 10px;\\r\\n    z-index: 10;\\r\\n}\\r\\n.dot {\\r\\n    border: none;\\r\\n    width: 25px;\\r\\n    height: 4px;\\r\\n    border-radius: 2px;\\r\\n    background-color: rgba(255, 255, 255, 0.4);\\r\\n    cursor: pointer;\\r\\n    transition: all 0.3s ease;\\r\\n}\\r\\n.dot.current-dot {\\r\\n    background-color: white;\\r\\n    width: 40px;\\r\\n}\\r\\n@media (max-width: 768px) {\\r\\n    .hero-text h1 {\\r\\n        font-size: 2rem;\\r\\n    }\\r\\n    .hero-text p {\\r\\n        font-size: 1rem;\\r\\n    }\\r\\n    .arrow {\\r\\n        display: none;\\r\\n    }\\r\\n}\\\"}' where `id` = 201", "duration": 2.16, "duration_str": "2.16s", "connection": "mlk"}]}, {"name": "Webkul\\User", "models": ["Webkul\\User\\Models\\Admin (1)", "Webkul\\User\\Models\\Role (1)"], "views": [], "queries": [{"sql": "select count(*) as aggregate from `admins`", "duration": 0.28, "duration_str": "280ms", "connection": "mlk"}, {"sql": "select count(*) as aggregate from `admins`", "duration": 0.17, "duration_str": "170ms", "connection": "mlk"}, {"sql": "select * from `admins` where `id` = 1 limit 1", "duration": 0.2, "duration_str": "200ms", "connection": "mlk"}, {"sql": "select * from `roles` where `roles`.`id` = 1 limit 1", "duration": 0.16, "duration_str": "160ms", "connection": "mlk"}]}]}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.270395, "end": **********.537255, "duration": 0.2668600082397461, "duration_str": "267ms", "measures": [{"label": "Booting", "start": **********.270395, "relative_start": 0, "end": **********.448822, "relative_end": **********.448822, "duration": 0.*****************, "duration_str": "178ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.448832, "relative_start": 0.****************, "end": **********.537257, "relative_end": 1.9073486328125e-06, "duration": 0.****************, "duration_str": "88.42ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.459119, "relative_start": 0.*****************, "end": **********.460993, "relative_end": **********.460993, "duration": 0.0018739700317382812, "duration_str": "1.87ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.52909, "relative_start": 0.****************, "end": **********.529316, "relative_end": **********.529316, "duration": 0.00022602081298828125, "duration_str": "226μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "39MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.2.0", "Environment": "local", "Debug Mode": "Enabled", "URL": "mlk.test", "Timezone": "Africa/Lagos", "Locale": "zh_CN"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 13, "nb_statements": 13, "nb_visible_statements": 13, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.00858, "accumulated_duration_str": "8.58ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select exists (select 1 from information_schema.tables where table_schema = 'mlk' and table_name = 'admins' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 32}, {"index": 14, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 44}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 1004}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 832}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 1078}], "start": **********.4708629, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "DatabaseManager.php:32", "source": {"index": 13, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 32}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FInstaller%2Fsrc%2FHelpers%2FDatabaseManager.php&line=32", "ajax": false, "filename": "DatabaseManager.php", "line": "32"}, "connection": "mlk", "explain": null, "start_percent": 0, "width_percent": 4.779}, {"sql": "select count(*) as aggregate from `admins`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 38}, {"index": 16, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 44}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 1004}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 832}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 1078}], "start": **********.4739802, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "DatabaseManager.php:38", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 38}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FInstaller%2Fsrc%2FHelpers%2FDatabaseManager.php&line=38", "ajax": false, "filename": "DatabaseManager.php", "line": "38"}, "connection": "mlk", "explain": null, "start_percent": 4.779, "width_percent": 3.263}, {"sql": "select * from `channels` where `hostname` in ('mlk.test', 'http://mlk.test', 'https://mlk.test')", "type": "query", "params": [], "bindings": ["mlk.test", "http://mlk.test", "https://mlk.test"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 578}, {"index": 16, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Core.php", "line": 143}, {"index": 17, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 127}, {"index": 18, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 45}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 1004}], "start": **********.479469, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:578", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 578}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=578", "ajax": false, "filename": "BaseRepository.php", "line": "578"}, "connection": "mlk", "explain": null, "start_percent": 8.042, "width_percent": 5.711}, {"sql": "select exists (select 1 from information_schema.tables where table_schema = 'mlk' and table_name = 'admins' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 32}, {"index": 14, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 59}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 16, "namespace": "middleware", "name": "admin_locale", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Middleware\\AdminLocale.php", "line": 46}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.481607, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "DatabaseManager.php:32", "source": {"index": 13, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 32}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FInstaller%2Fsrc%2FHelpers%2FDatabaseManager.php&line=32", "ajax": false, "filename": "DatabaseManager.php", "line": "32"}, "connection": "mlk", "explain": null, "start_percent": 13.753, "width_percent": 2.331}, {"sql": "select count(*) as aggregate from `admins`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 38}, {"index": 16, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 18, "namespace": "middleware", "name": "admin_locale", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Middleware\\AdminLocale.php", "line": 46}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.482568, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "DatabaseManager.php:38", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 38}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FInstaller%2Fsrc%2FHelpers%2FDatabaseManager.php&line=38", "ajax": false, "filename": "DatabaseManager.php", "line": "38"}, "connection": "mlk", "explain": null, "start_percent": 16.084, "width_percent": 1.981}, {"sql": "select * from `admins` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "admin", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\User\\src\\Http\\Middleware\\Bouncer.php", "line": 18}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.4856489, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "mlk", "explain": null, "start_percent": 18.065, "width_percent": 2.331}, {"sql": "select * from `roles` where `roles`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": "middleware", "name": "admin", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\User\\src\\Http\\Middleware\\Bouncer.php", "line": 54}, {"index": 22, "namespace": "middleware", "name": "admin", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\User\\src\\Http\\Middleware\\Bouncer.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 24, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 119}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.48767, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "admin:54", "source": {"index": 21, "namespace": "middleware", "name": "admin", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\User\\src\\Http\\Middleware\\Bouncer.php", "line": 54}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FUser%2Fsrc%2FHttp%2FMiddleware%2FBouncer.php&line=54", "ajax": false, "filename": "Bouncer.php", "line": "54"}, "connection": "mlk", "explain": null, "start_percent": 20.396, "width_percent": 1.865}, {"sql": "select * from `theme_customizations` where `theme_customizations`.`id` = 46 limit 1", "type": "query", "params": [], "bindings": [46], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, {"index": 21, "namespace": null, "name": "packages/Webkul/Theme/src/Repositories/ThemeCustomizationRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Theme\\src\\Repositories\\ThemeCustomizationRepository.php", "line": 42}, {"index": 22, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Settings/ThemeController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Settings\\ThemeController.php", "line": 117}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.491379, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:695", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=695", "ajax": false, "filename": "BaseRepository.php", "line": "695"}, "connection": "mlk", "explain": null, "start_percent": 22.261, "width_percent": 2.797}, {"sql": "select * from `theme_customization_translations` where `theme_customization_translations`.`theme_customization_id` in (46)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 25, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, {"index": 26, "namespace": null, "name": "packages/Webkul/Theme/src/Repositories/ThemeCustomizationRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Theme\\src\\Repositories\\ThemeCustomizationRepository.php", "line": 42}, {"index": 27, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Settings/ThemeController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Settings\\ThemeController.php", "line": 117}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.493379, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:695", "source": {"index": 25, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=695", "ajax": false, "filename": "BaseRepository.php", "line": "695"}, "connection": "mlk", "explain": null, "start_percent": 25.058, "width_percent": 3.846}, {"sql": "update `theme_customizations` set `status` = 1, `theme_customizations`.`updated_at` = '2025-08-07 17:09:55' where `id` = 46", "type": "query", "params": [], "bindings": [1, "2025-08-07 17:09:55", 46], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 700}, {"index": 15, "namespace": null, "name": "packages/Webkul/Theme/src/Repositories/ThemeCustomizationRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Theme\\src\\Repositories\\ThemeCustomizationRepository.php", "line": 42}, {"index": 16, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Settings/ThemeController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Settings\\ThemeController.php", "line": 117}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.5051382, "duration": 0.00326, "duration_str": "3.26ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:700", "source": {"index": 14, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 700}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=700", "ajax": false, "filename": "BaseRepository.php", "line": "700"}, "connection": "mlk", "explain": null, "start_percent": 28.904, "width_percent": 37.995}, {"sql": "update `theme_customization_translations` set `options` = '{\\\"html\\\":\\\"<div class=\\\\\\\"carousel\\\\\\\">\\\\r\\\\n        <div class=\\\\\\\"carousel-track\\\\\\\">\\\\r\\\\n            <div class=\\\\\\\"slide current-slide\\\\\\\" style=\\\\\\\"background-image: url(\\'https:\\\\/\\\\/images.unsplash.com\\\\/photo-1506744038136-46273834b3fb?q=80&w=2070&auto=format&fit=crop\\');\\\\\\\">\\\\r\\\\n                <div class=\\\\\\\"hero-text\\\\\\\">\\\\r\\\\n                    <h1>Like gentle sunshine<\\\\/h1>\\\\r\\\\n                    <p>Discover our eco-friendly collection, crafted with natural materials that feel as gentle as a ray of sunshine on a quiet morning.<\\\\/p>\\\\r\\\\n                <\\\\/div>\\\\r\\\\n            <\\\\/div>\\\\r\\\\n\\\\r\\\\n            <div class=\\\\\\\"slide\\\\\\\" style=\\\\\\\"background-image: url(\\'https:\\\\/\\\\/images.unsplash.com\\\\/photo-1470770841072-f978cf4d019e?q=80&w=2070&auto=format&fit=crop\\');\\\\\\\">\\\\r\\\\n                <div class=\\\\\\\"hero-text\\\\\\\">\\\\r\\\\n                    <h1>Whispers of the Forest<\\\\/h1>\\\\r\\\\n                    <p>Journey through our latest designs, inspired by the deep greens and untold stories whispered through the ancient forest canopy.<\\\\/p>\\\\r\\\\n                <\\\\/div>\\\\r\\\\n            <\\\\/div>\\\\r\\\\n\\\\r\\\\n            <div class=\\\\\\\"slide\\\\\\\" style=\\\\\\\"background-image: url(\\'https:\\\\/\\\\/images.unsplash.com\\\\/photo-1439405326853-58f2724f4d31?q=80&w=2070&auto=format&fit=crop\\');\\\\\\\">\\\\r\\\\n                <div class=\\\\\\\"hero-text\\\\\\\">\\\\r\\\\n                    <h1>Echoes of the Ocean<\\\\/h1>\\\\r\\\\n                    <p>Embrace the calm and power of the sea with our new line, featuring serene colors and fluid designs that capture the rhythm of the waves.<\\\\/p>\\\\r\\\\n                <\\\\/div>\\\\r\\\\n            <\\\\/div>\\\\r\\\\n        <\\\\/div>\\\\r\\\\n\\\\r\\\\n        <button class=\\\\\\\"arrow left-arrow\\\\\\\">&#10094;<\\\\/button>\\\\r\\\\n        <button class=\\\\\\\"arrow right-arrow\\\\\\\">&#10095;<\\\\/button>\\\\r\\\\n        \\\\r\\\\n        <div class=\\\\\\\"carousel-dots\\\\\\\">\\\\r\\\\n        <\\\\/div>\\\\r\\\\n    <\\\\/div>\\\",\\\"css\\\":\\\".carousel {\\\\r\\\\n    margin-top:14rem;\\\\r\\\\n    position: relative;\\\\r\\\\n    width: 100%;\\\\r\\\\n    height: 60vh;\\\\r\\\\n    min-height: 420px;\\\\r\\\\n    overflow: hidden;\\\\r\\\\n    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);\\\\r\\\\n}\\\\r\\\\n.carousel-track {\\\\r\\\\n    position: relative;\\\\r\\\\n    height: 100%;\\\\r\\\\n    display: flex;\\\\r\\\\n    transition: transform 0.7s cubic-bezier(0.65, 0, 0.35, 1);\\\\r\\\\n}\\\\r\\\\n.slide {\\\\r\\\\n    position: relative;\\\\r\\\\n    flex: 1 0 100%;\\\\r\\\\n    width: 100%;\\\\r\\\\n    height: 100%;\\\\r\\\\n    background-size: cover;\\\\r\\\\n    background-position: center;\\\\r\\\\n    display: flex;\\\\r\\\\n    justify-content: center;\\\\r\\\\n    align-items: center;\\\\r\\\\n    color: white;\\\\r\\\\n}\\\\r\\\\n.slide::before {\\\\r\\\\n    content:\\'\\';\\\\r\\\\n    position: absolute;\\\\r\\\\n    top: 0;\\\\r\\\\n    left: 0;\\\\r\\\\n    width: 100%;\\\\r\\\\n    height: 100%;\\\\r\\\\n    background-color: rgba(0, 0, 0, 0.25);\\\\r\\\\n    z-index: 1;\\\\r\\\\n}\\\\r\\\\n.hero-text {\\\\r\\\\n    position: relative;\\\\r\\\\n    z-index: 2;\\\\r\\\\n    text-align: center;\\\\r\\\\n    max-width: 90%;\\\\r\\\\n    padding: 20px;\\\\r\\\\n    text-shadow: 0 2px 15px rgba(0, 0, 0, 0.4);\\\\r\\\\n    opacity: 0;\\\\r\\\\n    transform: translateY(25px);\\\\r\\\\n    transition: opacity 0.6s cubic-bezier(0.22, 1, 0.36, 1) 0.3s, transform 0.6s cubic-bezier(0.22, 1, 0.36, 1) 0.3s;\\\\r\\\\n}\\\\r\\\\n.current-slide .hero-text {\\\\r\\\\n    opacity: 1;\\\\r\\\\n    transform: translateY(0);\\\\r\\\\n}\\\\r\\\\n.hero-text h1 {\\\\r\\\\n    font-size: 3rem;\\\\r\\\\n    font-weight: 700;\\\\r\\\\n    margin-bottom: 0.75rem;\\\\r\\\\n    letter-spacing: 1px;\\\\r\\\\n}\\\\r\\\\n.hero-text p {\\\\r\\\\n    font-size: 1.2rem;\\\\r\\\\n    font-weight: 300;\\\\r\\\\n    max-width: 650px;\\\\r\\\\n    margin: 0 auto;\\\\r\\\\n    line-height: 1.6;\\\\r\\\\n}\\\\r\\\\n.arrow {\\\\r\\\\n    position: absolute;\\\\r\\\\n    top: 50%;\\\\r\\\\n    transform: translateY(-50%);\\\\r\\\\n    background: rgba(0, 0, 0, 0.2);\\\\r\\\\n    border: none;\\\\r\\\\n    color: rgba(255, 255, 255, 0.9);\\\\r\\\\n    font-size: 1.5rem;\\\\r\\\\n    padding: 10px;\\\\r\\\\n    width: 50px;\\\\r\\\\n    height: 50px;\\\\r\\\\n    border-radius: 4px;\\\\r\\\\n    cursor: pointer;\\\\r\\\\n    z-index: 10;\\\\r\\\\n    display: flex;\\\\r\\\\n    justify-content: center;\\\\r\\\\n    align-items: center;\\\\r\\\\n    transition: all 0.2s ease-in-out;\\\\r\\\\n}\\\\r\\\\n.arrow:hover {\\\\r\\\\n    background-color: rgba(0, 0, 0, 0.5);\\\\r\\\\n    transform: translateY(-50%) scale(1.05);\\\\r\\\\n}\\\\r\\\\n.left-arrow {\\\\r\\\\n    left: 30px;\\\\r\\\\n}\\\\r\\\\n.right-arrow {\\\\r\\\\n    right: 30px;\\\\r\\\\n}\\\\r\\\\n.carousel-dots {\\\\r\\\\n    position: absolute;\\\\r\\\\n    bottom: 25px;\\\\r\\\\n    left: 50%;\\\\r\\\\n    transform: translateX(-50%);\\\\r\\\\n    display: flex;\\\\r\\\\n    gap: 10px;\\\\r\\\\n    z-index: 10;\\\\r\\\\n}\\\\r\\\\n.dot {\\\\r\\\\n    border: none;\\\\r\\\\n    width: 25px;\\\\r\\\\n    height: 4px;\\\\r\\\\n    border-radius: 2px;\\\\r\\\\n    background-color: rgba(255, 255, 255, 0.4);\\\\r\\\\n    cursor: pointer;\\\\r\\\\n    transition: all 0.3s ease;\\\\r\\\\n}\\\\r\\\\n.dot.current-dot {\\\\r\\\\n    background-color: white;\\\\r\\\\n    width: 40px;\\\\r\\\\n}\\\\r\\\\n@media (max-width: 768px) {\\\\r\\\\n    .hero-text h1 {\\\\r\\\\n        font-size: 2rem;\\\\r\\\\n    }\\\\r\\\\n    .hero-text p {\\\\r\\\\n        font-size: 1rem;\\\\r\\\\n    }\\\\r\\\\n    .arrow {\\\\r\\\\n        display: none;\\\\r\\\\n    }\\\\r\\\\n}\\\"}' where `id` = 201", "type": "query", "params": [], "bindings": ["{\"html\":\"<div class=\\\"carousel\\\">\\r\\n        <div class=\\\"carousel-track\\\">\\r\\n            <div class=\\\"slide current-slide\\\" style=\\\"background-image: url('https:\\/\\/images.unsplash.com\\/photo-1506744038136-46273834b3fb?q=80&w=2070&auto=format&fit=crop');\\\">\\r\\n                <div class=\\\"hero-text\\\">\\r\\n                    <h1>Like gentle sunshine<\\/h1>\\r\\n                    <p>Discover our eco-friendly collection, crafted with natural materials that feel as gentle as a ray of sunshine on a quiet morning.<\\/p>\\r\\n                <\\/div>\\r\\n            <\\/div>\\r\\n\\r\\n            <div class=\\\"slide\\\" style=\\\"background-image: url('https:\\/\\/images.unsplash.com\\/photo-1470770841072-f978cf4d019e?q=80&w=2070&auto=format&fit=crop');\\\">\\r\\n                <div class=\\\"hero-text\\\">\\r\\n                    <h1>Whispers of the Forest<\\/h1>\\r\\n                    <p>Journey through our latest designs, inspired by the deep greens and untold stories whispered through the ancient forest canopy.<\\/p>\\r\\n                <\\/div>\\r\\n            <\\/div>\\r\\n\\r\\n            <div class=\\\"slide\\\" style=\\\"background-image: url('https:\\/\\/images.unsplash.com\\/photo-1439405326853-58f2724f4d31?q=80&w=2070&auto=format&fit=crop');\\\">\\r\\n                <div class=\\\"hero-text\\\">\\r\\n                    <h1>Echoes of the Ocean<\\/h1>\\r\\n                    <p>Embrace the calm and power of the sea with our new line, featuring serene colors and fluid designs that capture the rhythm of the waves.<\\/p>\\r\\n                <\\/div>\\r\\n            <\\/div>\\r\\n        <\\/div>\\r\\n\\r\\n        <button class=\\\"arrow left-arrow\\\">&#10094;<\\/button>\\r\\n        <button class=\\\"arrow right-arrow\\\">&#10095;<\\/button>\\r\\n        \\r\\n        <div class=\\\"carousel-dots\\\">\\r\\n        <\\/div>\\r\\n    <\\/div>\",\"css\":\".carousel {\\r\\n    margin-top:14rem;\\r\\n    position: relative;\\r\\n    width: 100%;\\r\\n    height: 60vh;\\r\\n    min-height: 420px;\\r\\n    overflow: hidden;\\r\\n    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);\\r\\n}\\r\\n.carousel-track {\\r\\n    position: relative;\\r\\n    height: 100%;\\r\\n    display: flex;\\r\\n    transition: transform 0.7s cubic-bezier(0.65, 0, 0.35, 1);\\r\\n}\\r\\n.slide {\\r\\n    position: relative;\\r\\n    flex: 1 0 100%;\\r\\n    width: 100%;\\r\\n    height: 100%;\\r\\n    background-size: cover;\\r\\n    background-position: center;\\r\\n    display: flex;\\r\\n    justify-content: center;\\r\\n    align-items: center;\\r\\n    color: white;\\r\\n}\\r\\n.slide::before {\\r\\n    content:'';\\r\\n    position: absolute;\\r\\n    top: 0;\\r\\n    left: 0;\\r\\n    width: 100%;\\r\\n    height: 100%;\\r\\n    background-color: rgba(0, 0, 0, 0.25);\\r\\n    z-index: 1;\\r\\n}\\r\\n.hero-text {\\r\\n    position: relative;\\r\\n    z-index: 2;\\r\\n    text-align: center;\\r\\n    max-width: 90%;\\r\\n    padding: 20px;\\r\\n    text-shadow: 0 2px 15px rgba(0, 0, 0, 0.4);\\r\\n    opacity: 0;\\r\\n    transform: translateY(25px);\\r\\n    transition: opacity 0.6s cubic-bezier(0.22, 1, 0.36, 1) 0.3s, transform 0.6s cubic-bezier(0.22, 1, 0.36, 1) 0.3s;\\r\\n}\\r\\n.current-slide .hero-text {\\r\\n    opacity: 1;\\r\\n    transform: translateY(0);\\r\\n}\\r\\n.hero-text h1 {\\r\\n    font-size: 3rem;\\r\\n    font-weight: 700;\\r\\n    margin-bottom: 0.75rem;\\r\\n    letter-spacing: 1px;\\r\\n}\\r\\n.hero-text p {\\r\\n    font-size: 1.2rem;\\r\\n    font-weight: 300;\\r\\n    max-width: 650px;\\r\\n    margin: 0 auto;\\r\\n    line-height: 1.6;\\r\\n}\\r\\n.arrow {\\r\\n    position: absolute;\\r\\n    top: 50%;\\r\\n    transform: translateY(-50%);\\r\\n    background: rgba(0, 0, 0, 0.2);\\r\\n    border: none;\\r\\n    color: rgba(255, 255, 255, 0.9);\\r\\n    font-size: 1.5rem;\\r\\n    padding: 10px;\\r\\n    width: 50px;\\r\\n    height: 50px;\\r\\n    border-radius: 4px;\\r\\n    cursor: pointer;\\r\\n    z-index: 10;\\r\\n    display: flex;\\r\\n    justify-content: center;\\r\\n    align-items: center;\\r\\n    transition: all 0.2s ease-in-out;\\r\\n}\\r\\n.arrow:hover {\\r\\n    background-color: rgba(0, 0, 0, 0.5);\\r\\n    transform: translateY(-50%) scale(1.05);\\r\\n}\\r\\n.left-arrow {\\r\\n    left: 30px;\\r\\n}\\r\\n.right-arrow {\\r\\n    right: 30px;\\r\\n}\\r\\n.carousel-dots {\\r\\n    position: absolute;\\r\\n    bottom: 25px;\\r\\n    left: 50%;\\r\\n    transform: translateX(-50%);\\r\\n    display: flex;\\r\\n    gap: 10px;\\r\\n    z-index: 10;\\r\\n}\\r\\n.dot {\\r\\n    border: none;\\r\\n    width: 25px;\\r\\n    height: 4px;\\r\\n    border-radius: 2px;\\r\\n    background-color: rgba(255, 255, 255, 0.4);\\r\\n    cursor: pointer;\\r\\n    transition: all 0.3s ease;\\r\\n}\\r\\n.dot.current-dot {\\r\\n    background-color: white;\\r\\n    width: 40px;\\r\\n}\\r\\n@media (max-width: 768px) {\\r\\n    .hero-text h1 {\\r\\n        font-size: 2rem;\\r\\n    }\\r\\n    .hero-text p {\\r\\n        font-size: 1rem;\\r\\n    }\\r\\n    .arrow {\\r\\n        display: none;\\r\\n    }\\r\\n}\"}", 201], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 387}, {"index": 15, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 36}, {"index": 22, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 700}, {"index": 23, "namespace": null, "name": "packages/Webkul/Theme/src/Repositories/ThemeCustomizationRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Theme\\src\\Repositories\\ThemeCustomizationRepository.php", "line": 42}, {"index": 24, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Settings/ThemeController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Settings\\ThemeController.php", "line": 117}], "start": **********.509534, "duration": 0.00216, "duration_str": "2.16ms", "memory": 0, "memory_str": null, "filename": "Translatable.php:387", "source": {"index": 14, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 387}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=387", "ajax": false, "filename": "Translatable.php", "line": "387"}, "connection": "mlk", "explain": null, "start_percent": 66.9, "width_percent": 25.175}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "packages/Webkul/Theme/src/Repositories/ThemeCustomizationRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Theme\\src\\Repositories\\ThemeCustomizationRepository.php", "line": 149}, {"index": 22, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Settings/ThemeController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Settings\\ThemeController.php", "line": 120}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.516734, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 92.075, "width_percent": 4.895}, {"sql": "select * from `currencies` where `currencies`.`id` = 2 limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 22, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Core.php", "line": 390}, {"index": 23, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Core.php", "line": 433}, {"index": 24, "namespace": null, "name": "packages/Webkul/FPC/src/Hasher/DefaultHasher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\FPC\\src\\Hasher\\DefaultHasher.php", "line": 29}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-responsecache/src/Hasher/DefaultHasher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\spatie\\laravel-responsecache\\src\\Hasher\\DefaultHasher.php", "line": 18}], "start": **********.523198, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 20, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 96.97, "width_percent": 3.03}]}, "models": {"data": {"Webkul\\Theme\\Models\\ThemeCustomizationTranslation": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FTheme%2Fsrc%2FModels%2FThemeCustomizationTranslation.php&line=1", "ajax": false, "filename": "ThemeCustomizationTranslation.php", "line": "?"}}, "Webkul\\Core\\Models\\Locale": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FModels%2FLocale.php&line=1", "ajax": false, "filename": "Locale.php", "line": "?"}}, "Webkul\\Core\\Models\\Channel": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FModels%2FChannel.php&line=1", "ajax": false, "filename": "Channel.php", "line": "?"}}, "Webkul\\User\\Models\\Admin": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FUser%2Fsrc%2FModels%2FAdmin.php&line=1", "ajax": false, "filename": "Admin.php", "line": "?"}}, "Webkul\\User\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FUser%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "Webkul\\Theme\\Models\\ThemeCustomization": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FTheme%2Fsrc%2FModels%2FThemeCustomization.php&line=1", "ajax": false, "filename": "ThemeCustomization.php", "line": "?"}}, "Webkul\\Core\\Models\\Currency": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FModels%2FCurrency.php&line=1", "ajax": false, "filename": "Currency.php", "line": "?"}}}, "count": 15, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "302 Found", "full_url": "http://mlk.test/admin/settings/themes/edit/46", "action_name": "admin.settings.themes.update", "controller_action": "Webkul\\Admin\\Http\\Controllers\\Settings\\ThemeController@update", "uri": "POST admin/settings/themes/edit/{id}", "controller": "Webkul\\Admin\\Http\\Controllers\\Settings\\ThemeController@update<a href=\"phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FHttp%2FControllers%2FSettings%2FThemeController.php&line=90\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "admin/settings/themes", "file": "<a href=\"phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FHttp%2FControllers%2FSettings%2FThemeController.php&line=90\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">packages/Webkul/Admin/src/Http/Controllers/Settings/ThemeController.php:90-127</a>", "middleware": "web, admin_locale, Webkul\\Core\\Http\\Middleware\\PreventRequestsDuringMaintenance, admin, Webkul\\Core\\Http\\Middleware\\NoCacheMiddleware", "duration": "268ms", "peak_memory": "42MB", "response": "Redirect to http://mlk.test/admin/settings/themes", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1153020677 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1153020677\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1003286731 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">RL8eYWcLOX1nKpLrVKRAtWfTglgDAXuESTadrPQu</span>\"\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>en</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>options</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>html</span>\" => \"\"\"\n        <span class=sf-dump-str title=\"1663 characters\">&lt;div class=&quot;carousel&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"1663 characters\">        &lt;div class=&quot;carousel-track&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"1663 characters\">            &lt;div class=&quot;slide current-slide&quot; style=&quot;background-image: url(&#039;https://images.unsplash.com/photo-1506744038136-46273834b3fb?q=80&amp;w=2070&amp;auto=format&amp;fit=crop&#039;);&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"1663 characters\">                &lt;div class=&quot;hero-text&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"1663 characters\">                    &lt;h1&gt;Like gentle sunshine&lt;/h1&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"1663 characters\">                    &lt;p&gt;Discover our eco-friendly collection, crafted with natural materials that feel as gentle as a ray of sunshine on a quiet morning.&lt;/p&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"1663 characters\">                &lt;/div&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"1663 characters\">            &lt;/div&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"1663 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"1663 characters\">            &lt;div class=&quot;slide&quot; style=&quot;background-image: url(&#039;https://images.unsplash.com/photo-1470770841072-f978cf4d019e?q=80&amp;w=2070&amp;auto=format&amp;fit=crop&#039;);&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"1663 characters\">                &lt;div class=&quot;hero-text&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"1663 characters\">                    &lt;h1&gt;Whispers of the Forest&lt;/h1&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"1663 characters\">                    &lt;p&gt;Journey through our latest designs, inspired by the deep greens and untold stories whispered through the ancient forest canopy.&lt;/p&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"1663 characters\">                &lt;/div&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"1663 characters\">            &lt;/div&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"1663 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"1663 characters\">            &lt;div class=&quot;slide&quot; style=&quot;background-image: url(&#039;https://images.unsplash.com/photo-1439405326853-58f2724f4d31?q=80&amp;w=2070&amp;auto=format&amp;fit=crop&#039;);&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"1663 characters\">                &lt;div class=&quot;hero-text&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"1663 characters\">                    &lt;h1&gt;Echoes of the Ocean&lt;/h1&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"1663 characters\">                    &lt;p&gt;Embrace the calm and power of the sea with our new line, featuring serene colors and fluid designs that capture the rhythm of the waves.&lt;/p&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"1663 characters\">                &lt;/div&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"1663 characters\">            &lt;/div&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"1663 characters\">        &lt;/div&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"1663 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"1663 characters\">        &lt;button class=&quot;arrow left-arrow&quot;&gt;&amp;#10094;&lt;/button&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"1663 characters\">        &lt;button class=&quot;arrow right-arrow&quot;&gt;&amp;#10095;&lt;/button&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"1663 characters\">        <span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"1663 characters\">        &lt;div class=&quot;carousel-dots&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"1663 characters\">        &lt;/div&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"1663 characters\">    &lt;/div&gt;</span>\n        \"\"\"\n      \"<span class=sf-dump-key>css</span>\" => \"\"\"\n        <span class=sf-dump-str title=\"2704 characters\">.carousel {<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2704 characters\">    margin-top:14rem;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2704 characters\">    position: relative;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2704 characters\">    width: 100%;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2704 characters\">    height: 60vh;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2704 characters\">    min-height: 420px;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2704 characters\">    overflow: hidden;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2704 characters\">    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2704 characters\">}<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2704 characters\">.carousel-track {<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2704 characters\">    position: relative;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2704 characters\">    height: 100%;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2704 characters\">    display: flex;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2704 characters\">    transition: transform 0.7s cubic-bezier(0.65, 0, 0.35, 1);<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2704 characters\">}<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2704 characters\">.slide {<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2704 characters\">    position: relative;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2704 characters\">    flex: 1 0 100%;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2704 characters\">    width: 100%;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2704 characters\">    height: 100%;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2704 characters\">    background-size: cover;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2704 characters\">    background-position: center;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2704 characters\">    display: flex;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2704 characters\">    justify-content: center;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2704 characters\">    align-items: center;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2704 characters\">    color: white;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2704 characters\">}<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2704 characters\">.slide::before {<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2704 characters\">    content:&#039;&#039;;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2704 characters\">    position: absolute;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2704 characters\">    top: 0;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2704 characters\">    left: 0;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2704 characters\">    width: 100%;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2704 characters\">    height: 100%;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2704 characters\">    background-color: rgba(0, 0, 0, 0.25);<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2704 characters\">    z-index: 1;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2704 characters\">}<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2704 characters\">.hero-text {<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2704 characters\">    position: relative;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2704 characters\">    z-index: 2;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2704 characters\">    text-align: center;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2704 characters\">    max-width: 90%;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2704 characters\">    padding: 20px;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2704 characters\">    text-shadow: 0 2px 15px rgba(0, 0, 0, 0.4);<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2704 characters\">    opacity: 0;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2704 characters\">    transform: translateY(25px);<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2704 characters\">    transition: opacity 0.6s cubic-bezier(0.22, 1, 0.36, 1) 0.3s, transform 0.6s cubic-bezier(0.22, 1, 0.36, 1) 0.3s;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2704 characters\">}<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2704 characters\">.current-slide .hero-text {<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2704 characters\">    opacity: 1;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2704 characters\">    transform: translateY(0);<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2704 characters\">}<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2704 characters\">.hero-text h1 {<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2704 characters\">    font-size: 3rem;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2704 characters\">    font-weight: 700;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2704 characters\">    margin-bottom: 0.75rem;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2704 characters\">    letter-spacing: 1px;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2704 characters\">}<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2704 characters\">.hero-text p {<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2704 characters\">    font-size: 1.2rem;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2704 characters\">    font-weight: 300;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2704 characters\">    max-width: 650px;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2704 characters\">    margin: 0 auto;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2704 characters\">    line-height: 1.6;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2704 characters\">}<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2704 characters\">.arrow {<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2704 characters\">    position: absolute;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2704 characters\">    top: 50%;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2704 characters\">    transform: translateY(-50%);<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2704 characters\">    background: rgba(0, 0, 0, 0.2);<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2704 characters\">    border: none;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2704 characters\">    color: rgba(255, 255, 255, 0.9);<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2704 characters\">    font-size: 1.5rem;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2704 characters\">    padding: 10px;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2704 characters\">    width: 50px;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2704 characters\">    height: 50px;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2704 characters\">    border-radius: 4px;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2704 characters\">    cursor: pointer;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2704 characters\">    z-index: 10;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2704 characters\">    display: flex;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2704 characters\">    justify-content: center;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2704 characters\">    align-items: center;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2704 characters\">    transition: all 0.2s ease-in-out;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2704 characters\">}<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2704 characters\">.arrow:hover {<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2704 characters\">    background-color: rgba(0, 0, 0, 0.5);<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2704 characters\">    transform: translateY(-50%) scale(1.05);<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2704 characters\">}<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2704 characters\">.left-arrow {<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2704 characters\">    left: 30px;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2704 characters\">}<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2704 characters\">.right-arrow {<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2704 characters\">    right: 30px;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2704 characters\">}<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2704 characters\">.carousel-dots {<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2704 characters\">    position: absolute;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2704 characters\">    bottom: 25px;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2704 characters\">    left: 50%;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2704 characters\">    transform: translateX(-50%);<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2704 characters\">    display: flex;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2704 characters\">    gap: 10px;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2704 characters\">    z-index: 10;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2704 characters\">}<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2704 characters\">.dot {<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2704 characters\">    border: none;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2704 characters\">    width: 25px;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2704 characters\">    height: 4px;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2704 characters\">    border-radius: 2px;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2704 characters\">    background-color: rgba(255, 255, 255, 0.4);<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2704 characters\">    cursor: pointer;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2704 characters\">    transition: all 0.3s ease;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2704 characters\">}<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2704 characters\">.dot.current-dot {<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2704 characters\">    background-color: white;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2704 characters\">    width: 40px;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2704 characters\">}<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2704 characters\">@media (max-width: 768px) {<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2704 characters\">    .hero-text h1 {<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2704 characters\">        font-size: 2rem;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2704 characters\">    }<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2704 characters\">    .hero-text p {<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2704 characters\">        font-size: 1rem;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2704 characters\">    }<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2704 characters\">    .arrow {<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2704 characters\">        display: none;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2704 characters\">    }<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2704 characters\">}</span>\n        \"\"\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"14 characters\">static_content</span>\"\n  \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">slogan</span>\"\n  \"<span class=sf-dump-key>sort_order</span>\" => \"<span class=sf-dump-str>8</span>\"\n  \"<span class=sf-dump-key>channel_id</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>theme_code</span>\" => \"<span class=sf-dump-str title=\"7 characters\">default</span>\"\n  \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"2 characters\">on</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1003286731\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1465301892 data-indent-pad=\"  \"><span class=sf-dump-note>array:13</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1018 characters\">admin_locale=eyJpdiI6InlVTlI4cytSTEhwU1oxNXhFNmszTHc9PSIsInZhbHVlIjoiL1djczhIanIvN1ZXTlhGdmpwMWJhNkpqMnNiMHcxMjBic21GQ0ltRzRZV25DczdBcXZaVUNJWlFEakx1bjV6byIsIm1hYyI6IjhmNjlhYzdkYWNjMTVkNjA5ZThmMzlhYmViM2UwYTFiZDQxZDY4ZjEwZTg3NGVjZGNhOTAzODA3ODhhNjllMzQiLCJ0YWciOiIifQ%3D%3D; sidebar_collapsed=0; dark_mode=0; XSRF-TOKEN=eyJpdiI6IkJzU0xQT0VzZm9nZVFQMy9ZYmpzV2c9PSIsInZhbHVlIjoiSGNUTjBkb2dzTmUrNEdTYWRyV0ZSZUZxaWxzRmNZN2QwWTlhbDljcGg2bkdvdVhFOEJYaWhzYzJMdFRhb1FrdUVCdjRVbGVxUFZZV3p1M2hDU2h0UjcvQkJXTFQraEMrKy95RGFzNFdVcklXdXFQSlRIcU15WjV6WHp3YiswMW8iLCJtYWMiOiI0N2YxMDlhODZmMThhZWEzNDRkNWRmYTY3YjI1NTczZjEwMmI3OGY3ODgzMmIzZGY2NzRjNDA1NjAxZDYwOGNhIiwidGFnIjoiIn0%3D; mlk_session=eyJpdiI6InN1QlpJOUtTejFOZ0V0NGlnYkxJZHc9PSIsInZhbHVlIjoiU01SNWZrVG1SZW52ajI3bXdMV0phcllTVmV1TjFGdmNmaEMyMlNsMkhNZ1QyL2tRdzRYQWpweU9BREo5dnlaOG0wekNiMVdEVWkrQU51Y0FxdFlYUDRLV0U1Y2liaW16V1UrdnUzNHdDclRsNzJOY1B0R2E3TmN5OFFXSkEvYjEiLCJtYWMiOiIxNjhkYmFjYzgwNzdiM2M2MjE0OGM3MjE5NjNjMGJiMmNkNmJmNWE3NzJhMzBiMjY1ODc3M2U5NmYyMWNkMjlkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">zh-CN,zh;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"71 characters\">http://mlk.test/admin/settings/themes/edit/46?channel=default&amp;locale=en</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">multipart/form-data; boundary=----WebKitFormBoundaryeDvoh7eahhfkWFur</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"15 characters\">http://mlk.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">5443</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">mlk.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1465301892\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1431728495 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>admin_locale</span>\" => \"<span class=sf-dump-str title=\"5 characters\">zh_CN</span>\"\n  \"<span class=sf-dump-key>sidebar_collapsed</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>dark_mode</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">RL8eYWcLOX1nKpLrVKRAtWfTglgDAXuESTadrPQu</span>\"\n  \"<span class=sf-dump-key>mlk_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Zao9ptAmXhjVm7WD2j4izotzmKLg3wYf4OgogoiC</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1431728495\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-108945908 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 07 Aug 2025 16:09:55 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://mlk.test/admin/settings/themes</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>0</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-108945908\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1512707433 data-indent-pad=\"  \"><span class=sf-dump-note>array:10</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">RL8eYWcLOX1nKpLrVKRAtWfTglgDAXuESTadrPQu</span>\"\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>currency</span>\" => \"<span class=sf-dump-str title=\"3 characters\">EUR</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"71 characters\">http://mlk.test/admin/settings/themes/edit/46?channel=default&amp;locale=en</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>admin_locale</span>\" => \"<span class=sf-dump-str title=\"5 characters\">zh_CN</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>success</span>\" => \"<span class=sf-dump-str title=\"6 characters\">&#25104;&#21151;&#26356;&#26032;&#20027;&#39064;</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1512707433\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "302 Found", "full_url": "http://mlk.test/admin/settings/themes/edit/46", "action_name": "admin.settings.themes.update", "controller_action": "Webkul\\Admin\\Http\\Controllers\\Settings\\ThemeController@update"}, "badge": "302 Found"}}