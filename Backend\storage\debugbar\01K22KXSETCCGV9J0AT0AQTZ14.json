{"__meta": {"id": "01K22KXSETCCGV9J0AT0AQTZ14", "datetime": "2025-08-07 16:55:48", "utime": **********.570643, "method": "POST", "uri": "/admin/settings/themes/edit/45", "ip": "127.0.0.1"}, "modules": {"count": 3, "modules": [{"name": "Webkul\\Core", "models": ["Webkul\\Core\\Models\\Channel (1)", "Webkul\\Core\\Models\\Locale (5)", "Webkul\\Core\\Models\\Currency (1)"], "views": [], "queries": [{"sql": "select * from `channels` where `hostname` in ('mlk.test', 'http://mlk.test', 'https://mlk.test')", "duration": 0.25, "duration_str": "250ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 2, "duration_str": "2s", "connection": "mlk"}, {"sql": "select * from `currencies` where `currencies`.`id` = 2 limit 1", "duration": 0.22, "duration_str": "220ms", "connection": "mlk"}]}, {"name": "Webkul\\Theme", "models": ["Webkul\\Theme\\Models\\ThemeCustomization (1)"], "views": [], "queries": [{"sql": "select * from `theme_customizations` where `theme_customizations`.`id` = 45 limit 1", "duration": 1.88, "duration_str": "1.88s", "connection": "mlk"}, {"sql": "select * from `theme_customization_translations` where `theme_customization_translations`.`theme_customization_id` in (45)", "duration": 1.89, "duration_str": "1.89s", "connection": "mlk"}, {"sql": "update `theme_customizations` set `status` = 0, `theme_customizations`.`updated_at` = '2025-08-07 16:55:48' where `id` = 45", "duration": 3.44, "duration_str": "3.44s", "connection": "mlk"}]}, {"name": "Webkul\\User", "models": ["Webkul\\User\\Models\\Admin (1)", "Webkul\\User\\Models\\Role (1)"], "views": [], "queries": [{"sql": "select count(*) as aggregate from `admins`", "duration": 0.19, "duration_str": "190ms", "connection": "mlk"}, {"sql": "select count(*) as aggregate from `admins`", "duration": 0.17, "duration_str": "170ms", "connection": "mlk"}, {"sql": "select * from `admins` where `id` = 1 limit 1", "duration": 0.22, "duration_str": "220ms", "connection": "mlk"}, {"sql": "select * from `roles` where `roles`.`id` = 1 limit 1", "duration": 0.18, "duration_str": "180ms", "connection": "mlk"}]}]}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.221439, "end": **********.579217, "duration": 0.35777807235717773, "duration_str": "358ms", "measures": [{"label": "Booting", "start": **********.221439, "relative_start": 0, "end": **********.452546, "relative_end": **********.452546, "duration": 0.*****************, "duration_str": "231ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.452557, "relative_start": 0.*****************, "end": **********.579219, "relative_end": 2.1457672119140625e-06, "duration": 0.***************, "duration_str": "127ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.465685, "relative_start": 0.****************, "end": **********.467966, "relative_end": **********.467966, "duration": 0.*****************, "duration_str": "2.28ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.568955, "relative_start": 0.****************, "end": **********.569233, "relative_end": **********.569233, "duration": 0.0002779960632324219, "duration_str": "278μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "39MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.2.0", "Environment": "local", "Debug Mode": "Enabled", "URL": "mlk.test", "Timezone": "Africa/Lagos", "Locale": "zh_CN"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 17, "nb_statements": 17, "nb_visible_statements": 17, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.029300000000000003, "accumulated_duration_str": "29.3ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select exists (select 1 from information_schema.tables where table_schema = 'mlk' and table_name = 'admins' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 32}, {"index": 14, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 44}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 1004}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 832}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 1078}], "start": **********.481086, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "DatabaseManager.php:32", "source": {"index": 13, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 32}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FInstaller%2Fsrc%2FHelpers%2FDatabaseManager.php&line=32", "ajax": false, "filename": "DatabaseManager.php", "line": "32"}, "connection": "mlk", "explain": null, "start_percent": 0, "width_percent": 1.536}, {"sql": "select count(*) as aggregate from `admins`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 38}, {"index": 16, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 44}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 1004}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 832}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 1078}], "start": **********.485175, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "DatabaseManager.php:38", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 38}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FInstaller%2Fsrc%2FHelpers%2FDatabaseManager.php&line=38", "ajax": false, "filename": "DatabaseManager.php", "line": "38"}, "connection": "mlk", "explain": null, "start_percent": 1.536, "width_percent": 0.648}, {"sql": "select * from `channels` where `hostname` in ('mlk.test', 'http://mlk.test', 'https://mlk.test')", "type": "query", "params": [], "bindings": ["mlk.test", "http://mlk.test", "https://mlk.test"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 578}, {"index": 16, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Core.php", "line": 143}, {"index": 17, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 127}, {"index": 18, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 45}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 1004}], "start": **********.491927, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:578", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 578}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=578", "ajax": false, "filename": "BaseRepository.php", "line": "578"}, "connection": "mlk", "explain": null, "start_percent": 2.184, "width_percent": 0.853}, {"sql": "select exists (select 1 from information_schema.tables where table_schema = 'mlk' and table_name = 'admins' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 32}, {"index": 14, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 59}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 16, "namespace": "middleware", "name": "admin_locale", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Middleware\\AdminLocale.php", "line": 46}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.494486, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "DatabaseManager.php:32", "source": {"index": 13, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 32}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FInstaller%2Fsrc%2FHelpers%2FDatabaseManager.php&line=32", "ajax": false, "filename": "DatabaseManager.php", "line": "32"}, "connection": "mlk", "explain": null, "start_percent": 3.038, "width_percent": 1.126}, {"sql": "select count(*) as aggregate from `admins`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 38}, {"index": 16, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 18, "namespace": "middleware", "name": "admin_locale", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Middleware\\AdminLocale.php", "line": 46}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.496038, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "DatabaseManager.php:38", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 38}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FInstaller%2Fsrc%2FHelpers%2FDatabaseManager.php&line=38", "ajax": false, "filename": "DatabaseManager.php", "line": "38"}, "connection": "mlk", "explain": null, "start_percent": 4.164, "width_percent": 0.58}, {"sql": "select * from `admins` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "admin", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\User\\src\\Http\\Middleware\\Bouncer.php", "line": 18}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.500298, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "mlk", "explain": null, "start_percent": 4.744, "width_percent": 0.751}, {"sql": "select * from `roles` where `roles`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": "middleware", "name": "admin", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\User\\src\\Http\\Middleware\\Bouncer.php", "line": 54}, {"index": 22, "namespace": "middleware", "name": "admin", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\User\\src\\Http\\Middleware\\Bouncer.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 24, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 119}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.502835, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "admin:54", "source": {"index": 21, "namespace": "middleware", "name": "admin", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\User\\src\\Http\\Middleware\\Bouncer.php", "line": 54}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FUser%2Fsrc%2FHttp%2FMiddleware%2FBouncer.php&line=54", "ajax": false, "filename": "Bouncer.php", "line": "54"}, "connection": "mlk", "explain": null, "start_percent": 5.495, "width_percent": 0.614}, {"sql": "select * from `theme_customizations` where `theme_customizations`.`id` = 45 limit 1", "type": "query", "params": [], "bindings": [45], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, {"index": 21, "namespace": null, "name": "packages/Webkul/Theme/src/Repositories/ThemeCustomizationRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Theme\\src\\Repositories\\ThemeCustomizationRepository.php", "line": 42}, {"index": 22, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Settings/ThemeController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Settings\\ThemeController.php", "line": 117}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.506653, "duration": 0.00188, "duration_str": "1.88ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:695", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=695", "ajax": false, "filename": "BaseRepository.php", "line": "695"}, "connection": "mlk", "explain": null, "start_percent": 6.109, "width_percent": 6.416}, {"sql": "select * from `theme_customization_translations` where `theme_customization_translations`.`theme_customization_id` in (45)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 25, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, {"index": 26, "namespace": null, "name": "packages/Webkul/Theme/src/Repositories/ThemeCustomizationRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Theme\\src\\Repositories\\ThemeCustomizationRepository.php", "line": 42}, {"index": 27, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Settings/ThemeController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Settings\\ThemeController.php", "line": 117}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.510811, "duration": 0.00189, "duration_str": "1.89ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:695", "source": {"index": 25, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=695", "ajax": false, "filename": "BaseRepository.php", "line": "695"}, "connection": "mlk", "explain": null, "start_percent": 12.526, "width_percent": 6.451}, {"sql": "update `theme_customizations` set `status` = 0, `theme_customizations`.`updated_at` = '2025-08-07 16:55:48' where `id` = 45", "type": "query", "params": [], "bindings": [0, "2025-08-07 16:55:48", 45], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 700}, {"index": 15, "namespace": null, "name": "packages/Webkul/Theme/src/Repositories/ThemeCustomizationRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Theme\\src\\Repositories\\ThemeCustomizationRepository.php", "line": 42}, {"index": 16, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Settings/ThemeController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Settings\\ThemeController.php", "line": 117}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.524136, "duration": 0.00344, "duration_str": "3.44ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:700", "source": {"index": 14, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 700}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=700", "ajax": false, "filename": "BaseRepository.php", "line": "700"}, "connection": "mlk", "explain": null, "start_percent": 18.976, "width_percent": 11.741}, {"sql": "insert into `theme_customization_translations` (`locale`, `theme_customization_id`, `options`) values ('en', 45, '{\\\"html\\\":\\\"<div class=\\\\\\\"carousel\\\\\\\">\\\\r\\\\n        <div class=\\\\\\\"carousel-track\\\\\\\">\\\\r\\\\n            <div class=\\\\\\\"slide current-slide\\\\\\\" style=\\\\\\\"background-image: url(\\'https:\\\\/\\\\/images.unsplash.com\\\\/photo-1506744038136-46273834b3fb?q=80&w=2070&auto=format&fit=crop\\');\\\\\\\">\\\\r\\\\n                <div class=\\\\\\\"hero-text\\\\\\\">\\\\r\\\\n                    <h1>Like gentle sunshine<\\\\/h1>\\\\r\\\\n                    <p>on quiet leaves<\\\\/p>\\\\r\\\\n                <\\\\/div>\\\\r\\\\n            <\\\\/div>\\\\r\\\\n\\\\r\\\\n            <div class=\\\\\\\"slide\\\\\\\" style=\\\\\\\"background-image: url(\\'https:\\\\/\\\\/images.unsplash.com\\\\/photo-1470770841072-f978cf4d019e?q=80&w=2070&auto=format&fit=crop\\');\\\\\\\">\\\\r\\\\n                <div class=\\\\\\\"hero-text\\\\\\\">\\\\r\\\\n                    <h1>Whispers of the Forest<\\\\/h1>\\\\r\\\\n                    <p>a story in every branch<\\\\/p>\\\\r\\\\n                <\\\\/div>\\\\r\\\\n            <\\\\/div>\\\\r\\\\n\\\\r\\\\n            <div class=\\\\\\\"slide\\\\\\\" style=\\\\\\\"background-image: url(\\'https:\\\\/\\\\/images.unsplash.com\\\\/photo-1439405326853-58f2724f4d31?q=80&w=2070&auto=format&fit=crop\\');\\\\\\\">\\\\r\\\\n                <div class=\\\\\\\"hero-text\\\\\\\">\\\\r\\\\n                    <h1>Echoes of the Ocean<\\\\/h1>\\\\r\\\\n                    <p>deep and serene<\\\\/p>\\\\r\\\\n                <\\\\/div>\\\\r\\\\n            <\\\\/div>\\\\r\\\\n            <\\\\/div>\\\\r\\\\n\\\\r\\\\n        <button class=\\\\\\\"arrow left-arrow\\\\\\\">&#10094;<\\\\/button>\\\\r\\\\n        <button class=\\\\\\\"arrow right-arrow\\\\\\\">&#10095;<\\\\/button>\\\\r\\\\n        \\\\r\\\\n        <div class=\\\\\\\"carousel-dots\\\\\\\">\\\\r\\\\n            <\\\\/div>\\\\r\\\\n    <\\\\/div>\\\\r\\\\n\\\\r\\\\n\\\",\\\"css\\\":\\\"\\\\/* --- 2. \\\\u8f6e\\\\u64ad\\\\u7ec4\\\\u4ef6\\\\u4e3b\\\\u8981\\\\u6837\\\\u5f0f --- *\\\\/\\\\r\\\\n        .carousel {\\\\r\\\\n            position: relative;\\\\r\\\\n            width: 100%;\\\\r\\\\n            height: 70vh; \\\\/* \\\\u60a8\\\\u53ef\\\\u4ee5\\\\u6839\\\\u636e\\\\u9700\\\\u8981\\\\u8c03\\\\u6574\\\\u9ad8\\\\u5ea6 *\\\\/\\\\r\\\\n            min-height: 450px;\\\\r\\\\n            overflow: hidden; \\\\/* \\\\u5173\\\\u952e\\\\uff1a\\\\u9690\\\\u85cf\\\\u6240\\\\u6709\\\\u5728\\\\u5bb9\\\\u5668\\\\u5916\\\\u7684\\\\u5e7b\\\\u706f\\\\u7247 *\\\\/\\\\r\\\\n        }\\\\r\\\\n\\\\r\\\\n        .carousel-track {\\\\r\\\\n            position: relative;\\\\r\\\\n            height: 100%;\\\\r\\\\n            display: flex; \\\\/* \\\\u8ba9\\\\u6240\\\\u6709\\\\u5e7b\\\\u706f\\\\u7247\\\\u5728\\\\u540c\\\\u4e00\\\\u884c\\\\u6392\\\\u5217 *\\\\/\\\\r\\\\n            transition: transform 0.5s ease-in-out; \\\\/* \\\\u5e73\\\\u6ed1\\\\u7684\\\\u6ed1\\\\u52a8\\\\u52a8\\\\u753b *\\\\/\\\\r\\\\n        }\\\\r\\\\n\\\\r\\\\n        .slide {\\\\r\\\\n            flex: 1 0 100%; \\\\/* \\\\u6bcf\\\\u5f20\\\\u5e7b\\\\u706f\\\\u7247\\\\u5360\\\\u636e100%\\\\u7684\\\\u5bbd\\\\u5ea6\\\\u4e14\\\\u4e0d\\\\u6536\\\\u7f29 *\\\\/\\\\r\\\\n            width: 100%;\\\\r\\\\n            height: 100%;\\\\r\\\\n            background-size: cover;\\\\r\\\\n            background-position: center;\\\\r\\\\n            display: flex;\\\\r\\\\n            justify-content: center;\\\\r\\\\n            align-items: center;\\\\r\\\\n        }\\\\r\\\\n\\\\r\\\\n        \\\\/* --- 3. \\\\u6587\\\\u672c\\\\u548c\\\\u52a8\\\\u753b --- *\\\\/\\\\r\\\\n        .hero-text {\\\\r\\\\n            text-align: center;\\\\r\\\\n            color: white;\\\\r\\\\n            max-width: 90%;\\\\r\\\\n            text-shadow: 0 2px 8px rgba(0, 0, 0, 0.5); \\\\/* \\\\u7ed9\\\\u6587\\\\u5b57\\\\u6dfb\\\\u52a0\\\\u9634\\\\u5f71\\\\u4ee5\\\\u589e\\\\u5f3a\\\\u53ef\\\\u8bfb\\\\u6027 *\\\\/\\\\r\\\\n            opacity: 0; \\\\/* \\\\u9ed8\\\\u8ba4\\\\u9690\\\\u85cf\\\\u6587\\\\u672c *\\\\/\\\\r\\\\n            transform: translateY(20px);\\\\r\\\\n            transition: opacity 0.5s ease-out 0.4s, transform 0.5s ease-out 0.4s; \\\\/* \\\\u5ef6\\\\u8fdf0.4\\\\u79d2\\\\u6267\\\\u884c\\\\u6587\\\\u672c\\\\u52a8\\\\u753b *\\\\/\\\\r\\\\n        }\\\\r\\\\n\\\\r\\\\n        .current-slide .hero-text {\\\\r\\\\n            opacity: 1; \\\\/* \\\\u5f53\\\\u524d\\\\u5e7b\\\\u706f\\\\u7247\\\\u7684\\\\u6587\\\\u672c\\\\u663e\\\\u793a\\\\u51fa\\\\u6765 *\\\\/\\\\r\\\\n            transform: translateY(0);\\\\r\\\\n        }\\\\r\\\\n\\\\r\\\\n        .hero-text h1 {\\\\r\\\\n            font-size: 3rem;\\\\r\\\\n            font-weight: 500;\\\\r\\\\n            margin-bottom: 0.5rem;\\\\r\\\\n        }\\\\r\\\\n\\\\r\\\\n        .hero-text p {\\\\r\\\\n            font-size: 1.2rem;\\\\r\\\\n            font-weight: 300;\\\\r\\\\n        }\\\\r\\\\n        \\\\r\\\\n        \\\\/* --- 4. \\\\u5de6\\\\u53f3\\\\u7bad\\\\u5934 --- *\\\\/\\\\r\\\\n        .arrow {\\\\r\\\\n            position: absolute;\\\\r\\\\n            top: 50%;\\\\r\\\\n            transform: translateY(-50%);\\\\r\\\\n            background: transparent;\\\\r\\\\n            border: 1px solid rgba(255, 255, 255, 0.4);\\\\r\\\\n            color: rgba(255, 255, 255, 0.7);\\\\r\\\\n            font-size: 1.5rem;\\\\r\\\\n            padding: 10px 15px;\\\\r\\\\n            border-radius: 50%;\\\\r\\\\n            cursor: pointer;\\\\r\\\\n            z-index: 10;\\\\r\\\\n            transition: all 0.2s ease-in-out;\\\\r\\\\n        }\\\\r\\\\n        \\\\r\\\\n        .arrow:hover {\\\\r\\\\n            background-color: rgba(0, 0, 0, 0.3);\\\\r\\\\n            color: white;\\\\r\\\\n            border-color: transparent;\\\\r\\\\n        }\\\\r\\\\n\\\\r\\\\n        .left-arrow { left: 30px; }\\\\r\\\\n        .right-arrow { right: 30px; }\\\\r\\\\n        \\\\r\\\\n        \\\\/* --- 5. \\\\u5c0f\\\\u5706\\\\u70b9\\\\u6307\\\\u793a\\\\u5668 --- *\\\\/\\\\r\\\\n        .carousel-dots {\\\\r\\\\n            position: absolute;\\\\r\\\\n            bottom: 25px;\\\\r\\\\n            left: 50%;\\\\r\\\\n            transform: translateX(-50%);\\\\r\\\\n            display: flex;\\\\r\\\\n            gap: 12px;\\\\r\\\\n            z-index: 10;\\\\r\\\\n        }\\\\r\\\\n\\\\r\\\\n        .dot {\\\\r\\\\n            border: none;\\\\r\\\\n            width: 12px;\\\\r\\\\n            height: 12px;\\\\r\\\\n            border-radius: 50%;\\\\r\\\\n            background-color: rgba(255, 255, 255, 0.4);\\\\r\\\\n            cursor: pointer;\\\\r\\\\n            transition: background-color 0.2s ease, transform 0.2s ease;\\\\r\\\\n        }\\\\r\\\\n\\\\r\\\\n        .dot.current-dot {\\\\r\\\\n            background-color: white;\\\\r\\\\n            transform: scale(1.2);\\\\r\\\\n        }\\\\r\\\\n        \\\\r\\\\n        \\\\/* --- 6. \\\\u54cd\\\\u5e94\\\\u5f0f\\\\u8bbe\\\\u8ba1 --- *\\\\/\\\\r\\\\n        @media (max-width: 768px) {\\\\r\\\\n            .hero-text h1 { font-size: 2rem; }\\\\r\\\\n            .hero-text p { font-size: 1rem; }\\\\r\\\\n            .arrow { display: none; } \\\\/* \\\\u5728\\\\u624b\\\\u673a\\\\u4e0a\\\\u53ef\\\\u4ee5\\\\u9690\\\\u85cf\\\\u7bad\\\\u5934\\\\uff0c\\\\u53ea\\\\u7528\\\\u5c0f\\\\u5706\\\\u70b9 *\\\\/\\\\r\\\\n        }\\\"}')", "type": "query", "params": [], "bindings": ["en", 45, "{\"html\":\"<div class=\\\"carousel\\\">\\r\\n        <div class=\\\"carousel-track\\\">\\r\\n            <div class=\\\"slide current-slide\\\" style=\\\"background-image: url('https:\\/\\/images.unsplash.com\\/photo-1506744038136-46273834b3fb?q=80&w=2070&auto=format&fit=crop');\\\">\\r\\n                <div class=\\\"hero-text\\\">\\r\\n                    <h1>Like gentle sunshine<\\/h1>\\r\\n                    <p>on quiet leaves<\\/p>\\r\\n                <\\/div>\\r\\n            <\\/div>\\r\\n\\r\\n            <div class=\\\"slide\\\" style=\\\"background-image: url('https:\\/\\/images.unsplash.com\\/photo-1470770841072-f978cf4d019e?q=80&w=2070&auto=format&fit=crop');\\\">\\r\\n                <div class=\\\"hero-text\\\">\\r\\n                    <h1>Whispers of the Forest<\\/h1>\\r\\n                    <p>a story in every branch<\\/p>\\r\\n                <\\/div>\\r\\n            <\\/div>\\r\\n\\r\\n            <div class=\\\"slide\\\" style=\\\"background-image: url('https:\\/\\/images.unsplash.com\\/photo-1439405326853-58f2724f4d31?q=80&w=2070&auto=format&fit=crop');\\\">\\r\\n                <div class=\\\"hero-text\\\">\\r\\n                    <h1>Echoes of the Ocean<\\/h1>\\r\\n                    <p>deep and serene<\\/p>\\r\\n                <\\/div>\\r\\n            <\\/div>\\r\\n            <\\/div>\\r\\n\\r\\n        <button class=\\\"arrow left-arrow\\\">&#10094;<\\/button>\\r\\n        <button class=\\\"arrow right-arrow\\\">&#10095;<\\/button>\\r\\n        \\r\\n        <div class=\\\"carousel-dots\\\">\\r\\n            <\\/div>\\r\\n    <\\/div>\\r\\n\\r\\n\",\"css\":\"\\/* --- 2. \\u8f6e\\u64ad\\u7ec4\\u4ef6\\u4e3b\\u8981\\u6837\\u5f0f --- *\\/\\r\\n        .carousel {\\r\\n            position: relative;\\r\\n            width: 100%;\\r\\n            height: 70vh; \\/* \\u60a8\\u53ef\\u4ee5\\u6839\\u636e\\u9700\\u8981\\u8c03\\u6574\\u9ad8\\u5ea6 *\\/\\r\\n            min-height: 450px;\\r\\n            overflow: hidden; \\/* \\u5173\\u952e\\uff1a\\u9690\\u85cf\\u6240\\u6709\\u5728\\u5bb9\\u5668\\u5916\\u7684\\u5e7b\\u706f\\u7247 *\\/\\r\\n        }\\r\\n\\r\\n        .carousel-track {\\r\\n            position: relative;\\r\\n            height: 100%;\\r\\n            display: flex; \\/* \\u8ba9\\u6240\\u6709\\u5e7b\\u706f\\u7247\\u5728\\u540c\\u4e00\\u884c\\u6392\\u5217 *\\/\\r\\n            transition: transform 0.5s ease-in-out; \\/* \\u5e73\\u6ed1\\u7684\\u6ed1\\u52a8\\u52a8\\u753b *\\/\\r\\n        }\\r\\n\\r\\n        .slide {\\r\\n            flex: 1 0 100%; \\/* \\u6bcf\\u5f20\\u5e7b\\u706f\\u7247\\u5360\\u636e100%\\u7684\\u5bbd\\u5ea6\\u4e14\\u4e0d\\u6536\\u7f29 *\\/\\r\\n            width: 100%;\\r\\n            height: 100%;\\r\\n            background-size: cover;\\r\\n            background-position: center;\\r\\n            display: flex;\\r\\n            justify-content: center;\\r\\n            align-items: center;\\r\\n        }\\r\\n\\r\\n        \\/* --- 3. \\u6587\\u672c\\u548c\\u52a8\\u753b --- *\\/\\r\\n        .hero-text {\\r\\n            text-align: center;\\r\\n            color: white;\\r\\n            max-width: 90%;\\r\\n            text-shadow: 0 2px 8px rgba(0, 0, 0, 0.5); \\/* \\u7ed9\\u6587\\u5b57\\u6dfb\\u52a0\\u9634\\u5f71\\u4ee5\\u589e\\u5f3a\\u53ef\\u8bfb\\u6027 *\\/\\r\\n            opacity: 0; \\/* \\u9ed8\\u8ba4\\u9690\\u85cf\\u6587\\u672c *\\/\\r\\n            transform: translateY(20px);\\r\\n            transition: opacity 0.5s ease-out 0.4s, transform 0.5s ease-out 0.4s; \\/* \\u5ef6\\u8fdf0.4\\u79d2\\u6267\\u884c\\u6587\\u672c\\u52a8\\u753b *\\/\\r\\n        }\\r\\n\\r\\n        .current-slide .hero-text {\\r\\n            opacity: 1; \\/* \\u5f53\\u524d\\u5e7b\\u706f\\u7247\\u7684\\u6587\\u672c\\u663e\\u793a\\u51fa\\u6765 *\\/\\r\\n            transform: translateY(0);\\r\\n        }\\r\\n\\r\\n        .hero-text h1 {\\r\\n            font-size: 3rem;\\r\\n            font-weight: 500;\\r\\n            margin-bottom: 0.5rem;\\r\\n        }\\r\\n\\r\\n        .hero-text p {\\r\\n            font-size: 1.2rem;\\r\\n            font-weight: 300;\\r\\n        }\\r\\n        \\r\\n        \\/* --- 4. \\u5de6\\u53f3\\u7bad\\u5934 --- *\\/\\r\\n        .arrow {\\r\\n            position: absolute;\\r\\n            top: 50%;\\r\\n            transform: translateY(-50%);\\r\\n            background: transparent;\\r\\n            border: 1px solid rgba(255, 255, 255, 0.4);\\r\\n            color: rgba(255, 255, 255, 0.7);\\r\\n            font-size: 1.5rem;\\r\\n            padding: 10px 15px;\\r\\n            border-radius: 50%;\\r\\n            cursor: pointer;\\r\\n            z-index: 10;\\r\\n            transition: all 0.2s ease-in-out;\\r\\n        }\\r\\n        \\r\\n        .arrow:hover {\\r\\n            background-color: rgba(0, 0, 0, 0.3);\\r\\n            color: white;\\r\\n            border-color: transparent;\\r\\n        }\\r\\n\\r\\n        .left-arrow { left: 30px; }\\r\\n        .right-arrow { right: 30px; }\\r\\n        \\r\\n        \\/* --- 5. \\u5c0f\\u5706\\u70b9\\u6307\\u793a\\u5668 --- *\\/\\r\\n        .carousel-dots {\\r\\n            position: absolute;\\r\\n            bottom: 25px;\\r\\n            left: 50%;\\r\\n            transform: translateX(-50%);\\r\\n            display: flex;\\r\\n            gap: 12px;\\r\\n            z-index: 10;\\r\\n        }\\r\\n\\r\\n        .dot {\\r\\n            border: none;\\r\\n            width: 12px;\\r\\n            height: 12px;\\r\\n            border-radius: 50%;\\r\\n            background-color: rgba(255, 255, 255, 0.4);\\r\\n            cursor: pointer;\\r\\n            transition: background-color 0.2s ease, transform 0.2s ease;\\r\\n        }\\r\\n\\r\\n        .dot.current-dot {\\r\\n            background-color: white;\\r\\n            transform: scale(1.2);\\r\\n        }\\r\\n        \\r\\n        \\/* --- 6. \\u54cd\\u5e94\\u5f0f\\u8bbe\\u8ba1 --- *\\/\\r\\n        @media (max-width: 768px) {\\r\\n            .hero-text h1 { font-size: 2rem; }\\r\\n            .hero-text p { font-size: 1rem; }\\r\\n            .arrow { display: none; } \\/* \\u5728\\u624b\\u673a\\u4e0a\\u53ef\\u4ee5\\u9690\\u85cf\\u7bad\\u5934\\uff0c\\u53ea\\u7528\\u5c0f\\u5706\\u70b9 *\\/\\r\\n        }\"}"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 387}, {"index": 16, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 700}, {"index": 24, "namespace": null, "name": "packages/Webkul/Theme/src/Repositories/ThemeCustomizationRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Theme\\src\\Repositories\\ThemeCustomizationRepository.php", "line": 42}, {"index": 25, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Settings/ThemeController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Settings\\ThemeController.php", "line": 117}], "start": **********.5286329, "duration": 0.00215, "duration_str": "2.15ms", "memory": 0, "memory_str": null, "filename": "Translatable.php:387", "source": {"index": 15, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 387}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=387", "ajax": false, "filename": "Translatable.php", "line": "387"}, "connection": "mlk", "explain": null, "start_percent": 30.717, "width_percent": 7.338}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "packages/Webkul/Theme/src/Repositories/ThemeCustomizationRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Theme\\src\\Repositories\\ThemeCustomizationRepository.php", "line": 149}, {"index": 22, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Settings/ThemeController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Settings\\ThemeController.php", "line": 120}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.5355818, "duration": 0.002, "duration_str": "2ms", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 38.055, "width_percent": 6.826}, {"sql": "insert into `theme_customization_translations` (`theme_customization_id`, `locale`, `options`) values (45, 'it', '{\\\"html\\\":\\\"<div class=\\\\\\\"carousel\\\\\\\">\\\\r\\\\n        <div class=\\\\\\\"carousel-track\\\\\\\">\\\\r\\\\n            <div class=\\\\\\\"slide current-slide\\\\\\\" style=\\\\\\\"background-image: url(\\'https:\\\\/\\\\/images.unsplash.com\\\\/photo-1506744038136-46273834b3fb?q=80&w=2070&auto=format&fit=crop\\');\\\\\\\">\\\\r\\\\n                <div class=\\\\\\\"hero-text\\\\\\\">\\\\r\\\\n                    <h1>Like gentle sunshine<\\\\/h1>\\\\r\\\\n                    <p>on quiet leaves<\\\\/p>\\\\r\\\\n                <\\\\/div>\\\\r\\\\n            <\\\\/div>\\\\r\\\\n\\\\r\\\\n            <div class=\\\\\\\"slide\\\\\\\" style=\\\\\\\"background-image: url(\\'https:\\\\/\\\\/images.unsplash.com\\\\/photo-1470770841072-f978cf4d019e?q=80&w=2070&auto=format&fit=crop\\');\\\\\\\">\\\\r\\\\n                <div class=\\\\\\\"hero-text\\\\\\\">\\\\r\\\\n                    <h1>Whispers of the Forest<\\\\/h1>\\\\r\\\\n                    <p>a story in every branch<\\\\/p>\\\\r\\\\n                <\\\\/div>\\\\r\\\\n            <\\\\/div>\\\\r\\\\n\\\\r\\\\n            <div class=\\\\\\\"slide\\\\\\\" style=\\\\\\\"background-image: url(\\'https:\\\\/\\\\/images.unsplash.com\\\\/photo-1439405326853-58f2724f4d31?q=80&w=2070&auto=format&fit=crop\\');\\\\\\\">\\\\r\\\\n                <div class=\\\\\\\"hero-text\\\\\\\">\\\\r\\\\n                    <h1>Echoes of the Ocean<\\\\/h1>\\\\r\\\\n                    <p>deep and serene<\\\\/p>\\\\r\\\\n                <\\\\/div>\\\\r\\\\n            <\\\\/div>\\\\r\\\\n            <\\\\/div>\\\\r\\\\n\\\\r\\\\n        <button class=\\\\\\\"arrow left-arrow\\\\\\\">&#10094;<\\\\/button>\\\\r\\\\n        <button class=\\\\\\\"arrow right-arrow\\\\\\\">&#10095;<\\\\/button>\\\\r\\\\n        \\\\r\\\\n        <div class=\\\\\\\"carousel-dots\\\\\\\">\\\\r\\\\n            <\\\\/div>\\\\r\\\\n    <\\\\/div>\\\\r\\\\n\\\\r\\\\n\\\",\\\"css\\\":\\\"\\\\/* --- 2. \\\\u8f6e\\\\u64ad\\\\u7ec4\\\\u4ef6\\\\u4e3b\\\\u8981\\\\u6837\\\\u5f0f --- *\\\\/\\\\r\\\\n        .carousel {\\\\r\\\\n            position: relative;\\\\r\\\\n            width: 100%;\\\\r\\\\n            height: 70vh; \\\\/* \\\\u60a8\\\\u53ef\\\\u4ee5\\\\u6839\\\\u636e\\\\u9700\\\\u8981\\\\u8c03\\\\u6574\\\\u9ad8\\\\u5ea6 *\\\\/\\\\r\\\\n            min-height: 450px;\\\\r\\\\n            overflow: hidden; \\\\/* \\\\u5173\\\\u952e\\\\uff1a\\\\u9690\\\\u85cf\\\\u6240\\\\u6709\\\\u5728\\\\u5bb9\\\\u5668\\\\u5916\\\\u7684\\\\u5e7b\\\\u706f\\\\u7247 *\\\\/\\\\r\\\\n        }\\\\r\\\\n\\\\r\\\\n        .carousel-track {\\\\r\\\\n            position: relative;\\\\r\\\\n            height: 100%;\\\\r\\\\n            display: flex; \\\\/* \\\\u8ba9\\\\u6240\\\\u6709\\\\u5e7b\\\\u706f\\\\u7247\\\\u5728\\\\u540c\\\\u4e00\\\\u884c\\\\u6392\\\\u5217 *\\\\/\\\\r\\\\n            transition: transform 0.5s ease-in-out; \\\\/* \\\\u5e73\\\\u6ed1\\\\u7684\\\\u6ed1\\\\u52a8\\\\u52a8\\\\u753b *\\\\/\\\\r\\\\n        }\\\\r\\\\n\\\\r\\\\n        .slide {\\\\r\\\\n            flex: 1 0 100%; \\\\/* \\\\u6bcf\\\\u5f20\\\\u5e7b\\\\u706f\\\\u7247\\\\u5360\\\\u636e100%\\\\u7684\\\\u5bbd\\\\u5ea6\\\\u4e14\\\\u4e0d\\\\u6536\\\\u7f29 *\\\\/\\\\r\\\\n            width: 100%;\\\\r\\\\n            height: 100%;\\\\r\\\\n            background-size: cover;\\\\r\\\\n            background-position: center;\\\\r\\\\n            display: flex;\\\\r\\\\n            justify-content: center;\\\\r\\\\n            align-items: center;\\\\r\\\\n        }\\\\r\\\\n\\\\r\\\\n        \\\\/* --- 3. \\\\u6587\\\\u672c\\\\u548c\\\\u52a8\\\\u753b --- *\\\\/\\\\r\\\\n        .hero-text {\\\\r\\\\n            text-align: center;\\\\r\\\\n            color: white;\\\\r\\\\n            max-width: 90%;\\\\r\\\\n            text-shadow: 0 2px 8px rgba(0, 0, 0, 0.5); \\\\/* \\\\u7ed9\\\\u6587\\\\u5b57\\\\u6dfb\\\\u52a0\\\\u9634\\\\u5f71\\\\u4ee5\\\\u589e\\\\u5f3a\\\\u53ef\\\\u8bfb\\\\u6027 *\\\\/\\\\r\\\\n            opacity: 0; \\\\/* \\\\u9ed8\\\\u8ba4\\\\u9690\\\\u85cf\\\\u6587\\\\u672c *\\\\/\\\\r\\\\n            transform: translateY(20px);\\\\r\\\\n            transition: opacity 0.5s ease-out 0.4s, transform 0.5s ease-out 0.4s; \\\\/* \\\\u5ef6\\\\u8fdf0.4\\\\u79d2\\\\u6267\\\\u884c\\\\u6587\\\\u672c\\\\u52a8\\\\u753b *\\\\/\\\\r\\\\n        }\\\\r\\\\n\\\\r\\\\n        .current-slide .hero-text {\\\\r\\\\n            opacity: 1; \\\\/* \\\\u5f53\\\\u524d\\\\u5e7b\\\\u706f\\\\u7247\\\\u7684\\\\u6587\\\\u672c\\\\u663e\\\\u793a\\\\u51fa\\\\u6765 *\\\\/\\\\r\\\\n            transform: translateY(0);\\\\r\\\\n        }\\\\r\\\\n\\\\r\\\\n        .hero-text h1 {\\\\r\\\\n            font-size: 3rem;\\\\r\\\\n            font-weight: 500;\\\\r\\\\n            margin-bottom: 0.5rem;\\\\r\\\\n        }\\\\r\\\\n\\\\r\\\\n        .hero-text p {\\\\r\\\\n            font-size: 1.2rem;\\\\r\\\\n            font-weight: 300;\\\\r\\\\n        }\\\\r\\\\n        \\\\r\\\\n        \\\\/* --- 4. \\\\u5de6\\\\u53f3\\\\u7bad\\\\u5934 --- *\\\\/\\\\r\\\\n        .arrow {\\\\r\\\\n            position: absolute;\\\\r\\\\n            top: 50%;\\\\r\\\\n            transform: translateY(-50%);\\\\r\\\\n            background: transparent;\\\\r\\\\n            border: 1px solid rgba(255, 255, 255, 0.4);\\\\r\\\\n            color: rgba(255, 255, 255, 0.7);\\\\r\\\\n            font-size: 1.5rem;\\\\r\\\\n            padding: 10px 15px;\\\\r\\\\n            border-radius: 50%;\\\\r\\\\n            cursor: pointer;\\\\r\\\\n            z-index: 10;\\\\r\\\\n            transition: all 0.2s ease-in-out;\\\\r\\\\n        }\\\\r\\\\n        \\\\r\\\\n        .arrow:hover {\\\\r\\\\n            background-color: rgba(0, 0, 0, 0.3);\\\\r\\\\n            color: white;\\\\r\\\\n            border-color: transparent;\\\\r\\\\n        }\\\\r\\\\n\\\\r\\\\n        .left-arrow { left: 30px; }\\\\r\\\\n        .right-arrow { right: 30px; }\\\\r\\\\n        \\\\r\\\\n        \\\\/* --- 5. \\\\u5c0f\\\\u5706\\\\u70b9\\\\u6307\\\\u793a\\\\u5668 --- *\\\\/\\\\r\\\\n        .carousel-dots {\\\\r\\\\n            position: absolute;\\\\r\\\\n            bottom: 25px;\\\\r\\\\n            left: 50%;\\\\r\\\\n            transform: translateX(-50%);\\\\r\\\\n            display: flex;\\\\r\\\\n            gap: 12px;\\\\r\\\\n            z-index: 10;\\\\r\\\\n        }\\\\r\\\\n\\\\r\\\\n        .dot {\\\\r\\\\n            border: none;\\\\r\\\\n            width: 12px;\\\\r\\\\n            height: 12px;\\\\r\\\\n            border-radius: 50%;\\\\r\\\\n            background-color: rgba(255, 255, 255, 0.4);\\\\r\\\\n            cursor: pointer;\\\\r\\\\n            transition: background-color 0.2s ease, transform 0.2s ease;\\\\r\\\\n        }\\\\r\\\\n\\\\r\\\\n        .dot.current-dot {\\\\r\\\\n            background-color: white;\\\\r\\\\n            transform: scale(1.2);\\\\r\\\\n        }\\\\r\\\\n        \\\\r\\\\n        \\\\/* --- 6. \\\\u54cd\\\\u5e94\\\\u5f0f\\\\u8bbe\\\\u8ba1 --- *\\\\/\\\\r\\\\n        @media (max-width: 768px) {\\\\r\\\\n            .hero-text h1 { font-size: 2rem; }\\\\r\\\\n            .hero-text p { font-size: 1rem; }\\\\r\\\\n            .arrow { display: none; } \\\\/* \\\\u5728\\\\u624b\\\\u673a\\\\u4e0a\\\\u53ef\\\\u4ee5\\\\u9690\\\\u85cf\\\\u7bad\\\\u5934\\\\uff0c\\\\u53ea\\\\u7528\\\\u5c0f\\\\u5706\\\\u70b9 *\\\\/\\\\r\\\\n        }\\\"}')", "type": "query", "params": [], "bindings": [45, "it", "{\"html\":\"<div class=\\\"carousel\\\">\\r\\n        <div class=\\\"carousel-track\\\">\\r\\n            <div class=\\\"slide current-slide\\\" style=\\\"background-image: url('https:\\/\\/images.unsplash.com\\/photo-1506744038136-46273834b3fb?q=80&w=2070&auto=format&fit=crop');\\\">\\r\\n                <div class=\\\"hero-text\\\">\\r\\n                    <h1>Like gentle sunshine<\\/h1>\\r\\n                    <p>on quiet leaves<\\/p>\\r\\n                <\\/div>\\r\\n            <\\/div>\\r\\n\\r\\n            <div class=\\\"slide\\\" style=\\\"background-image: url('https:\\/\\/images.unsplash.com\\/photo-1470770841072-f978cf4d019e?q=80&w=2070&auto=format&fit=crop');\\\">\\r\\n                <div class=\\\"hero-text\\\">\\r\\n                    <h1>Whispers of the Forest<\\/h1>\\r\\n                    <p>a story in every branch<\\/p>\\r\\n                <\\/div>\\r\\n            <\\/div>\\r\\n\\r\\n            <div class=\\\"slide\\\" style=\\\"background-image: url('https:\\/\\/images.unsplash.com\\/photo-1439405326853-58f2724f4d31?q=80&w=2070&auto=format&fit=crop');\\\">\\r\\n                <div class=\\\"hero-text\\\">\\r\\n                    <h1>Echoes of the Ocean<\\/h1>\\r\\n                    <p>deep and serene<\\/p>\\r\\n                <\\/div>\\r\\n            <\\/div>\\r\\n            <\\/div>\\r\\n\\r\\n        <button class=\\\"arrow left-arrow\\\">&#10094;<\\/button>\\r\\n        <button class=\\\"arrow right-arrow\\\">&#10095;<\\/button>\\r\\n        \\r\\n        <div class=\\\"carousel-dots\\\">\\r\\n            <\\/div>\\r\\n    <\\/div>\\r\\n\\r\\n\",\"css\":\"\\/* --- 2. \\u8f6e\\u64ad\\u7ec4\\u4ef6\\u4e3b\\u8981\\u6837\\u5f0f --- *\\/\\r\\n        .carousel {\\r\\n            position: relative;\\r\\n            width: 100%;\\r\\n            height: 70vh; \\/* \\u60a8\\u53ef\\u4ee5\\u6839\\u636e\\u9700\\u8981\\u8c03\\u6574\\u9ad8\\u5ea6 *\\/\\r\\n            min-height: 450px;\\r\\n            overflow: hidden; \\/* \\u5173\\u952e\\uff1a\\u9690\\u85cf\\u6240\\u6709\\u5728\\u5bb9\\u5668\\u5916\\u7684\\u5e7b\\u706f\\u7247 *\\/\\r\\n        }\\r\\n\\r\\n        .carousel-track {\\r\\n            position: relative;\\r\\n            height: 100%;\\r\\n            display: flex; \\/* \\u8ba9\\u6240\\u6709\\u5e7b\\u706f\\u7247\\u5728\\u540c\\u4e00\\u884c\\u6392\\u5217 *\\/\\r\\n            transition: transform 0.5s ease-in-out; \\/* \\u5e73\\u6ed1\\u7684\\u6ed1\\u52a8\\u52a8\\u753b *\\/\\r\\n        }\\r\\n\\r\\n        .slide {\\r\\n            flex: 1 0 100%; \\/* \\u6bcf\\u5f20\\u5e7b\\u706f\\u7247\\u5360\\u636e100%\\u7684\\u5bbd\\u5ea6\\u4e14\\u4e0d\\u6536\\u7f29 *\\/\\r\\n            width: 100%;\\r\\n            height: 100%;\\r\\n            background-size: cover;\\r\\n            background-position: center;\\r\\n            display: flex;\\r\\n            justify-content: center;\\r\\n            align-items: center;\\r\\n        }\\r\\n\\r\\n        \\/* --- 3. \\u6587\\u672c\\u548c\\u52a8\\u753b --- *\\/\\r\\n        .hero-text {\\r\\n            text-align: center;\\r\\n            color: white;\\r\\n            max-width: 90%;\\r\\n            text-shadow: 0 2px 8px rgba(0, 0, 0, 0.5); \\/* \\u7ed9\\u6587\\u5b57\\u6dfb\\u52a0\\u9634\\u5f71\\u4ee5\\u589e\\u5f3a\\u53ef\\u8bfb\\u6027 *\\/\\r\\n            opacity: 0; \\/* \\u9ed8\\u8ba4\\u9690\\u85cf\\u6587\\u672c *\\/\\r\\n            transform: translateY(20px);\\r\\n            transition: opacity 0.5s ease-out 0.4s, transform 0.5s ease-out 0.4s; \\/* \\u5ef6\\u8fdf0.4\\u79d2\\u6267\\u884c\\u6587\\u672c\\u52a8\\u753b *\\/\\r\\n        }\\r\\n\\r\\n        .current-slide .hero-text {\\r\\n            opacity: 1; \\/* \\u5f53\\u524d\\u5e7b\\u706f\\u7247\\u7684\\u6587\\u672c\\u663e\\u793a\\u51fa\\u6765 *\\/\\r\\n            transform: translateY(0);\\r\\n        }\\r\\n\\r\\n        .hero-text h1 {\\r\\n            font-size: 3rem;\\r\\n            font-weight: 500;\\r\\n            margin-bottom: 0.5rem;\\r\\n        }\\r\\n\\r\\n        .hero-text p {\\r\\n            font-size: 1.2rem;\\r\\n            font-weight: 300;\\r\\n        }\\r\\n        \\r\\n        \\/* --- 4. \\u5de6\\u53f3\\u7bad\\u5934 --- *\\/\\r\\n        .arrow {\\r\\n            position: absolute;\\r\\n            top: 50%;\\r\\n            transform: translateY(-50%);\\r\\n            background: transparent;\\r\\n            border: 1px solid rgba(255, 255, 255, 0.4);\\r\\n            color: rgba(255, 255, 255, 0.7);\\r\\n            font-size: 1.5rem;\\r\\n            padding: 10px 15px;\\r\\n            border-radius: 50%;\\r\\n            cursor: pointer;\\r\\n            z-index: 10;\\r\\n            transition: all 0.2s ease-in-out;\\r\\n        }\\r\\n        \\r\\n        .arrow:hover {\\r\\n            background-color: rgba(0, 0, 0, 0.3);\\r\\n            color: white;\\r\\n            border-color: transparent;\\r\\n        }\\r\\n\\r\\n        .left-arrow { left: 30px; }\\r\\n        .right-arrow { right: 30px; }\\r\\n        \\r\\n        \\/* --- 5. \\u5c0f\\u5706\\u70b9\\u6307\\u793a\\u5668 --- *\\/\\r\\n        .carousel-dots {\\r\\n            position: absolute;\\r\\n            bottom: 25px;\\r\\n            left: 50%;\\r\\n            transform: translateX(-50%);\\r\\n            display: flex;\\r\\n            gap: 12px;\\r\\n            z-index: 10;\\r\\n        }\\r\\n\\r\\n        .dot {\\r\\n            border: none;\\r\\n            width: 12px;\\r\\n            height: 12px;\\r\\n            border-radius: 50%;\\r\\n            background-color: rgba(255, 255, 255, 0.4);\\r\\n            cursor: pointer;\\r\\n            transition: background-color 0.2s ease, transform 0.2s ease;\\r\\n        }\\r\\n\\r\\n        .dot.current-dot {\\r\\n            background-color: white;\\r\\n            transform: scale(1.2);\\r\\n        }\\r\\n        \\r\\n        \\/* --- 6. \\u54cd\\u5e94\\u5f0f\\u8bbe\\u8ba1 --- *\\/\\r\\n        @media (max-width: 768px) {\\r\\n            .hero-text h1 { font-size: 2rem; }\\r\\n            .hero-text p { font-size: 1rem; }\\r\\n            .arrow { display: none; } \\/* \\u5728\\u624b\\u673a\\u4e0a\\u53ef\\u4ee5\\u9690\\u85cf\\u7bad\\u5934\\uff0c\\u53ea\\u7528\\u5c0f\\u5706\\u70b9 *\\/\\r\\n        }\"}"], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "packages/Webkul/Theme/src/Repositories/ThemeCustomizationRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Theme\\src\\Repositories\\ThemeCustomizationRepository.php", "line": 172}, {"index": 19, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Settings/ThemeController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Settings\\ThemeController.php", "line": 120}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.539199, "duration": 0.00401, "duration_str": "4.01ms", "memory": 0, "memory_str": null, "filename": "ThemeCustomizationRepository.php:172", "source": {"index": 18, "namespace": null, "name": "packages/Webkul/Theme/src/Repositories/ThemeCustomizationRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Theme\\src\\Repositories\\ThemeCustomizationRepository.php", "line": 172}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FTheme%2Fsrc%2FRepositories%2FThemeCustomizationRepository.php&line=172", "ajax": false, "filename": "ThemeCustomizationRepository.php", "line": "172"}, "connection": "mlk", "explain": null, "start_percent": 44.881, "width_percent": 13.686}, {"sql": "insert into `theme_customization_translations` (`theme_customization_id`, `locale`, `options`) values (45, 'de', '{\\\"html\\\":\\\"<div class=\\\\\\\"carousel\\\\\\\">\\\\r\\\\n        <div class=\\\\\\\"carousel-track\\\\\\\">\\\\r\\\\n            <div class=\\\\\\\"slide current-slide\\\\\\\" style=\\\\\\\"background-image: url(\\'https:\\\\/\\\\/images.unsplash.com\\\\/photo-1506744038136-46273834b3fb?q=80&w=2070&auto=format&fit=crop\\');\\\\\\\">\\\\r\\\\n                <div class=\\\\\\\"hero-text\\\\\\\">\\\\r\\\\n                    <h1>Like gentle sunshine<\\\\/h1>\\\\r\\\\n                    <p>on quiet leaves<\\\\/p>\\\\r\\\\n                <\\\\/div>\\\\r\\\\n            <\\\\/div>\\\\r\\\\n\\\\r\\\\n            <div class=\\\\\\\"slide\\\\\\\" style=\\\\\\\"background-image: url(\\'https:\\\\/\\\\/images.unsplash.com\\\\/photo-1470770841072-f978cf4d019e?q=80&w=2070&auto=format&fit=crop\\');\\\\\\\">\\\\r\\\\n                <div class=\\\\\\\"hero-text\\\\\\\">\\\\r\\\\n                    <h1>Whispers of the Forest<\\\\/h1>\\\\r\\\\n                    <p>a story in every branch<\\\\/p>\\\\r\\\\n                <\\\\/div>\\\\r\\\\n            <\\\\/div>\\\\r\\\\n\\\\r\\\\n            <div class=\\\\\\\"slide\\\\\\\" style=\\\\\\\"background-image: url(\\'https:\\\\/\\\\/images.unsplash.com\\\\/photo-1439405326853-58f2724f4d31?q=80&w=2070&auto=format&fit=crop\\');\\\\\\\">\\\\r\\\\n                <div class=\\\\\\\"hero-text\\\\\\\">\\\\r\\\\n                    <h1>Echoes of the Ocean<\\\\/h1>\\\\r\\\\n                    <p>deep and serene<\\\\/p>\\\\r\\\\n                <\\\\/div>\\\\r\\\\n            <\\\\/div>\\\\r\\\\n            <\\\\/div>\\\\r\\\\n\\\\r\\\\n        <button class=\\\\\\\"arrow left-arrow\\\\\\\">&#10094;<\\\\/button>\\\\r\\\\n        <button class=\\\\\\\"arrow right-arrow\\\\\\\">&#10095;<\\\\/button>\\\\r\\\\n        \\\\r\\\\n        <div class=\\\\\\\"carousel-dots\\\\\\\">\\\\r\\\\n            <\\\\/div>\\\\r\\\\n    <\\\\/div>\\\\r\\\\n\\\\r\\\\n\\\",\\\"css\\\":\\\"\\\\/* --- 2. \\\\u8f6e\\\\u64ad\\\\u7ec4\\\\u4ef6\\\\u4e3b\\\\u8981\\\\u6837\\\\u5f0f --- *\\\\/\\\\r\\\\n        .carousel {\\\\r\\\\n            position: relative;\\\\r\\\\n            width: 100%;\\\\r\\\\n            height: 70vh; \\\\/* \\\\u60a8\\\\u53ef\\\\u4ee5\\\\u6839\\\\u636e\\\\u9700\\\\u8981\\\\u8c03\\\\u6574\\\\u9ad8\\\\u5ea6 *\\\\/\\\\r\\\\n            min-height: 450px;\\\\r\\\\n            overflow: hidden; \\\\/* \\\\u5173\\\\u952e\\\\uff1a\\\\u9690\\\\u85cf\\\\u6240\\\\u6709\\\\u5728\\\\u5bb9\\\\u5668\\\\u5916\\\\u7684\\\\u5e7b\\\\u706f\\\\u7247 *\\\\/\\\\r\\\\n        }\\\\r\\\\n\\\\r\\\\n        .carousel-track {\\\\r\\\\n            position: relative;\\\\r\\\\n            height: 100%;\\\\r\\\\n            display: flex; \\\\/* \\\\u8ba9\\\\u6240\\\\u6709\\\\u5e7b\\\\u706f\\\\u7247\\\\u5728\\\\u540c\\\\u4e00\\\\u884c\\\\u6392\\\\u5217 *\\\\/\\\\r\\\\n            transition: transform 0.5s ease-in-out; \\\\/* \\\\u5e73\\\\u6ed1\\\\u7684\\\\u6ed1\\\\u52a8\\\\u52a8\\\\u753b *\\\\/\\\\r\\\\n        }\\\\r\\\\n\\\\r\\\\n        .slide {\\\\r\\\\n            flex: 1 0 100%; \\\\/* \\\\u6bcf\\\\u5f20\\\\u5e7b\\\\u706f\\\\u7247\\\\u5360\\\\u636e100%\\\\u7684\\\\u5bbd\\\\u5ea6\\\\u4e14\\\\u4e0d\\\\u6536\\\\u7f29 *\\\\/\\\\r\\\\n            width: 100%;\\\\r\\\\n            height: 100%;\\\\r\\\\n            background-size: cover;\\\\r\\\\n            background-position: center;\\\\r\\\\n            display: flex;\\\\r\\\\n            justify-content: center;\\\\r\\\\n            align-items: center;\\\\r\\\\n        }\\\\r\\\\n\\\\r\\\\n        \\\\/* --- 3. \\\\u6587\\\\u672c\\\\u548c\\\\u52a8\\\\u753b --- *\\\\/\\\\r\\\\n        .hero-text {\\\\r\\\\n            text-align: center;\\\\r\\\\n            color: white;\\\\r\\\\n            max-width: 90%;\\\\r\\\\n            text-shadow: 0 2px 8px rgba(0, 0, 0, 0.5); \\\\/* \\\\u7ed9\\\\u6587\\\\u5b57\\\\u6dfb\\\\u52a0\\\\u9634\\\\u5f71\\\\u4ee5\\\\u589e\\\\u5f3a\\\\u53ef\\\\u8bfb\\\\u6027 *\\\\/\\\\r\\\\n            opacity: 0; \\\\/* \\\\u9ed8\\\\u8ba4\\\\u9690\\\\u85cf\\\\u6587\\\\u672c *\\\\/\\\\r\\\\n            transform: translateY(20px);\\\\r\\\\n            transition: opacity 0.5s ease-out 0.4s, transform 0.5s ease-out 0.4s; \\\\/* \\\\u5ef6\\\\u8fdf0.4\\\\u79d2\\\\u6267\\\\u884c\\\\u6587\\\\u672c\\\\u52a8\\\\u753b *\\\\/\\\\r\\\\n        }\\\\r\\\\n\\\\r\\\\n        .current-slide .hero-text {\\\\r\\\\n            opacity: 1; \\\\/* \\\\u5f53\\\\u524d\\\\u5e7b\\\\u706f\\\\u7247\\\\u7684\\\\u6587\\\\u672c\\\\u663e\\\\u793a\\\\u51fa\\\\u6765 *\\\\/\\\\r\\\\n            transform: translateY(0);\\\\r\\\\n        }\\\\r\\\\n\\\\r\\\\n        .hero-text h1 {\\\\r\\\\n            font-size: 3rem;\\\\r\\\\n            font-weight: 500;\\\\r\\\\n            margin-bottom: 0.5rem;\\\\r\\\\n        }\\\\r\\\\n\\\\r\\\\n        .hero-text p {\\\\r\\\\n            font-size: 1.2rem;\\\\r\\\\n            font-weight: 300;\\\\r\\\\n        }\\\\r\\\\n        \\\\r\\\\n        \\\\/* --- 4. \\\\u5de6\\\\u53f3\\\\u7bad\\\\u5934 --- *\\\\/\\\\r\\\\n        .arrow {\\\\r\\\\n            position: absolute;\\\\r\\\\n            top: 50%;\\\\r\\\\n            transform: translateY(-50%);\\\\r\\\\n            background: transparent;\\\\r\\\\n            border: 1px solid rgba(255, 255, 255, 0.4);\\\\r\\\\n            color: rgba(255, 255, 255, 0.7);\\\\r\\\\n            font-size: 1.5rem;\\\\r\\\\n            padding: 10px 15px;\\\\r\\\\n            border-radius: 50%;\\\\r\\\\n            cursor: pointer;\\\\r\\\\n            z-index: 10;\\\\r\\\\n            transition: all 0.2s ease-in-out;\\\\r\\\\n        }\\\\r\\\\n        \\\\r\\\\n        .arrow:hover {\\\\r\\\\n            background-color: rgba(0, 0, 0, 0.3);\\\\r\\\\n            color: white;\\\\r\\\\n            border-color: transparent;\\\\r\\\\n        }\\\\r\\\\n\\\\r\\\\n        .left-arrow { left: 30px; }\\\\r\\\\n        .right-arrow { right: 30px; }\\\\r\\\\n        \\\\r\\\\n        \\\\/* --- 5. \\\\u5c0f\\\\u5706\\\\u70b9\\\\u6307\\\\u793a\\\\u5668 --- *\\\\/\\\\r\\\\n        .carousel-dots {\\\\r\\\\n            position: absolute;\\\\r\\\\n            bottom: 25px;\\\\r\\\\n            left: 50%;\\\\r\\\\n            transform: translateX(-50%);\\\\r\\\\n            display: flex;\\\\r\\\\n            gap: 12px;\\\\r\\\\n            z-index: 10;\\\\r\\\\n        }\\\\r\\\\n\\\\r\\\\n        .dot {\\\\r\\\\n            border: none;\\\\r\\\\n            width: 12px;\\\\r\\\\n            height: 12px;\\\\r\\\\n            border-radius: 50%;\\\\r\\\\n            background-color: rgba(255, 255, 255, 0.4);\\\\r\\\\n            cursor: pointer;\\\\r\\\\n            transition: background-color 0.2s ease, transform 0.2s ease;\\\\r\\\\n        }\\\\r\\\\n\\\\r\\\\n        .dot.current-dot {\\\\r\\\\n            background-color: white;\\\\r\\\\n            transform: scale(1.2);\\\\r\\\\n        }\\\\r\\\\n        \\\\r\\\\n        \\\\/* --- 6. \\\\u54cd\\\\u5e94\\\\u5f0f\\\\u8bbe\\\\u8ba1 --- *\\\\/\\\\r\\\\n        @media (max-width: 768px) {\\\\r\\\\n            .hero-text h1 { font-size: 2rem; }\\\\r\\\\n            .hero-text p { font-size: 1rem; }\\\\r\\\\n            .arrow { display: none; } \\\\/* \\\\u5728\\\\u624b\\\\u673a\\\\u4e0a\\\\u53ef\\\\u4ee5\\\\u9690\\\\u85cf\\\\u7bad\\\\u5934\\\\uff0c\\\\u53ea\\\\u7528\\\\u5c0f\\\\u5706\\\\u70b9 *\\\\/\\\\r\\\\n        }\\\"}')", "type": "query", "params": [], "bindings": [45, "de", "{\"html\":\"<div class=\\\"carousel\\\">\\r\\n        <div class=\\\"carousel-track\\\">\\r\\n            <div class=\\\"slide current-slide\\\" style=\\\"background-image: url('https:\\/\\/images.unsplash.com\\/photo-1506744038136-46273834b3fb?q=80&w=2070&auto=format&fit=crop');\\\">\\r\\n                <div class=\\\"hero-text\\\">\\r\\n                    <h1>Like gentle sunshine<\\/h1>\\r\\n                    <p>on quiet leaves<\\/p>\\r\\n                <\\/div>\\r\\n            <\\/div>\\r\\n\\r\\n            <div class=\\\"slide\\\" style=\\\"background-image: url('https:\\/\\/images.unsplash.com\\/photo-1470770841072-f978cf4d019e?q=80&w=2070&auto=format&fit=crop');\\\">\\r\\n                <div class=\\\"hero-text\\\">\\r\\n                    <h1>Whispers of the Forest<\\/h1>\\r\\n                    <p>a story in every branch<\\/p>\\r\\n                <\\/div>\\r\\n            <\\/div>\\r\\n\\r\\n            <div class=\\\"slide\\\" style=\\\"background-image: url('https:\\/\\/images.unsplash.com\\/photo-1439405326853-58f2724f4d31?q=80&w=2070&auto=format&fit=crop');\\\">\\r\\n                <div class=\\\"hero-text\\\">\\r\\n                    <h1>Echoes of the Ocean<\\/h1>\\r\\n                    <p>deep and serene<\\/p>\\r\\n                <\\/div>\\r\\n            <\\/div>\\r\\n            <\\/div>\\r\\n\\r\\n        <button class=\\\"arrow left-arrow\\\">&#10094;<\\/button>\\r\\n        <button class=\\\"arrow right-arrow\\\">&#10095;<\\/button>\\r\\n        \\r\\n        <div class=\\\"carousel-dots\\\">\\r\\n            <\\/div>\\r\\n    <\\/div>\\r\\n\\r\\n\",\"css\":\"\\/* --- 2. \\u8f6e\\u64ad\\u7ec4\\u4ef6\\u4e3b\\u8981\\u6837\\u5f0f --- *\\/\\r\\n        .carousel {\\r\\n            position: relative;\\r\\n            width: 100%;\\r\\n            height: 70vh; \\/* \\u60a8\\u53ef\\u4ee5\\u6839\\u636e\\u9700\\u8981\\u8c03\\u6574\\u9ad8\\u5ea6 *\\/\\r\\n            min-height: 450px;\\r\\n            overflow: hidden; \\/* \\u5173\\u952e\\uff1a\\u9690\\u85cf\\u6240\\u6709\\u5728\\u5bb9\\u5668\\u5916\\u7684\\u5e7b\\u706f\\u7247 *\\/\\r\\n        }\\r\\n\\r\\n        .carousel-track {\\r\\n            position: relative;\\r\\n            height: 100%;\\r\\n            display: flex; \\/* \\u8ba9\\u6240\\u6709\\u5e7b\\u706f\\u7247\\u5728\\u540c\\u4e00\\u884c\\u6392\\u5217 *\\/\\r\\n            transition: transform 0.5s ease-in-out; \\/* \\u5e73\\u6ed1\\u7684\\u6ed1\\u52a8\\u52a8\\u753b *\\/\\r\\n        }\\r\\n\\r\\n        .slide {\\r\\n            flex: 1 0 100%; \\/* \\u6bcf\\u5f20\\u5e7b\\u706f\\u7247\\u5360\\u636e100%\\u7684\\u5bbd\\u5ea6\\u4e14\\u4e0d\\u6536\\u7f29 *\\/\\r\\n            width: 100%;\\r\\n            height: 100%;\\r\\n            background-size: cover;\\r\\n            background-position: center;\\r\\n            display: flex;\\r\\n            justify-content: center;\\r\\n            align-items: center;\\r\\n        }\\r\\n\\r\\n        \\/* --- 3. \\u6587\\u672c\\u548c\\u52a8\\u753b --- *\\/\\r\\n        .hero-text {\\r\\n            text-align: center;\\r\\n            color: white;\\r\\n            max-width: 90%;\\r\\n            text-shadow: 0 2px 8px rgba(0, 0, 0, 0.5); \\/* \\u7ed9\\u6587\\u5b57\\u6dfb\\u52a0\\u9634\\u5f71\\u4ee5\\u589e\\u5f3a\\u53ef\\u8bfb\\u6027 *\\/\\r\\n            opacity: 0; \\/* \\u9ed8\\u8ba4\\u9690\\u85cf\\u6587\\u672c *\\/\\r\\n            transform: translateY(20px);\\r\\n            transition: opacity 0.5s ease-out 0.4s, transform 0.5s ease-out 0.4s; \\/* \\u5ef6\\u8fdf0.4\\u79d2\\u6267\\u884c\\u6587\\u672c\\u52a8\\u753b *\\/\\r\\n        }\\r\\n\\r\\n        .current-slide .hero-text {\\r\\n            opacity: 1; \\/* \\u5f53\\u524d\\u5e7b\\u706f\\u7247\\u7684\\u6587\\u672c\\u663e\\u793a\\u51fa\\u6765 *\\/\\r\\n            transform: translateY(0);\\r\\n        }\\r\\n\\r\\n        .hero-text h1 {\\r\\n            font-size: 3rem;\\r\\n            font-weight: 500;\\r\\n            margin-bottom: 0.5rem;\\r\\n        }\\r\\n\\r\\n        .hero-text p {\\r\\n            font-size: 1.2rem;\\r\\n            font-weight: 300;\\r\\n        }\\r\\n        \\r\\n        \\/* --- 4. \\u5de6\\u53f3\\u7bad\\u5934 --- *\\/\\r\\n        .arrow {\\r\\n            position: absolute;\\r\\n            top: 50%;\\r\\n            transform: translateY(-50%);\\r\\n            background: transparent;\\r\\n            border: 1px solid rgba(255, 255, 255, 0.4);\\r\\n            color: rgba(255, 255, 255, 0.7);\\r\\n            font-size: 1.5rem;\\r\\n            padding: 10px 15px;\\r\\n            border-radius: 50%;\\r\\n            cursor: pointer;\\r\\n            z-index: 10;\\r\\n            transition: all 0.2s ease-in-out;\\r\\n        }\\r\\n        \\r\\n        .arrow:hover {\\r\\n            background-color: rgba(0, 0, 0, 0.3);\\r\\n            color: white;\\r\\n            border-color: transparent;\\r\\n        }\\r\\n\\r\\n        .left-arrow { left: 30px; }\\r\\n        .right-arrow { right: 30px; }\\r\\n        \\r\\n        \\/* --- 5. \\u5c0f\\u5706\\u70b9\\u6307\\u793a\\u5668 --- *\\/\\r\\n        .carousel-dots {\\r\\n            position: absolute;\\r\\n            bottom: 25px;\\r\\n            left: 50%;\\r\\n            transform: translateX(-50%);\\r\\n            display: flex;\\r\\n            gap: 12px;\\r\\n            z-index: 10;\\r\\n        }\\r\\n\\r\\n        .dot {\\r\\n            border: none;\\r\\n            width: 12px;\\r\\n            height: 12px;\\r\\n            border-radius: 50%;\\r\\n            background-color: rgba(255, 255, 255, 0.4);\\r\\n            cursor: pointer;\\r\\n            transition: background-color 0.2s ease, transform 0.2s ease;\\r\\n        }\\r\\n\\r\\n        .dot.current-dot {\\r\\n            background-color: white;\\r\\n            transform: scale(1.2);\\r\\n        }\\r\\n        \\r\\n        \\/* --- 6. \\u54cd\\u5e94\\u5f0f\\u8bbe\\u8ba1 --- *\\/\\r\\n        @media (max-width: 768px) {\\r\\n            .hero-text h1 { font-size: 2rem; }\\r\\n            .hero-text p { font-size: 1rem; }\\r\\n            .arrow { display: none; } \\/* \\u5728\\u624b\\u673a\\u4e0a\\u53ef\\u4ee5\\u9690\\u85cf\\u7bad\\u5934\\uff0c\\u53ea\\u7528\\u5c0f\\u5706\\u70b9 *\\/\\r\\n        }\"}"], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "packages/Webkul/Theme/src/Repositories/ThemeCustomizationRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Theme\\src\\Repositories\\ThemeCustomizationRepository.php", "line": 172}, {"index": 19, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Settings/ThemeController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Settings\\ThemeController.php", "line": 120}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.544289, "duration": 0.00207, "duration_str": "2.07ms", "memory": 0, "memory_str": null, "filename": "ThemeCustomizationRepository.php:172", "source": {"index": 18, "namespace": null, "name": "packages/Webkul/Theme/src/Repositories/ThemeCustomizationRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Theme\\src\\Repositories\\ThemeCustomizationRepository.php", "line": 172}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FTheme%2Fsrc%2FRepositories%2FThemeCustomizationRepository.php&line=172", "ajax": false, "filename": "ThemeCustomizationRepository.php", "line": "172"}, "connection": "mlk", "explain": null, "start_percent": 58.567, "width_percent": 7.065}, {"sql": "insert into `theme_customization_translations` (`theme_customization_id`, `locale`, `options`) values (45, 'fr', '{\\\"html\\\":\\\"<div class=\\\\\\\"carousel\\\\\\\">\\\\r\\\\n        <div class=\\\\\\\"carousel-track\\\\\\\">\\\\r\\\\n            <div class=\\\\\\\"slide current-slide\\\\\\\" style=\\\\\\\"background-image: url(\\'https:\\\\/\\\\/images.unsplash.com\\\\/photo-1506744038136-46273834b3fb?q=80&w=2070&auto=format&fit=crop\\');\\\\\\\">\\\\r\\\\n                <div class=\\\\\\\"hero-text\\\\\\\">\\\\r\\\\n                    <h1>Like gentle sunshine<\\\\/h1>\\\\r\\\\n                    <p>on quiet leaves<\\\\/p>\\\\r\\\\n                <\\\\/div>\\\\r\\\\n            <\\\\/div>\\\\r\\\\n\\\\r\\\\n            <div class=\\\\\\\"slide\\\\\\\" style=\\\\\\\"background-image: url(\\'https:\\\\/\\\\/images.unsplash.com\\\\/photo-1470770841072-f978cf4d019e?q=80&w=2070&auto=format&fit=crop\\');\\\\\\\">\\\\r\\\\n                <div class=\\\\\\\"hero-text\\\\\\\">\\\\r\\\\n                    <h1>Whispers of the Forest<\\\\/h1>\\\\r\\\\n                    <p>a story in every branch<\\\\/p>\\\\r\\\\n                <\\\\/div>\\\\r\\\\n            <\\\\/div>\\\\r\\\\n\\\\r\\\\n            <div class=\\\\\\\"slide\\\\\\\" style=\\\\\\\"background-image: url(\\'https:\\\\/\\\\/images.unsplash.com\\\\/photo-1439405326853-58f2724f4d31?q=80&w=2070&auto=format&fit=crop\\');\\\\\\\">\\\\r\\\\n                <div class=\\\\\\\"hero-text\\\\\\\">\\\\r\\\\n                    <h1>Echoes of the Ocean<\\\\/h1>\\\\r\\\\n                    <p>deep and serene<\\\\/p>\\\\r\\\\n                <\\\\/div>\\\\r\\\\n            <\\\\/div>\\\\r\\\\n            <\\\\/div>\\\\r\\\\n\\\\r\\\\n        <button class=\\\\\\\"arrow left-arrow\\\\\\\">&#10094;<\\\\/button>\\\\r\\\\n        <button class=\\\\\\\"arrow right-arrow\\\\\\\">&#10095;<\\\\/button>\\\\r\\\\n        \\\\r\\\\n        <div class=\\\\\\\"carousel-dots\\\\\\\">\\\\r\\\\n            <\\\\/div>\\\\r\\\\n    <\\\\/div>\\\\r\\\\n\\\\r\\\\n\\\",\\\"css\\\":\\\"\\\\/* --- 2. \\\\u8f6e\\\\u64ad\\\\u7ec4\\\\u4ef6\\\\u4e3b\\\\u8981\\\\u6837\\\\u5f0f --- *\\\\/\\\\r\\\\n        .carousel {\\\\r\\\\n            position: relative;\\\\r\\\\n            width: 100%;\\\\r\\\\n            height: 70vh; \\\\/* \\\\u60a8\\\\u53ef\\\\u4ee5\\\\u6839\\\\u636e\\\\u9700\\\\u8981\\\\u8c03\\\\u6574\\\\u9ad8\\\\u5ea6 *\\\\/\\\\r\\\\n            min-height: 450px;\\\\r\\\\n            overflow: hidden; \\\\/* \\\\u5173\\\\u952e\\\\uff1a\\\\u9690\\\\u85cf\\\\u6240\\\\u6709\\\\u5728\\\\u5bb9\\\\u5668\\\\u5916\\\\u7684\\\\u5e7b\\\\u706f\\\\u7247 *\\\\/\\\\r\\\\n        }\\\\r\\\\n\\\\r\\\\n        .carousel-track {\\\\r\\\\n            position: relative;\\\\r\\\\n            height: 100%;\\\\r\\\\n            display: flex; \\\\/* \\\\u8ba9\\\\u6240\\\\u6709\\\\u5e7b\\\\u706f\\\\u7247\\\\u5728\\\\u540c\\\\u4e00\\\\u884c\\\\u6392\\\\u5217 *\\\\/\\\\r\\\\n            transition: transform 0.5s ease-in-out; \\\\/* \\\\u5e73\\\\u6ed1\\\\u7684\\\\u6ed1\\\\u52a8\\\\u52a8\\\\u753b *\\\\/\\\\r\\\\n        }\\\\r\\\\n\\\\r\\\\n        .slide {\\\\r\\\\n            flex: 1 0 100%; \\\\/* \\\\u6bcf\\\\u5f20\\\\u5e7b\\\\u706f\\\\u7247\\\\u5360\\\\u636e100%\\\\u7684\\\\u5bbd\\\\u5ea6\\\\u4e14\\\\u4e0d\\\\u6536\\\\u7f29 *\\\\/\\\\r\\\\n            width: 100%;\\\\r\\\\n            height: 100%;\\\\r\\\\n            background-size: cover;\\\\r\\\\n            background-position: center;\\\\r\\\\n            display: flex;\\\\r\\\\n            justify-content: center;\\\\r\\\\n            align-items: center;\\\\r\\\\n        }\\\\r\\\\n\\\\r\\\\n        \\\\/* --- 3. \\\\u6587\\\\u672c\\\\u548c\\\\u52a8\\\\u753b --- *\\\\/\\\\r\\\\n        .hero-text {\\\\r\\\\n            text-align: center;\\\\r\\\\n            color: white;\\\\r\\\\n            max-width: 90%;\\\\r\\\\n            text-shadow: 0 2px 8px rgba(0, 0, 0, 0.5); \\\\/* \\\\u7ed9\\\\u6587\\\\u5b57\\\\u6dfb\\\\u52a0\\\\u9634\\\\u5f71\\\\u4ee5\\\\u589e\\\\u5f3a\\\\u53ef\\\\u8bfb\\\\u6027 *\\\\/\\\\r\\\\n            opacity: 0; \\\\/* \\\\u9ed8\\\\u8ba4\\\\u9690\\\\u85cf\\\\u6587\\\\u672c *\\\\/\\\\r\\\\n            transform: translateY(20px);\\\\r\\\\n            transition: opacity 0.5s ease-out 0.4s, transform 0.5s ease-out 0.4s; \\\\/* \\\\u5ef6\\\\u8fdf0.4\\\\u79d2\\\\u6267\\\\u884c\\\\u6587\\\\u672c\\\\u52a8\\\\u753b *\\\\/\\\\r\\\\n        }\\\\r\\\\n\\\\r\\\\n        .current-slide .hero-text {\\\\r\\\\n            opacity: 1; \\\\/* \\\\u5f53\\\\u524d\\\\u5e7b\\\\u706f\\\\u7247\\\\u7684\\\\u6587\\\\u672c\\\\u663e\\\\u793a\\\\u51fa\\\\u6765 *\\\\/\\\\r\\\\n            transform: translateY(0);\\\\r\\\\n        }\\\\r\\\\n\\\\r\\\\n        .hero-text h1 {\\\\r\\\\n            font-size: 3rem;\\\\r\\\\n            font-weight: 500;\\\\r\\\\n            margin-bottom: 0.5rem;\\\\r\\\\n        }\\\\r\\\\n\\\\r\\\\n        .hero-text p {\\\\r\\\\n            font-size: 1.2rem;\\\\r\\\\n            font-weight: 300;\\\\r\\\\n        }\\\\r\\\\n        \\\\r\\\\n        \\\\/* --- 4. \\\\u5de6\\\\u53f3\\\\u7bad\\\\u5934 --- *\\\\/\\\\r\\\\n        .arrow {\\\\r\\\\n            position: absolute;\\\\r\\\\n            top: 50%;\\\\r\\\\n            transform: translateY(-50%);\\\\r\\\\n            background: transparent;\\\\r\\\\n            border: 1px solid rgba(255, 255, 255, 0.4);\\\\r\\\\n            color: rgba(255, 255, 255, 0.7);\\\\r\\\\n            font-size: 1.5rem;\\\\r\\\\n            padding: 10px 15px;\\\\r\\\\n            border-radius: 50%;\\\\r\\\\n            cursor: pointer;\\\\r\\\\n            z-index: 10;\\\\r\\\\n            transition: all 0.2s ease-in-out;\\\\r\\\\n        }\\\\r\\\\n        \\\\r\\\\n        .arrow:hover {\\\\r\\\\n            background-color: rgba(0, 0, 0, 0.3);\\\\r\\\\n            color: white;\\\\r\\\\n            border-color: transparent;\\\\r\\\\n        }\\\\r\\\\n\\\\r\\\\n        .left-arrow { left: 30px; }\\\\r\\\\n        .right-arrow { right: 30px; }\\\\r\\\\n        \\\\r\\\\n        \\\\/* --- 5. \\\\u5c0f\\\\u5706\\\\u70b9\\\\u6307\\\\u793a\\\\u5668 --- *\\\\/\\\\r\\\\n        .carousel-dots {\\\\r\\\\n            position: absolute;\\\\r\\\\n            bottom: 25px;\\\\r\\\\n            left: 50%;\\\\r\\\\n            transform: translateX(-50%);\\\\r\\\\n            display: flex;\\\\r\\\\n            gap: 12px;\\\\r\\\\n            z-index: 10;\\\\r\\\\n        }\\\\r\\\\n\\\\r\\\\n        .dot {\\\\r\\\\n            border: none;\\\\r\\\\n            width: 12px;\\\\r\\\\n            height: 12px;\\\\r\\\\n            border-radius: 50%;\\\\r\\\\n            background-color: rgba(255, 255, 255, 0.4);\\\\r\\\\n            cursor: pointer;\\\\r\\\\n            transition: background-color 0.2s ease, transform 0.2s ease;\\\\r\\\\n        }\\\\r\\\\n\\\\r\\\\n        .dot.current-dot {\\\\r\\\\n            background-color: white;\\\\r\\\\n            transform: scale(1.2);\\\\r\\\\n        }\\\\r\\\\n        \\\\r\\\\n        \\\\/* --- 6. \\\\u54cd\\\\u5e94\\\\u5f0f\\\\u8bbe\\\\u8ba1 --- *\\\\/\\\\r\\\\n        @media (max-width: 768px) {\\\\r\\\\n            .hero-text h1 { font-size: 2rem; }\\\\r\\\\n            .hero-text p { font-size: 1rem; }\\\\r\\\\n            .arrow { display: none; } \\\\/* \\\\u5728\\\\u624b\\\\u673a\\\\u4e0a\\\\u53ef\\\\u4ee5\\\\u9690\\\\u85cf\\\\u7bad\\\\u5934\\\\uff0c\\\\u53ea\\\\u7528\\\\u5c0f\\\\u5706\\\\u70b9 *\\\\/\\\\r\\\\n        }\\\"}')", "type": "query", "params": [], "bindings": [45, "fr", "{\"html\":\"<div class=\\\"carousel\\\">\\r\\n        <div class=\\\"carousel-track\\\">\\r\\n            <div class=\\\"slide current-slide\\\" style=\\\"background-image: url('https:\\/\\/images.unsplash.com\\/photo-1506744038136-46273834b3fb?q=80&w=2070&auto=format&fit=crop');\\\">\\r\\n                <div class=\\\"hero-text\\\">\\r\\n                    <h1>Like gentle sunshine<\\/h1>\\r\\n                    <p>on quiet leaves<\\/p>\\r\\n                <\\/div>\\r\\n            <\\/div>\\r\\n\\r\\n            <div class=\\\"slide\\\" style=\\\"background-image: url('https:\\/\\/images.unsplash.com\\/photo-1470770841072-f978cf4d019e?q=80&w=2070&auto=format&fit=crop');\\\">\\r\\n                <div class=\\\"hero-text\\\">\\r\\n                    <h1>Whispers of the Forest<\\/h1>\\r\\n                    <p>a story in every branch<\\/p>\\r\\n                <\\/div>\\r\\n            <\\/div>\\r\\n\\r\\n            <div class=\\\"slide\\\" style=\\\"background-image: url('https:\\/\\/images.unsplash.com\\/photo-1439405326853-58f2724f4d31?q=80&w=2070&auto=format&fit=crop');\\\">\\r\\n                <div class=\\\"hero-text\\\">\\r\\n                    <h1>Echoes of the Ocean<\\/h1>\\r\\n                    <p>deep and serene<\\/p>\\r\\n                <\\/div>\\r\\n            <\\/div>\\r\\n            <\\/div>\\r\\n\\r\\n        <button class=\\\"arrow left-arrow\\\">&#10094;<\\/button>\\r\\n        <button class=\\\"arrow right-arrow\\\">&#10095;<\\/button>\\r\\n        \\r\\n        <div class=\\\"carousel-dots\\\">\\r\\n            <\\/div>\\r\\n    <\\/div>\\r\\n\\r\\n\",\"css\":\"\\/* --- 2. \\u8f6e\\u64ad\\u7ec4\\u4ef6\\u4e3b\\u8981\\u6837\\u5f0f --- *\\/\\r\\n        .carousel {\\r\\n            position: relative;\\r\\n            width: 100%;\\r\\n            height: 70vh; \\/* \\u60a8\\u53ef\\u4ee5\\u6839\\u636e\\u9700\\u8981\\u8c03\\u6574\\u9ad8\\u5ea6 *\\/\\r\\n            min-height: 450px;\\r\\n            overflow: hidden; \\/* \\u5173\\u952e\\uff1a\\u9690\\u85cf\\u6240\\u6709\\u5728\\u5bb9\\u5668\\u5916\\u7684\\u5e7b\\u706f\\u7247 *\\/\\r\\n        }\\r\\n\\r\\n        .carousel-track {\\r\\n            position: relative;\\r\\n            height: 100%;\\r\\n            display: flex; \\/* \\u8ba9\\u6240\\u6709\\u5e7b\\u706f\\u7247\\u5728\\u540c\\u4e00\\u884c\\u6392\\u5217 *\\/\\r\\n            transition: transform 0.5s ease-in-out; \\/* \\u5e73\\u6ed1\\u7684\\u6ed1\\u52a8\\u52a8\\u753b *\\/\\r\\n        }\\r\\n\\r\\n        .slide {\\r\\n            flex: 1 0 100%; \\/* \\u6bcf\\u5f20\\u5e7b\\u706f\\u7247\\u5360\\u636e100%\\u7684\\u5bbd\\u5ea6\\u4e14\\u4e0d\\u6536\\u7f29 *\\/\\r\\n            width: 100%;\\r\\n            height: 100%;\\r\\n            background-size: cover;\\r\\n            background-position: center;\\r\\n            display: flex;\\r\\n            justify-content: center;\\r\\n            align-items: center;\\r\\n        }\\r\\n\\r\\n        \\/* --- 3. \\u6587\\u672c\\u548c\\u52a8\\u753b --- *\\/\\r\\n        .hero-text {\\r\\n            text-align: center;\\r\\n            color: white;\\r\\n            max-width: 90%;\\r\\n            text-shadow: 0 2px 8px rgba(0, 0, 0, 0.5); \\/* \\u7ed9\\u6587\\u5b57\\u6dfb\\u52a0\\u9634\\u5f71\\u4ee5\\u589e\\u5f3a\\u53ef\\u8bfb\\u6027 *\\/\\r\\n            opacity: 0; \\/* \\u9ed8\\u8ba4\\u9690\\u85cf\\u6587\\u672c *\\/\\r\\n            transform: translateY(20px);\\r\\n            transition: opacity 0.5s ease-out 0.4s, transform 0.5s ease-out 0.4s; \\/* \\u5ef6\\u8fdf0.4\\u79d2\\u6267\\u884c\\u6587\\u672c\\u52a8\\u753b *\\/\\r\\n        }\\r\\n\\r\\n        .current-slide .hero-text {\\r\\n            opacity: 1; \\/* \\u5f53\\u524d\\u5e7b\\u706f\\u7247\\u7684\\u6587\\u672c\\u663e\\u793a\\u51fa\\u6765 *\\/\\r\\n            transform: translateY(0);\\r\\n        }\\r\\n\\r\\n        .hero-text h1 {\\r\\n            font-size: 3rem;\\r\\n            font-weight: 500;\\r\\n            margin-bottom: 0.5rem;\\r\\n        }\\r\\n\\r\\n        .hero-text p {\\r\\n            font-size: 1.2rem;\\r\\n            font-weight: 300;\\r\\n        }\\r\\n        \\r\\n        \\/* --- 4. \\u5de6\\u53f3\\u7bad\\u5934 --- *\\/\\r\\n        .arrow {\\r\\n            position: absolute;\\r\\n            top: 50%;\\r\\n            transform: translateY(-50%);\\r\\n            background: transparent;\\r\\n            border: 1px solid rgba(255, 255, 255, 0.4);\\r\\n            color: rgba(255, 255, 255, 0.7);\\r\\n            font-size: 1.5rem;\\r\\n            padding: 10px 15px;\\r\\n            border-radius: 50%;\\r\\n            cursor: pointer;\\r\\n            z-index: 10;\\r\\n            transition: all 0.2s ease-in-out;\\r\\n        }\\r\\n        \\r\\n        .arrow:hover {\\r\\n            background-color: rgba(0, 0, 0, 0.3);\\r\\n            color: white;\\r\\n            border-color: transparent;\\r\\n        }\\r\\n\\r\\n        .left-arrow { left: 30px; }\\r\\n        .right-arrow { right: 30px; }\\r\\n        \\r\\n        \\/* --- 5. \\u5c0f\\u5706\\u70b9\\u6307\\u793a\\u5668 --- *\\/\\r\\n        .carousel-dots {\\r\\n            position: absolute;\\r\\n            bottom: 25px;\\r\\n            left: 50%;\\r\\n            transform: translateX(-50%);\\r\\n            display: flex;\\r\\n            gap: 12px;\\r\\n            z-index: 10;\\r\\n        }\\r\\n\\r\\n        .dot {\\r\\n            border: none;\\r\\n            width: 12px;\\r\\n            height: 12px;\\r\\n            border-radius: 50%;\\r\\n            background-color: rgba(255, 255, 255, 0.4);\\r\\n            cursor: pointer;\\r\\n            transition: background-color 0.2s ease, transform 0.2s ease;\\r\\n        }\\r\\n\\r\\n        .dot.current-dot {\\r\\n            background-color: white;\\r\\n            transform: scale(1.2);\\r\\n        }\\r\\n        \\r\\n        \\/* --- 6. \\u54cd\\u5e94\\u5f0f\\u8bbe\\u8ba1 --- *\\/\\r\\n        @media (max-width: 768px) {\\r\\n            .hero-text h1 { font-size: 2rem; }\\r\\n            .hero-text p { font-size: 1rem; }\\r\\n            .arrow { display: none; } \\/* \\u5728\\u624b\\u673a\\u4e0a\\u53ef\\u4ee5\\u9690\\u85cf\\u7bad\\u5934\\uff0c\\u53ea\\u7528\\u5c0f\\u5706\\u70b9 *\\/\\r\\n        }\"}"], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "packages/Webkul/Theme/src/Repositories/ThemeCustomizationRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Theme\\src\\Repositories\\ThemeCustomizationRepository.php", "line": 172}, {"index": 19, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Settings/ThemeController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Settings\\ThemeController.php", "line": 120}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.5474021, "duration": 0.00202, "duration_str": "2.02ms", "memory": 0, "memory_str": null, "filename": "ThemeCustomizationRepository.php:172", "source": {"index": 18, "namespace": null, "name": "packages/Webkul/Theme/src/Repositories/ThemeCustomizationRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Theme\\src\\Repositories\\ThemeCustomizationRepository.php", "line": 172}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FTheme%2Fsrc%2FRepositories%2FThemeCustomizationRepository.php&line=172", "ajax": false, "filename": "ThemeCustomizationRepository.php", "line": "172"}, "connection": "mlk", "explain": null, "start_percent": 65.631, "width_percent": 6.894}, {"sql": "insert into `theme_customization_translations` (`theme_customization_id`, `locale`, `options`) values (45, 'gr', '{\\\"html\\\":\\\"<div class=\\\\\\\"carousel\\\\\\\">\\\\r\\\\n        <div class=\\\\\\\"carousel-track\\\\\\\">\\\\r\\\\n            <div class=\\\\\\\"slide current-slide\\\\\\\" style=\\\\\\\"background-image: url(\\'https:\\\\/\\\\/images.unsplash.com\\\\/photo-1506744038136-46273834b3fb?q=80&w=2070&auto=format&fit=crop\\');\\\\\\\">\\\\r\\\\n                <div class=\\\\\\\"hero-text\\\\\\\">\\\\r\\\\n                    <h1>Like gentle sunshine<\\\\/h1>\\\\r\\\\n                    <p>on quiet leaves<\\\\/p>\\\\r\\\\n                <\\\\/div>\\\\r\\\\n            <\\\\/div>\\\\r\\\\n\\\\r\\\\n            <div class=\\\\\\\"slide\\\\\\\" style=\\\\\\\"background-image: url(\\'https:\\\\/\\\\/images.unsplash.com\\\\/photo-1470770841072-f978cf4d019e?q=80&w=2070&auto=format&fit=crop\\');\\\\\\\">\\\\r\\\\n                <div class=\\\\\\\"hero-text\\\\\\\">\\\\r\\\\n                    <h1>Whispers of the Forest<\\\\/h1>\\\\r\\\\n                    <p>a story in every branch<\\\\/p>\\\\r\\\\n                <\\\\/div>\\\\r\\\\n            <\\\\/div>\\\\r\\\\n\\\\r\\\\n            <div class=\\\\\\\"slide\\\\\\\" style=\\\\\\\"background-image: url(\\'https:\\\\/\\\\/images.unsplash.com\\\\/photo-1439405326853-58f2724f4d31?q=80&w=2070&auto=format&fit=crop\\');\\\\\\\">\\\\r\\\\n                <div class=\\\\\\\"hero-text\\\\\\\">\\\\r\\\\n                    <h1>Echoes of the Ocean<\\\\/h1>\\\\r\\\\n                    <p>deep and serene<\\\\/p>\\\\r\\\\n                <\\\\/div>\\\\r\\\\n            <\\\\/div>\\\\r\\\\n            <\\\\/div>\\\\r\\\\n\\\\r\\\\n        <button class=\\\\\\\"arrow left-arrow\\\\\\\">&#10094;<\\\\/button>\\\\r\\\\n        <button class=\\\\\\\"arrow right-arrow\\\\\\\">&#10095;<\\\\/button>\\\\r\\\\n        \\\\r\\\\n        <div class=\\\\\\\"carousel-dots\\\\\\\">\\\\r\\\\n            <\\\\/div>\\\\r\\\\n    <\\\\/div>\\\\r\\\\n\\\\r\\\\n\\\",\\\"css\\\":\\\"\\\\/* --- 2. \\\\u8f6e\\\\u64ad\\\\u7ec4\\\\u4ef6\\\\u4e3b\\\\u8981\\\\u6837\\\\u5f0f --- *\\\\/\\\\r\\\\n        .carousel {\\\\r\\\\n            position: relative;\\\\r\\\\n            width: 100%;\\\\r\\\\n            height: 70vh; \\\\/* \\\\u60a8\\\\u53ef\\\\u4ee5\\\\u6839\\\\u636e\\\\u9700\\\\u8981\\\\u8c03\\\\u6574\\\\u9ad8\\\\u5ea6 *\\\\/\\\\r\\\\n            min-height: 450px;\\\\r\\\\n            overflow: hidden; \\\\/* \\\\u5173\\\\u952e\\\\uff1a\\\\u9690\\\\u85cf\\\\u6240\\\\u6709\\\\u5728\\\\u5bb9\\\\u5668\\\\u5916\\\\u7684\\\\u5e7b\\\\u706f\\\\u7247 *\\\\/\\\\r\\\\n        }\\\\r\\\\n\\\\r\\\\n        .carousel-track {\\\\r\\\\n            position: relative;\\\\r\\\\n            height: 100%;\\\\r\\\\n            display: flex; \\\\/* \\\\u8ba9\\\\u6240\\\\u6709\\\\u5e7b\\\\u706f\\\\u7247\\\\u5728\\\\u540c\\\\u4e00\\\\u884c\\\\u6392\\\\u5217 *\\\\/\\\\r\\\\n            transition: transform 0.5s ease-in-out; \\\\/* \\\\u5e73\\\\u6ed1\\\\u7684\\\\u6ed1\\\\u52a8\\\\u52a8\\\\u753b *\\\\/\\\\r\\\\n        }\\\\r\\\\n\\\\r\\\\n        .slide {\\\\r\\\\n            flex: 1 0 100%; \\\\/* \\\\u6bcf\\\\u5f20\\\\u5e7b\\\\u706f\\\\u7247\\\\u5360\\\\u636e100%\\\\u7684\\\\u5bbd\\\\u5ea6\\\\u4e14\\\\u4e0d\\\\u6536\\\\u7f29 *\\\\/\\\\r\\\\n            width: 100%;\\\\r\\\\n            height: 100%;\\\\r\\\\n            background-size: cover;\\\\r\\\\n            background-position: center;\\\\r\\\\n            display: flex;\\\\r\\\\n            justify-content: center;\\\\r\\\\n            align-items: center;\\\\r\\\\n        }\\\\r\\\\n\\\\r\\\\n        \\\\/* --- 3. \\\\u6587\\\\u672c\\\\u548c\\\\u52a8\\\\u753b --- *\\\\/\\\\r\\\\n        .hero-text {\\\\r\\\\n            text-align: center;\\\\r\\\\n            color: white;\\\\r\\\\n            max-width: 90%;\\\\r\\\\n            text-shadow: 0 2px 8px rgba(0, 0, 0, 0.5); \\\\/* \\\\u7ed9\\\\u6587\\\\u5b57\\\\u6dfb\\\\u52a0\\\\u9634\\\\u5f71\\\\u4ee5\\\\u589e\\\\u5f3a\\\\u53ef\\\\u8bfb\\\\u6027 *\\\\/\\\\r\\\\n            opacity: 0; \\\\/* \\\\u9ed8\\\\u8ba4\\\\u9690\\\\u85cf\\\\u6587\\\\u672c *\\\\/\\\\r\\\\n            transform: translateY(20px);\\\\r\\\\n            transition: opacity 0.5s ease-out 0.4s, transform 0.5s ease-out 0.4s; \\\\/* \\\\u5ef6\\\\u8fdf0.4\\\\u79d2\\\\u6267\\\\u884c\\\\u6587\\\\u672c\\\\u52a8\\\\u753b *\\\\/\\\\r\\\\n        }\\\\r\\\\n\\\\r\\\\n        .current-slide .hero-text {\\\\r\\\\n            opacity: 1; \\\\/* \\\\u5f53\\\\u524d\\\\u5e7b\\\\u706f\\\\u7247\\\\u7684\\\\u6587\\\\u672c\\\\u663e\\\\u793a\\\\u51fa\\\\u6765 *\\\\/\\\\r\\\\n            transform: translateY(0);\\\\r\\\\n        }\\\\r\\\\n\\\\r\\\\n        .hero-text h1 {\\\\r\\\\n            font-size: 3rem;\\\\r\\\\n            font-weight: 500;\\\\r\\\\n            margin-bottom: 0.5rem;\\\\r\\\\n        }\\\\r\\\\n\\\\r\\\\n        .hero-text p {\\\\r\\\\n            font-size: 1.2rem;\\\\r\\\\n            font-weight: 300;\\\\r\\\\n        }\\\\r\\\\n        \\\\r\\\\n        \\\\/* --- 4. \\\\u5de6\\\\u53f3\\\\u7bad\\\\u5934 --- *\\\\/\\\\r\\\\n        .arrow {\\\\r\\\\n            position: absolute;\\\\r\\\\n            top: 50%;\\\\r\\\\n            transform: translateY(-50%);\\\\r\\\\n            background: transparent;\\\\r\\\\n            border: 1px solid rgba(255, 255, 255, 0.4);\\\\r\\\\n            color: rgba(255, 255, 255, 0.7);\\\\r\\\\n            font-size: 1.5rem;\\\\r\\\\n            padding: 10px 15px;\\\\r\\\\n            border-radius: 50%;\\\\r\\\\n            cursor: pointer;\\\\r\\\\n            z-index: 10;\\\\r\\\\n            transition: all 0.2s ease-in-out;\\\\r\\\\n        }\\\\r\\\\n        \\\\r\\\\n        .arrow:hover {\\\\r\\\\n            background-color: rgba(0, 0, 0, 0.3);\\\\r\\\\n            color: white;\\\\r\\\\n            border-color: transparent;\\\\r\\\\n        }\\\\r\\\\n\\\\r\\\\n        .left-arrow { left: 30px; }\\\\r\\\\n        .right-arrow { right: 30px; }\\\\r\\\\n        \\\\r\\\\n        \\\\/* --- 5. \\\\u5c0f\\\\u5706\\\\u70b9\\\\u6307\\\\u793a\\\\u5668 --- *\\\\/\\\\r\\\\n        .carousel-dots {\\\\r\\\\n            position: absolute;\\\\r\\\\n            bottom: 25px;\\\\r\\\\n            left: 50%;\\\\r\\\\n            transform: translateX(-50%);\\\\r\\\\n            display: flex;\\\\r\\\\n            gap: 12px;\\\\r\\\\n            z-index: 10;\\\\r\\\\n        }\\\\r\\\\n\\\\r\\\\n        .dot {\\\\r\\\\n            border: none;\\\\r\\\\n            width: 12px;\\\\r\\\\n            height: 12px;\\\\r\\\\n            border-radius: 50%;\\\\r\\\\n            background-color: rgba(255, 255, 255, 0.4);\\\\r\\\\n            cursor: pointer;\\\\r\\\\n            transition: background-color 0.2s ease, transform 0.2s ease;\\\\r\\\\n        }\\\\r\\\\n\\\\r\\\\n        .dot.current-dot {\\\\r\\\\n            background-color: white;\\\\r\\\\n            transform: scale(1.2);\\\\r\\\\n        }\\\\r\\\\n        \\\\r\\\\n        \\\\/* --- 6. \\\\u54cd\\\\u5e94\\\\u5f0f\\\\u8bbe\\\\u8ba1 --- *\\\\/\\\\r\\\\n        @media (max-width: 768px) {\\\\r\\\\n            .hero-text h1 { font-size: 2rem; }\\\\r\\\\n            .hero-text p { font-size: 1rem; }\\\\r\\\\n            .arrow { display: none; } \\\\/* \\\\u5728\\\\u624b\\\\u673a\\\\u4e0a\\\\u53ef\\\\u4ee5\\\\u9690\\\\u85cf\\\\u7bad\\\\u5934\\\\uff0c\\\\u53ea\\\\u7528\\\\u5c0f\\\\u5706\\\\u70b9 *\\\\/\\\\r\\\\n        }\\\"}')", "type": "query", "params": [], "bindings": [45, "gr", "{\"html\":\"<div class=\\\"carousel\\\">\\r\\n        <div class=\\\"carousel-track\\\">\\r\\n            <div class=\\\"slide current-slide\\\" style=\\\"background-image: url('https:\\/\\/images.unsplash.com\\/photo-1506744038136-46273834b3fb?q=80&w=2070&auto=format&fit=crop');\\\">\\r\\n                <div class=\\\"hero-text\\\">\\r\\n                    <h1>Like gentle sunshine<\\/h1>\\r\\n                    <p>on quiet leaves<\\/p>\\r\\n                <\\/div>\\r\\n            <\\/div>\\r\\n\\r\\n            <div class=\\\"slide\\\" style=\\\"background-image: url('https:\\/\\/images.unsplash.com\\/photo-1470770841072-f978cf4d019e?q=80&w=2070&auto=format&fit=crop');\\\">\\r\\n                <div class=\\\"hero-text\\\">\\r\\n                    <h1>Whispers of the Forest<\\/h1>\\r\\n                    <p>a story in every branch<\\/p>\\r\\n                <\\/div>\\r\\n            <\\/div>\\r\\n\\r\\n            <div class=\\\"slide\\\" style=\\\"background-image: url('https:\\/\\/images.unsplash.com\\/photo-1439405326853-58f2724f4d31?q=80&w=2070&auto=format&fit=crop');\\\">\\r\\n                <div class=\\\"hero-text\\\">\\r\\n                    <h1>Echoes of the Ocean<\\/h1>\\r\\n                    <p>deep and serene<\\/p>\\r\\n                <\\/div>\\r\\n            <\\/div>\\r\\n            <\\/div>\\r\\n\\r\\n        <button class=\\\"arrow left-arrow\\\">&#10094;<\\/button>\\r\\n        <button class=\\\"arrow right-arrow\\\">&#10095;<\\/button>\\r\\n        \\r\\n        <div class=\\\"carousel-dots\\\">\\r\\n            <\\/div>\\r\\n    <\\/div>\\r\\n\\r\\n\",\"css\":\"\\/* --- 2. \\u8f6e\\u64ad\\u7ec4\\u4ef6\\u4e3b\\u8981\\u6837\\u5f0f --- *\\/\\r\\n        .carousel {\\r\\n            position: relative;\\r\\n            width: 100%;\\r\\n            height: 70vh; \\/* \\u60a8\\u53ef\\u4ee5\\u6839\\u636e\\u9700\\u8981\\u8c03\\u6574\\u9ad8\\u5ea6 *\\/\\r\\n            min-height: 450px;\\r\\n            overflow: hidden; \\/* \\u5173\\u952e\\uff1a\\u9690\\u85cf\\u6240\\u6709\\u5728\\u5bb9\\u5668\\u5916\\u7684\\u5e7b\\u706f\\u7247 *\\/\\r\\n        }\\r\\n\\r\\n        .carousel-track {\\r\\n            position: relative;\\r\\n            height: 100%;\\r\\n            display: flex; \\/* \\u8ba9\\u6240\\u6709\\u5e7b\\u706f\\u7247\\u5728\\u540c\\u4e00\\u884c\\u6392\\u5217 *\\/\\r\\n            transition: transform 0.5s ease-in-out; \\/* \\u5e73\\u6ed1\\u7684\\u6ed1\\u52a8\\u52a8\\u753b *\\/\\r\\n        }\\r\\n\\r\\n        .slide {\\r\\n            flex: 1 0 100%; \\/* \\u6bcf\\u5f20\\u5e7b\\u706f\\u7247\\u5360\\u636e100%\\u7684\\u5bbd\\u5ea6\\u4e14\\u4e0d\\u6536\\u7f29 *\\/\\r\\n            width: 100%;\\r\\n            height: 100%;\\r\\n            background-size: cover;\\r\\n            background-position: center;\\r\\n            display: flex;\\r\\n            justify-content: center;\\r\\n            align-items: center;\\r\\n        }\\r\\n\\r\\n        \\/* --- 3. \\u6587\\u672c\\u548c\\u52a8\\u753b --- *\\/\\r\\n        .hero-text {\\r\\n            text-align: center;\\r\\n            color: white;\\r\\n            max-width: 90%;\\r\\n            text-shadow: 0 2px 8px rgba(0, 0, 0, 0.5); \\/* \\u7ed9\\u6587\\u5b57\\u6dfb\\u52a0\\u9634\\u5f71\\u4ee5\\u589e\\u5f3a\\u53ef\\u8bfb\\u6027 *\\/\\r\\n            opacity: 0; \\/* \\u9ed8\\u8ba4\\u9690\\u85cf\\u6587\\u672c *\\/\\r\\n            transform: translateY(20px);\\r\\n            transition: opacity 0.5s ease-out 0.4s, transform 0.5s ease-out 0.4s; \\/* \\u5ef6\\u8fdf0.4\\u79d2\\u6267\\u884c\\u6587\\u672c\\u52a8\\u753b *\\/\\r\\n        }\\r\\n\\r\\n        .current-slide .hero-text {\\r\\n            opacity: 1; \\/* \\u5f53\\u524d\\u5e7b\\u706f\\u7247\\u7684\\u6587\\u672c\\u663e\\u793a\\u51fa\\u6765 *\\/\\r\\n            transform: translateY(0);\\r\\n        }\\r\\n\\r\\n        .hero-text h1 {\\r\\n            font-size: 3rem;\\r\\n            font-weight: 500;\\r\\n            margin-bottom: 0.5rem;\\r\\n        }\\r\\n\\r\\n        .hero-text p {\\r\\n            font-size: 1.2rem;\\r\\n            font-weight: 300;\\r\\n        }\\r\\n        \\r\\n        \\/* --- 4. \\u5de6\\u53f3\\u7bad\\u5934 --- *\\/\\r\\n        .arrow {\\r\\n            position: absolute;\\r\\n            top: 50%;\\r\\n            transform: translateY(-50%);\\r\\n            background: transparent;\\r\\n            border: 1px solid rgba(255, 255, 255, 0.4);\\r\\n            color: rgba(255, 255, 255, 0.7);\\r\\n            font-size: 1.5rem;\\r\\n            padding: 10px 15px;\\r\\n            border-radius: 50%;\\r\\n            cursor: pointer;\\r\\n            z-index: 10;\\r\\n            transition: all 0.2s ease-in-out;\\r\\n        }\\r\\n        \\r\\n        .arrow:hover {\\r\\n            background-color: rgba(0, 0, 0, 0.3);\\r\\n            color: white;\\r\\n            border-color: transparent;\\r\\n        }\\r\\n\\r\\n        .left-arrow { left: 30px; }\\r\\n        .right-arrow { right: 30px; }\\r\\n        \\r\\n        \\/* --- 5. \\u5c0f\\u5706\\u70b9\\u6307\\u793a\\u5668 --- *\\/\\r\\n        .carousel-dots {\\r\\n            position: absolute;\\r\\n            bottom: 25px;\\r\\n            left: 50%;\\r\\n            transform: translateX(-50%);\\r\\n            display: flex;\\r\\n            gap: 12px;\\r\\n            z-index: 10;\\r\\n        }\\r\\n\\r\\n        .dot {\\r\\n            border: none;\\r\\n            width: 12px;\\r\\n            height: 12px;\\r\\n            border-radius: 50%;\\r\\n            background-color: rgba(255, 255, 255, 0.4);\\r\\n            cursor: pointer;\\r\\n            transition: background-color 0.2s ease, transform 0.2s ease;\\r\\n        }\\r\\n\\r\\n        .dot.current-dot {\\r\\n            background-color: white;\\r\\n            transform: scale(1.2);\\r\\n        }\\r\\n        \\r\\n        \\/* --- 6. \\u54cd\\u5e94\\u5f0f\\u8bbe\\u8ba1 --- *\\/\\r\\n        @media (max-width: 768px) {\\r\\n            .hero-text h1 { font-size: 2rem; }\\r\\n            .hero-text p { font-size: 1rem; }\\r\\n            .arrow { display: none; } \\/* \\u5728\\u624b\\u673a\\u4e0a\\u53ef\\u4ee5\\u9690\\u85cf\\u7bad\\u5934\\uff0c\\u53ea\\u7528\\u5c0f\\u5706\\u70b9 *\\/\\r\\n        }\"}"], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "packages/Webkul/Theme/src/Repositories/ThemeCustomizationRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Theme\\src\\Repositories\\ThemeCustomizationRepository.php", "line": 172}, {"index": 19, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Settings/ThemeController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Settings\\ThemeController.php", "line": 120}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.5504222, "duration": 0.00783, "duration_str": "7.83ms", "memory": 0, "memory_str": null, "filename": "ThemeCustomizationRepository.php:172", "source": {"index": 18, "namespace": null, "name": "packages/Webkul/Theme/src/Repositories/ThemeCustomizationRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Theme\\src\\Repositories\\ThemeCustomizationRepository.php", "line": 172}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FTheme%2Fsrc%2FRepositories%2FThemeCustomizationRepository.php&line=172", "ajax": false, "filename": "ThemeCustomizationRepository.php", "line": "172"}, "connection": "mlk", "explain": null, "start_percent": 72.526, "width_percent": 26.724}, {"sql": "select * from `currencies` where `currencies`.`id` = 2 limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 22, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Core.php", "line": 390}, {"index": 23, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Core.php", "line": 433}, {"index": 24, "namespace": null, "name": "packages/Webkul/FPC/src/Hasher/DefaultHasher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\FPC\\src\\Hasher\\DefaultHasher.php", "line": 29}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-responsecache/src/Hasher/DefaultHasher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\spatie\\laravel-responsecache\\src\\Hasher\\DefaultHasher.php", "line": 18}], "start": **********.5636199, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 20, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 99.249, "width_percent": 0.751}]}, "models": {"data": {"Webkul\\Core\\Models\\Locale": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FModels%2FLocale.php&line=1", "ajax": false, "filename": "Locale.php", "line": "?"}}, "Webkul\\Core\\Models\\Channel": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FModels%2FChannel.php&line=1", "ajax": false, "filename": "Channel.php", "line": "?"}}, "Webkul\\User\\Models\\Admin": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FUser%2Fsrc%2FModels%2FAdmin.php&line=1", "ajax": false, "filename": "Admin.php", "line": "?"}}, "Webkul\\User\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FUser%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "Webkul\\Theme\\Models\\ThemeCustomization": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FTheme%2Fsrc%2FModels%2FThemeCustomization.php&line=1", "ajax": false, "filename": "ThemeCustomization.php", "line": "?"}}, "Webkul\\Core\\Models\\Currency": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FModels%2FCurrency.php&line=1", "ajax": false, "filename": "Currency.php", "line": "?"}}}, "count": 10, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "302 Found", "full_url": "http://mlk.test/admin/settings/themes/edit/45", "action_name": "admin.settings.themes.update", "controller_action": "Webkul\\Admin\\Http\\Controllers\\Settings\\ThemeController@update", "uri": "POST admin/settings/themes/edit/{id}", "controller": "Webkul\\Admin\\Http\\Controllers\\Settings\\ThemeController@update<a href=\"phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FHttp%2FControllers%2FSettings%2FThemeController.php&line=90\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "admin/settings/themes", "file": "<a href=\"phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FHttp%2FControllers%2FSettings%2FThemeController.php&line=90\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">packages/Webkul/Admin/src/Http/Controllers/Settings/ThemeController.php:90-127</a>", "middleware": "web, admin_locale, Webkul\\Core\\Http\\Middleware\\PreventRequestsDuringMaintenance, admin, Webkul\\Core\\Http\\Middleware\\NoCacheMiddleware", "duration": "360ms", "peak_memory": "42MB", "response": "Redirect to http://mlk.test/admin/settings/themes", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1778717557 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1778717557\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-111159412 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">RL8eYWcLOX1nKpLrVKRAtWfTglgDAXuESTadrPQu</span>\"\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>en</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>options</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>html</span>\" => \"\"\"\n        <span class=sf-dump-str title=\"4370 characters\">&lt;div class=&quot;carousel&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"4370 characters\">        &lt;div class=&quot;carousel-track&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"4370 characters\">            &lt;div class=&quot;slide current-slide&quot; style=&quot;background-image: url(&#039;https://images.unsplash.com/photo-1506744038136-46273834b3fb?q=80&amp;w=2070&amp;auto=format&amp;fit=crop&#039;);&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"4370 characters\">                &lt;div class=&quot;hero-text&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"4370 characters\">                    &lt;h1&gt;Like gentle sunshine&lt;/h1&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"4370 characters\">                    &lt;p&gt;on quiet leaves&lt;/p&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"4370 characters\">                &lt;/div&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"4370 characters\">            &lt;/div&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"4370 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"4370 characters\">            &lt;div class=&quot;slide&quot; style=&quot;background-image: url(&#039;https://images.unsplash.com/photo-1470770841072-f978cf4d019e?q=80&amp;w=2070&amp;auto=format&amp;fit=crop&#039;);&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"4370 characters\">                &lt;div class=&quot;hero-text&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"4370 characters\">                    &lt;h1&gt;Whispers of the Forest&lt;/h1&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"4370 characters\">                    &lt;p&gt;a story in every branch&lt;/p&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"4370 characters\">                &lt;/div&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"4370 characters\">            &lt;/div&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"4370 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"4370 characters\">            &lt;div class=&quot;slide&quot; style=&quot;background-image: url(&#039;https://images.unsplash.com/photo-1439405326853-58f2724f4d31?q=80&amp;w=2070&amp;auto=format&amp;fit=crop&#039;);&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"4370 characters\">                &lt;div class=&quot;hero-text&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"4370 characters\">                    &lt;h1&gt;Echoes of the Ocean&lt;/h1&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"4370 characters\">                    &lt;p&gt;deep and serene&lt;/p&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"4370 characters\">                &lt;/div&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"4370 characters\">            &lt;/div&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"4370 characters\">            &lt;/div&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"4370 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"4370 characters\">        &lt;button class=&quot;arrow left-arrow&quot;&gt;&amp;#10094;&lt;/button&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"4370 characters\">        &lt;button class=&quot;arrow right-arrow&quot;&gt;&amp;#10095;&lt;/button&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"4370 characters\">        <span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"4370 characters\">        &lt;div class=&quot;carousel-dots&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"4370 characters\">            &lt;/div&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"4370 characters\">    &lt;/div&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"4370 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"4370 characters\">&lt;script&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"4370 characters\">document.addEventListener(&#039;DOMContentLoaded&#039;, function() {<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"4370 characters\">    const track = document.querySelector(&#039;.carousel-track&#039;);<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"4370 characters\">    const slides = Array.from(track.children);<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"4370 characters\">    const nextButton = document.querySelector(&#039;.right-arrow&#039;);<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"4370 characters\">    const prevButton = document.querySelector(&#039;.left-arrow&#039;);<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"4370 characters\">    const dotsNav = document.querySelector(&#039;.carousel-dots&#039;);<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"4370 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"4370 characters\">    if (slides.length === 0) return;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"4370 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"4370 characters\">    // 1. &#21019;&#24314;&#23567;&#22278;&#28857;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"4370 characters\">    slides.forEach((slide, index) =&gt; {<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"4370 characters\">        const dot = document.createElement(&#039;button&#039;);<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"4370 characters\">        dot.classList.add(&#039;dot&#039;);<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"4370 characters\">        if (index === 0) dot.classList.add(&#039;current-dot&#039;);<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"4370 characters\">        dotsNav.appendChild(dot);<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"4370 characters\">    });<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"4370 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"4370 characters\">    const dots = Array.from(dotsNav.children);<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"4370 characters\">    const slideWidth = slides[0].getBoundingClientRect().width;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"4370 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"4370 characters\">    // 2. &#23558;&#25152;&#26377;&#24187;&#28783;&#29255;&#23433;&#25490;&#22312;&#27491;&#30830;&#30340;&#20301;&#32622;&#65288;&#24182;&#25490;&#65289;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"4370 characters\">    const setSlidePosition = (slide, index) =&gt; {<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"4370 characters\">        slide.style.left = slideWidth * index + &#039;px&#039;;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"4370 characters\">    };<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"4370 characters\">    slides.forEach(setSlidePosition);<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"4370 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"4370 characters\">    // 3. &#26680;&#24515;&#20989;&#25968;&#65306;&#31227;&#21160;&#21040;&#30446;&#26631;&#24187;&#28783;&#29255;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"4370 characters\">    const moveToSlide = (currentSlide, targetSlide) =&gt; {<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"4370 characters\">        const targetIndex = slides.findIndex(slide =&gt; slide === targetSlide);<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"4370 characters\">        track.style.transform = &#039;translateX(-&#039; + targetSlide.style.left + &#039;)&#039;;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"4370 characters\">        currentSlide.classList.remove(&#039;current-slide&#039;);<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"4370 characters\">        targetSlide.classList.add(&#039;current-slide&#039;);<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"4370 characters\">        <span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"4370 characters\">        // &#26356;&#26032;&#23567;&#22278;&#28857;&#30340;&#28608;&#27963;&#29366;&#24577;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"4370 characters\">        const currentDot = dotsNav.querySelector(&#039;.current-dot&#039;);<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"4370 characters\">        const targetDot = dots[targetIndex];<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"4370 characters\">        currentDot.classList.remove(&#039;current-dot&#039;);<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"4370 characters\">        targetDot.classList.add(&#039;current-dot&#039;);<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"4370 characters\">    };<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"4370 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"4370 characters\">    // 4. &#32465;&#23450;&#31661;&#22836;&#28857;&#20987;&#20107;&#20214;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"4370 characters\">    nextButton.addEventListener(&#039;click&#039;, e =&gt; {<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"4370 characters\">        const currentSlide = track.querySelector(&#039;.current-slide&#039;);<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"4370 characters\">        const nextSlide = currentSlide.nextElementSibling || slides[0]; // &#24490;&#29615;&#25773;&#25918;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"4370 characters\">        moveToSlide(currentSlide, nextSlide);<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"4370 characters\">    });<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"4370 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"4370 characters\">    prevButton.addEventListener(&#039;click&#039;, e =&gt; {<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"4370 characters\">        const currentSlide = track.querySelector(&#039;.current-slide&#039;);<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"4370 characters\">        const prevSlide = currentSlide.previousElementSibling || slides[slides.length - 1]; // &#24490;&#29615;&#25773;&#25918;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"4370 characters\">        moveToSlide(currentSlide, prevSlide);<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"4370 characters\">    });<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"4370 characters\">    <span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"4370 characters\">    // 5. &#32465;&#23450;&#23567;&#22278;&#28857;&#28857;&#20987;&#20107;&#20214;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"4370 characters\">    dotsNav.addEventListener(&#039;click&#039;, e =&gt; {<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"4370 characters\">        const targetDot = e.target.closest(&#039;button.dot&#039;);<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"4370 characters\">        if (!targetDot) return;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"4370 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"4370 characters\">        const currentSlide = track.querySelector(&#039;.current-slide&#039;);<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"4370 characters\">        const targetIndex = dots.findIndex(dot =&gt; dot === targetDot);<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"4370 characters\">        const targetSlide = slides[targetIndex];<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"4370 characters\">        <span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"4370 characters\">        moveToSlide(currentSlide, targetSlide);<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"4370 characters\">    });<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"4370 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"4370 characters\">    // (&#21487;&#36873;) 6. &#33258;&#21160;&#25773;&#25918;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"4370 characters\">    let slideInterval;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"4370 characters\">    const startSlideShow = () =&gt; {<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"4370 characters\">      slideInterval = setInterval(() =&gt; {<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"4370 characters\">        nextButton.click(); // &#27169;&#25311;&#28857;&#20987;&#19979;&#19968;&#20010;&#25353;&#38062;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"4370 characters\">      }, 5000); // &#27599;5&#31186;&#20999;&#25442;&#19968;&#27425;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"4370 characters\">    };<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"4370 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"4370 characters\">    const stopSlideShow = () =&gt; {<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"4370 characters\">      clearInterval(slideInterval);<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"4370 characters\">    };<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"4370 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"4370 characters\">    // &#40736;&#26631;&#24748;&#20572;&#26102;&#20572;&#27490;&#33258;&#21160;&#25773;&#25918;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"4370 characters\">    track.addEventListener(&#039;mouseenter&#039;, stopSlideShow);<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"4370 characters\">    track.addEventListener(&#039;mouseleave&#039;, startSlideShow);<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"4370 characters\">    <span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"4370 characters\">    startSlideShow(); // &#39029;&#38754;&#21152;&#36733;&#21518;&#24320;&#22987;&#33258;&#21160;&#25773;&#25918;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"4370 characters\">});<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"4370 characters\">&lt;/script&gt;</span>\n        \"\"\"\n      \"<span class=sf-dump-key>css</span>\" => \"\"\"\n        <span class=sf-dump-str title=\"3259 characters\">/* --- 2. &#36718;&#25773;&#32452;&#20214;&#20027;&#35201;&#26679;&#24335; --- */<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3259 characters\">        .carousel {<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3259 characters\">            position: relative;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3259 characters\">            width: 100%;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3259 characters\">            height: 70vh; /* &#24744;&#21487;&#20197;&#26681;&#25454;&#38656;&#35201;&#35843;&#25972;&#39640;&#24230; */<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3259 characters\">            min-height: 450px;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3259 characters\">            overflow: hidden; /* &#20851;&#38190;&#65306;&#38544;&#34255;&#25152;&#26377;&#22312;&#23481;&#22120;&#22806;&#30340;&#24187;&#28783;&#29255; */<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3259 characters\">        }<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3259 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3259 characters\">        .carousel-track {<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3259 characters\">            position: relative;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3259 characters\">            height: 100%;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3259 characters\">            display: flex; /* &#35753;&#25152;&#26377;&#24187;&#28783;&#29255;&#22312;&#21516;&#19968;&#34892;&#25490;&#21015; */<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3259 characters\">            transition: transform 0.5s ease-in-out; /* &#24179;&#28369;&#30340;&#28369;&#21160;&#21160;&#30011; */<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3259 characters\">        }<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3259 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3259 characters\">        .slide {<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3259 characters\">            flex: 1 0 100%; /* &#27599;&#24352;&#24187;&#28783;&#29255;&#21344;&#25454;100%&#30340;&#23485;&#24230;&#19988;&#19981;&#25910;&#32553; */<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3259 characters\">            width: 100%;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3259 characters\">            height: 100%;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3259 characters\">            background-size: cover;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3259 characters\">            background-position: center;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3259 characters\">            display: flex;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3259 characters\">            justify-content: center;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3259 characters\">            align-items: center;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3259 characters\">        }<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3259 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3259 characters\">        /* --- 3. &#25991;&#26412;&#21644;&#21160;&#30011; --- */<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3259 characters\">        .hero-text {<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3259 characters\">            text-align: center;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3259 characters\">            color: white;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3259 characters\">            max-width: 90%;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3259 characters\">            text-shadow: 0 2px 8px rgba(0, 0, 0, 0.5); /* &#32473;&#25991;&#23383;&#28155;&#21152;&#38452;&#24433;&#20197;&#22686;&#24378;&#21487;&#35835;&#24615; */<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3259 characters\">            opacity: 0; /* &#40664;&#35748;&#38544;&#34255;&#25991;&#26412; */<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3259 characters\">            transform: translateY(20px);<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3259 characters\">            transition: opacity 0.5s ease-out 0.4s, transform 0.5s ease-out 0.4s; /* &#24310;&#36831;0.4&#31186;&#25191;&#34892;&#25991;&#26412;&#21160;&#30011; */<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3259 characters\">        }<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3259 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3259 characters\">        .current-slide .hero-text {<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3259 characters\">            opacity: 1; /* &#24403;&#21069;&#24187;&#28783;&#29255;&#30340;&#25991;&#26412;&#26174;&#31034;&#20986;&#26469; */<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3259 characters\">            transform: translateY(0);<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3259 characters\">        }<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3259 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3259 characters\">        .hero-text h1 {<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3259 characters\">            font-size: 3rem;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3259 characters\">            font-weight: 500;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3259 characters\">            margin-bottom: 0.5rem;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3259 characters\">        }<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3259 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3259 characters\">        .hero-text p {<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3259 characters\">            font-size: 1.2rem;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3259 characters\">            font-weight: 300;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3259 characters\">        }<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3259 characters\">        <span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3259 characters\">        /* --- 4. &#24038;&#21491;&#31661;&#22836; --- */<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3259 characters\">        .arrow {<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3259 characters\">            position: absolute;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3259 characters\">            top: 50%;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3259 characters\">            transform: translateY(-50%);<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3259 characters\">            background: transparent;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3259 characters\">            border: 1px solid rgba(255, 255, 255, 0.4);<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3259 characters\">            color: rgba(255, 255, 255, 0.7);<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3259 characters\">            font-size: 1.5rem;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3259 characters\">            padding: 10px 15px;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3259 characters\">            border-radius: 50%;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3259 characters\">            cursor: pointer;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3259 characters\">            z-index: 10;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3259 characters\">            transition: all 0.2s ease-in-out;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3259 characters\">        }<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3259 characters\">        <span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3259 characters\">        .arrow:hover {<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3259 characters\">            background-color: rgba(0, 0, 0, 0.3);<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3259 characters\">            color: white;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3259 characters\">            border-color: transparent;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3259 characters\">        }<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3259 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3259 characters\">        .left-arrow { left: 30px; }<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3259 characters\">        .right-arrow { right: 30px; }<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3259 characters\">        <span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3259 characters\">        /* --- 5. &#23567;&#22278;&#28857;&#25351;&#31034;&#22120; --- */<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3259 characters\">        .carousel-dots {<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3259 characters\">            position: absolute;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3259 characters\">            bottom: 25px;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3259 characters\">            left: 50%;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3259 characters\">            transform: translateX(-50%);<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3259 characters\">            display: flex;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3259 characters\">            gap: 12px;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3259 characters\">            z-index: 10;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3259 characters\">        }<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3259 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3259 characters\">        .dot {<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3259 characters\">            border: none;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3259 characters\">            width: 12px;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3259 characters\">            height: 12px;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3259 characters\">            border-radius: 50%;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3259 characters\">            background-color: rgba(255, 255, 255, 0.4);<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3259 characters\">            cursor: pointer;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3259 characters\">            transition: background-color 0.2s ease, transform 0.2s ease;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3259 characters\">        }<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3259 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3259 characters\">        .dot.current-dot {<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3259 characters\">            background-color: white;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3259 characters\">            transform: scale(1.2);<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3259 characters\">        }<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3259 characters\">        <span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3259 characters\">        /* --- 6. &#21709;&#24212;&#24335;&#35774;&#35745; --- */<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3259 characters\">        @media (max-width: 768px) {<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3259 characters\">            .hero-text h1 { font-size: 2rem; }<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3259 characters\">            .hero-text p { font-size: 1rem; }<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3259 characters\">            .arrow { display: none; } /* &#22312;&#25163;&#26426;&#19978;&#21487;&#20197;&#38544;&#34255;&#31661;&#22836;&#65292;&#21482;&#29992;&#23567;&#22278;&#28857; */<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"3259 characters\">        }</span>\n        \"\"\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"14 characters\">static_content</span>\"\n  \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">slogan</span>\"\n  \"<span class=sf-dump-key>sort_order</span>\" => \"<span class=sf-dump-str>8</span>\"\n  \"<span class=sf-dump-key>channel_id</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>theme_code</span>\" => \"<span class=sf-dump-str title=\"7 characters\">default</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-111159412\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-214382284 data-indent-pad=\"  \"><span class=sf-dump-note>array:13</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1018 characters\">admin_locale=eyJpdiI6InlVTlI4cytSTEhwU1oxNXhFNmszTHc9PSIsInZhbHVlIjoiL1djczhIanIvN1ZXTlhGdmpwMWJhNkpqMnNiMHcxMjBic21GQ0ltRzRZV25DczdBcXZaVUNJWlFEakx1bjV6byIsIm1hYyI6IjhmNjlhYzdkYWNjMTVkNjA5ZThmMzlhYmViM2UwYTFiZDQxZDY4ZjEwZTg3NGVjZGNhOTAzODA3ODhhNjllMzQiLCJ0YWciOiIifQ%3D%3D; sidebar_collapsed=0; dark_mode=0; XSRF-TOKEN=eyJpdiI6IlBscnRhTXZ2ZWtZWDFtbjllWndwQVE9PSIsInZhbHVlIjoiZ3JOc2pHNjJvU1FwQ3JjdmdCUlZkcHUvWHJ5bnNPNjRNNU9NdXU2bFU0UnBaOFFHQ2ZJUlJib2p5bFlyWEdyMFA3aXZ5YmJEWkQzbi9oUnB1d1VTMG5ic2hPNVZyekVqTE40Y1pZNjhVMkxDdmhxdVJQdUFpejI1alBEZjRyQnQiLCJtYWMiOiJlZWFlOTU3NjQ3MjUwNDYyMmEzYTEzMjM2M2U0MjMyNDQ0ZDI0NmQ2NzdmY2E3ZGI3Y2Y0MzgxNTIwNTA2NDE0IiwidGFnIjoiIn0%3D; mlk_session=eyJpdiI6IjM1SDBpY3VKNmQ0aGFVRVV1U0Z1OVE9PSIsInZhbHVlIjoiZDl1bWtEcGdET094M08vT1J5VGZMM3B3Yno0S2pia2xybGdHSG9hMFhBZkNvcmxZR0pQa0hDYmpTR2FqQ01XWGIyTE9uZnRCZTlYemJhajQvUWIrR0N4eWJ3N1AyWWpkRVlOOUpOb0Qwc1lpbk1xN0k2aEhwRG80MVBYWVlSOWwiLCJtYWMiOiI4MzdkZjgzZmY3MWIwMGY4MDBhNmQ5NGNlY2Y4NTU5NTdmYTE5OTk5OGRjOGJjMWVjZmQyZjc0NjJhMzkyOWIxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">zh-CN,zh;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"45 characters\">http://mlk.test/admin/settings/themes/edit/45</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">multipart/form-data; boundary=----WebKitFormBoundaryr9ZIjkcBIU7Yikik</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"15 characters\">http://mlk.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">9131</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">mlk.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-214382284\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1549623507 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>admin_locale</span>\" => \"<span class=sf-dump-str title=\"5 characters\">zh_CN</span>\"\n  \"<span class=sf-dump-key>sidebar_collapsed</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>dark_mode</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">RL8eYWcLOX1nKpLrVKRAtWfTglgDAXuESTadrPQu</span>\"\n  \"<span class=sf-dump-key>mlk_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Zao9ptAmXhjVm7WD2j4izotzmKLg3wYf4OgogoiC</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1549623507\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1642731833 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 07 Aug 2025 15:55:48 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://mlk.test/admin/settings/themes</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>0</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1642731833\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-396359309 data-indent-pad=\"  \"><span class=sf-dump-note>array:10</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">RL8eYWcLOX1nKpLrVKRAtWfTglgDAXuESTadrPQu</span>\"\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">it</span>\"\n  \"<span class=sf-dump-key>currency</span>\" => \"<span class=sf-dump-str title=\"3 characters\">EUR</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"45 characters\">http://mlk.test/admin/settings/themes/edit/45</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>admin_locale</span>\" => \"<span class=sf-dump-str title=\"5 characters\">zh_CN</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>success</span>\" => \"<span class=sf-dump-str title=\"6 characters\">&#25104;&#21151;&#26356;&#26032;&#20027;&#39064;</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-396359309\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "302 Found", "full_url": "http://mlk.test/admin/settings/themes/edit/45", "action_name": "admin.settings.themes.update", "controller_action": "Webkul\\Admin\\Http\\Controllers\\Settings\\ThemeController@update"}, "badge": "302 Found"}}