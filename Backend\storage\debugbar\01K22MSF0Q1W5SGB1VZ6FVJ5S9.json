{"__meta": {"id": "01K22MSF0Q1W5SGB1VZ6FVJ5S9", "datetime": "2025-08-07 17:10:55", "utime": **********.384275, "method": "POST", "uri": "/admin/settings/themes/edit/46", "ip": "127.0.0.1"}, "modules": {"count": 3, "modules": [{"name": "Webkul\\Core", "models": ["Webkul\\Core\\Models\\Channel (1)", "Webkul\\Core\\Models\\Locale (5)", "Webkul\\Core\\Models\\Currency (1)"], "views": [], "queries": [{"sql": "select * from `channels` where `hostname` in ('mlk.test', 'http://mlk.test', 'https://mlk.test')", "duration": 0.24, "duration_str": "240ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.27, "duration_str": "270ms", "connection": "mlk"}, {"sql": "select * from `currencies` where `currencies`.`id` = 2 limit 1", "duration": 0.36, "duration_str": "360ms", "connection": "mlk"}]}, {"name": "Webkul\\Theme", "models": ["Webkul\\Theme\\Models\\ThemeCustomization (1)", "Webkul\\Theme\\Models\\ThemeCustomizationTranslation (5)"], "views": [], "queries": [{"sql": "select * from `theme_customizations` where `theme_customizations`.`id` = 46 limit 1", "duration": 0.16, "duration_str": "160ms", "connection": "mlk"}, {"sql": "select * from `theme_customization_translations` where `theme_customization_translations`.`theme_customization_id` in (46)", "duration": 0.3, "duration_str": "300ms", "connection": "mlk"}, {"sql": "update `theme_customizations` set `status` = 1, `theme_customizations`.`updated_at` = '2025-08-07 17:10:55' where `id` = 46", "duration": 2.67, "duration_str": "2.67s", "connection": "mlk"}, {"sql": "update `theme_customization_translations` set `options` = '{\\\"html\\\":\\\"<div class=\\\\\"carousel\\\\\">\\r\\n    <div class=\\\\\"carousel-track\\\\\">\\r\\n        <div class=\\\\\"slide current-slide\\\\\" style=\\\\\"background-image: url(\\'https:\\/\\/images.unsplash.com\\/photo-1506744038136-46273834b3fb?q=80&w=2070&auto=format&fit=crop\\');\\\\\">\\r\\n            <div class=\\\\\"hero-text\\\\\">\\r\\n                 <h1>Come un dolce raggio di sole<\\/h1>\\r\\n\\r\\n                <p>Scopri la nostra collezione eco-sostenibile, realizzata con materiali naturali che donano una sensazione delicata come un raggio di sole in una mattinata tranquilla.<\\/p>\\r\\n            <\\/div>\\r\\n        <\\/div>\\r\\n        <div class=\\\\\"slide\\\\\" style=\\\\\"background-image: url(\\'https:\\/\\/images.unsplash.com\\/photo-1470770841072-f978cf4d019e?q=80&w=2070&auto=format&fit=crop\\');\\\\\">\\r\\n            <div class=\\\\\"hero-text\\\\\">\\r\\n                 <h1>Sussurri della Foresta<\\/h1>\\r\\n\\r\\n                <p>Viaggia attraverso i nostri ultimi design, ispirati ai verdi intensi e alle storie non raccontate sussurrate tra le volte dell\\'antica foresta.<\\/p>\\r\\n            <\\/div>\\r\\n        <\\/div>\\r\\n        <div class=\\\\\"slide\\\\\" style=\\\\\"background-image: url(\\'https:\\/\\/images.unsplash.com\\/photo-1439405326853-58f2724f4d31?q=80&w=2070&auto=format&fit=crop\\');\\\\\">\\r\\n            <div class=\\\\\"hero-text\\\\\">\\r\\n                 <h1>Echi dell\\'Oceano<\\/h1>\\r\\n\\r\\n                <p>Abbraccia la calma e la potenza del mare con la nostra nuova linea, caratterizzata da colori sereni e design fluidi che catturano il ritmo delle onde.<\\/p>\\r\\n            <\\/div>\\r\\n        <\\/div>\\r\\n    <\\/div>\\r\\n    <button class=\\\\\"arrow left-arrow\\\\\">&#10094;<\\/button>\\r\\n    <button class=\\\\\"arrow right-arrow\\\\\">&#10095;<\\/button>\\r\\n    <div class=\\\\\"carousel-dots\\\\\"><\\/div>\\r\\n<\\/div>\\\",\\\"css\\\":\\\".carousel {\\r\\n  margin-top:4rem;\\r\\n    position: relative;\\r\\n    width: 100%;\\r\\n    height: 60vh;\\r\\n    min-height: 420px;\\r\\n    overflow: hidden;\\r\\n    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);\\r\\n}\\r\\n.carousel-track {\\r\\n    position: relative;\\r\\n    height: 100%;\\r\\n    display: flex;\\r\\n    transition: transform 0.7s cubic-bezier(0.65, 0, 0.35, 1);\\r\\n}\\r\\n.slide {\\r\\n    position: relative;\\r\\n    flex: 1 0 100%;\\r\\n    width: 100%;\\r\\n    height: 100%;\\r\\n    background-size: cover;\\r\\n    background-position: center;\\r\\n    display: flex;\\r\\n    justify-content: center;\\r\\n    align-items: center;\\r\\n    color: white;\\r\\n}\\r\\n.slide::before {\\r\\n    content:\\'\\';\\r\\n    position: absolute;\\r\\n    top: 0;\\r\\n    left: 0;\\r\\n    width: 100%;\\r\\n    height: 100%;\\r\\n    background-color: rgba(0, 0, 0, 0.25);\\r\\n    z-index: 1;\\r\\n}\\r\\n.hero-text {\\r\\n    position: relative;\\r\\n    z-index: 2;\\r\\n    text-align: center;\\r\\n    max-width: 90%;\\r\\n    padding: 20px;\\r\\n    text-shadow: 0 2px 15px rgba(0, 0, 0, 0.4);\\r\\n    opacity: 0;\\r\\n    transform: translateY(25px);\\r\\n    transition: opacity 0.6s cubic-bezier(0.22, 1, 0.36, 1) 0.3s, transform 0.6s cubic-bezier(0.22, 1, 0.36, 1) 0.3s;\\r\\n}\\r\\n.current-slide .hero-text {\\r\\n    opacity: 1;\\r\\n    transform: translateY(0);\\r\\n}\\r\\n.hero-text h1 {\\r\\n    font-size: 3rem;\\r\\n    font-weight: 700;\\r\\n    margin-bottom: 0.75rem;\\r\\n    letter-spacing: 1px;\\r\\n}\\r\\n.hero-text p {\\r\\n    font-size: 1.2rem;\\r\\n    font-weight: 300;\\r\\n    max-width: 650px;\\r\\n    margin: 0 auto;\\r\\n    line-height: 1.6;\\r\\n}\\r\\n.arrow {\\r\\n    position: absolute;\\r\\n    top: 50%;\\r\\n    transform: translateY(-50%);\\r\\n    background: rgba(0, 0, 0, 0.2);\\r\\n    border: none;\\r\\n    color: rgba(255, 255, 255, 0.9);\\r\\n    font-size: 1.5rem;\\r\\n    padding: 10px;\\r\\n    width: 50px;\\r\\n    height: 50px;\\r\\n    border-radius: 4px;\\r\\n    cursor: pointer;\\r\\n    z-index: 10;\\r\\n    display: flex;\\r\\n    justify-content: center;\\r\\n    align-items: center;\\r\\n    transition: all 0.2s ease-in-out;\\r\\n}\\r\\n.arrow:hover {\\r\\n    background-color: rgba(0, 0, 0, 0.5);\\r\\n    transform: translateY(-50%) scale(1.05);\\r\\n}\\r\\n.left-arrow {\\r\\n    left: 30px;\\r\\n}\\r\\n.right-arrow {\\r\\n    right: 30px;\\r\\n}\\r\\n.carousel-dots {\\r\\n    position: absolute;\\r\\n    bottom: 25px;\\r\\n    left: 50%;\\r\\n    transform: translateX(-50%);\\r\\n    display: flex;\\r\\n    gap: 10px;\\r\\n    z-index: 10;\\r\\n}\\r\\n.dot {\\r\\n    border: none;\\r\\n    width: 25px;\\r\\n    height: 4px;\\r\\n    border-radius: 2px;\\r\\n    background-color: rgba(255, 255, 255, 0.4);\\r\\n    cursor: pointer;\\r\\n    transition: all 0.3s ease;\\r\\n}\\r\\n.dot.current-dot {\\r\\n    background-color: white;\\r\\n    width: 40px;\\r\\n}\\r\\n@media (max-width: 768px) {\\r\\n    .hero-text h1 {\\r\\n        font-size: 2rem;\\r\\n    }\\r\\n    .hero-text p {\\r\\n        font-size: 1rem;\\r\\n    }\\r\\n    .arrow {\\r\\n        display: none;\\r\\n    }\\r\\n}\\\"}' where `id` = 202", "duration": 2.17, "duration_str": "2.17s", "connection": "mlk"}]}, {"name": "Webkul\\User", "models": ["Webkul\\User\\Models\\Admin (1)", "Webkul\\User\\Models\\Role (1)"], "views": [], "queries": [{"sql": "select count(*) as aggregate from `admins`", "duration": 0.32, "duration_str": "320ms", "connection": "mlk"}, {"sql": "select count(*) as aggregate from `admins`", "duration": 0.15, "duration_str": "150ms", "connection": "mlk"}, {"sql": "select * from `admins` where `id` = 1 limit 1", "duration": 0.21, "duration_str": "210ms", "connection": "mlk"}, {"sql": "select * from `roles` where `roles`.`id` = 1 limit 1", "duration": 0.15, "duration_str": "150ms", "connection": "mlk"}]}]}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.128444, "end": **********.392486, "duration": 0.2640421390533447, "duration_str": "264ms", "measures": [{"label": "Booting", "start": **********.128444, "relative_start": 0, "end": **********.301304, "relative_end": **********.301304, "duration": 0.*****************, "duration_str": "173ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.301316, "relative_start": 0.*****************, "end": **********.392488, "relative_end": 1.9073486328125e-06, "duration": 0.****************, "duration_str": "91.17ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.311927, "relative_start": 0.*****************, "end": **********.313956, "relative_end": **********.313956, "duration": 0.002028942108154297, "duration_str": "2.03ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.382762, "relative_start": 0.****************, "end": **********.382998, "relative_end": **********.382998, "duration": 0.00023603439331054688, "duration_str": "236μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "39MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.2.0", "Environment": "local", "Debug Mode": "Enabled", "URL": "mlk.test", "Timezone": "Africa/Lagos", "Locale": "zh_CN"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 13, "nb_statements": 13, "nb_visible_statements": 13, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.00768, "accumulated_duration_str": "7.68ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select exists (select 1 from information_schema.tables where table_schema = 'mlk' and table_name = 'admins' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 32}, {"index": 14, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 44}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 1004}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 832}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 1078}], "start": **********.3245232, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "DatabaseManager.php:32", "source": {"index": 13, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 32}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FInstaller%2Fsrc%2FHelpers%2FDatabaseManager.php&line=32", "ajax": false, "filename": "DatabaseManager.php", "line": "32"}, "connection": "mlk", "explain": null, "start_percent": 0, "width_percent": 5.729}, {"sql": "select count(*) as aggregate from `admins`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 38}, {"index": 16, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 44}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 1004}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 832}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 1078}], "start": **********.327886, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "DatabaseManager.php:38", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 38}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FInstaller%2Fsrc%2FHelpers%2FDatabaseManager.php&line=38", "ajax": false, "filename": "DatabaseManager.php", "line": "38"}, "connection": "mlk", "explain": null, "start_percent": 5.729, "width_percent": 4.167}, {"sql": "select * from `channels` where `hostname` in ('mlk.test', 'http://mlk.test', 'https://mlk.test')", "type": "query", "params": [], "bindings": ["mlk.test", "http://mlk.test", "https://mlk.test"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 578}, {"index": 16, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Core.php", "line": 143}, {"index": 17, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 127}, {"index": 18, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 45}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 1004}], "start": **********.333762, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:578", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 578}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=578", "ajax": false, "filename": "BaseRepository.php", "line": "578"}, "connection": "mlk", "explain": null, "start_percent": 9.896, "width_percent": 3.125}, {"sql": "select exists (select 1 from information_schema.tables where table_schema = 'mlk' and table_name = 'admins' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 32}, {"index": 14, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 59}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 16, "namespace": "middleware", "name": "admin_locale", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Middleware\\AdminLocale.php", "line": 46}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.335484, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "DatabaseManager.php:32", "source": {"index": 13, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 32}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FInstaller%2Fsrc%2FHelpers%2FDatabaseManager.php&line=32", "ajax": false, "filename": "DatabaseManager.php", "line": "32"}, "connection": "mlk", "explain": null, "start_percent": 13.021, "width_percent": 3.125}, {"sql": "select count(*) as aggregate from `admins`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 38}, {"index": 16, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 18, "namespace": "middleware", "name": "admin_locale", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Middleware\\AdminLocale.php", "line": 46}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.33643, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "DatabaseManager.php:38", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 38}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FInstaller%2Fsrc%2FHelpers%2FDatabaseManager.php&line=38", "ajax": false, "filename": "DatabaseManager.php", "line": "38"}, "connection": "mlk", "explain": null, "start_percent": 16.146, "width_percent": 1.953}, {"sql": "select * from `admins` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "admin", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\User\\src\\Http\\Middleware\\Bouncer.php", "line": 18}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.3394759, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "mlk", "explain": null, "start_percent": 18.099, "width_percent": 2.734}, {"sql": "select * from `roles` where `roles`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": "middleware", "name": "admin", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\User\\src\\Http\\Middleware\\Bouncer.php", "line": 54}, {"index": 22, "namespace": "middleware", "name": "admin", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\User\\src\\Http\\Middleware\\Bouncer.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 24, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 119}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.341571, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "admin:54", "source": {"index": 21, "namespace": "middleware", "name": "admin", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\User\\src\\Http\\Middleware\\Bouncer.php", "line": 54}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FUser%2Fsrc%2FHttp%2FMiddleware%2FBouncer.php&line=54", "ajax": false, "filename": "Bouncer.php", "line": "54"}, "connection": "mlk", "explain": null, "start_percent": 20.833, "width_percent": 1.953}, {"sql": "select * from `theme_customizations` where `theme_customizations`.`id` = 46 limit 1", "type": "query", "params": [], "bindings": [46], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, {"index": 21, "namespace": null, "name": "packages/Webkul/Theme/src/Repositories/ThemeCustomizationRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Theme\\src\\Repositories\\ThemeCustomizationRepository.php", "line": 42}, {"index": 22, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Settings/ThemeController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Settings\\ThemeController.php", "line": 117}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.344779, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:695", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=695", "ajax": false, "filename": "BaseRepository.php", "line": "695"}, "connection": "mlk", "explain": null, "start_percent": 22.786, "width_percent": 2.083}, {"sql": "select * from `theme_customization_translations` where `theme_customization_translations`.`theme_customization_id` in (46)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 25, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, {"index": 26, "namespace": null, "name": "packages/Webkul/Theme/src/Repositories/ThemeCustomizationRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Theme\\src\\Repositories\\ThemeCustomizationRepository.php", "line": 42}, {"index": 27, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Settings/ThemeController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Settings\\ThemeController.php", "line": 117}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.3464952, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:695", "source": {"index": 25, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=695", "ajax": false, "filename": "BaseRepository.php", "line": "695"}, "connection": "mlk", "explain": null, "start_percent": 24.87, "width_percent": 3.906}, {"sql": "update `theme_customizations` set `status` = 1, `theme_customizations`.`updated_at` = '2025-08-07 17:10:55' where `id` = 46", "type": "query", "params": [], "bindings": [1, "2025-08-07 17:10:55", 46], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 700}, {"index": 15, "namespace": null, "name": "packages/Webkul/Theme/src/Repositories/ThemeCustomizationRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Theme\\src\\Repositories\\ThemeCustomizationRepository.php", "line": 42}, {"index": 16, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Settings/ThemeController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Settings\\ThemeController.php", "line": 117}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.358695, "duration": 0.00267, "duration_str": "2.67ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:700", "source": {"index": 14, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 700}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=700", "ajax": false, "filename": "BaseRepository.php", "line": "700"}, "connection": "mlk", "explain": null, "start_percent": 28.776, "width_percent": 34.766}, {"sql": "update `theme_customization_translations` set `options` = '{\\\"html\\\":\\\"<div class=\\\\\\\"carousel\\\\\\\">\\\\r\\\\n    <div class=\\\\\\\"carousel-track\\\\\\\">\\\\r\\\\n        <div class=\\\\\\\"slide current-slide\\\\\\\" style=\\\\\\\"background-image: url(\\'https:\\\\/\\\\/images.unsplash.com\\\\/photo-1506744038136-46273834b3fb?q=80&w=2070&auto=format&fit=crop\\');\\\\\\\">\\\\r\\\\n            <div class=\\\\\\\"hero-text\\\\\\\">\\\\r\\\\n                 <h1>Come un dolce raggio di sole<\\\\/h1>\\\\r\\\\n\\\\r\\\\n                <p>Scopri la nostra collezione eco-sostenibile, realizzata con materiali naturali che donano una sensazione delicata come un raggio di sole in una mattinata tranquilla.<\\\\/p>\\\\r\\\\n            <\\\\/div>\\\\r\\\\n        <\\\\/div>\\\\r\\\\n        <div class=\\\\\\\"slide\\\\\\\" style=\\\\\\\"background-image: url(\\'https:\\\\/\\\\/images.unsplash.com\\\\/photo-1470770841072-f978cf4d019e?q=80&w=2070&auto=format&fit=crop\\');\\\\\\\">\\\\r\\\\n            <div class=\\\\\\\"hero-text\\\\\\\">\\\\r\\\\n                 <h1>Sussurri della Foresta<\\\\/h1>\\\\r\\\\n\\\\r\\\\n                <p>Viaggia attraverso i nostri ultimi design, ispirati ai verdi intensi e alle storie non raccontate sussurrate tra le volte dell\\'antica foresta.<\\\\/p>\\\\r\\\\n            <\\\\/div>\\\\r\\\\n        <\\\\/div>\\\\r\\\\n        <div class=\\\\\\\"slide\\\\\\\" style=\\\\\\\"background-image: url(\\'https:\\\\/\\\\/images.unsplash.com\\\\/photo-1439405326853-58f2724f4d31?q=80&w=2070&auto=format&fit=crop\\');\\\\\\\">\\\\r\\\\n            <div class=\\\\\\\"hero-text\\\\\\\">\\\\r\\\\n                 <h1>Echi dell\\'Oceano<\\\\/h1>\\\\r\\\\n\\\\r\\\\n                <p>Abbraccia la calma e la potenza del mare con la nostra nuova linea, caratterizzata da colori sereni e design fluidi che catturano il ritmo delle onde.<\\\\/p>\\\\r\\\\n            <\\\\/div>\\\\r\\\\n        <\\\\/div>\\\\r\\\\n    <\\\\/div>\\\\r\\\\n    <button class=\\\\\\\"arrow left-arrow\\\\\\\">&#10094;<\\\\/button>\\\\r\\\\n    <button class=\\\\\\\"arrow right-arrow\\\\\\\">&#10095;<\\\\/button>\\\\r\\\\n    <div class=\\\\\\\"carousel-dots\\\\\\\"><\\\\/div>\\\\r\\\\n<\\\\/div>\\\",\\\"css\\\":\\\".carousel {\\\\r\\\\n  margin-top:4rem;\\\\r\\\\n    position: relative;\\\\r\\\\n    width: 100%;\\\\r\\\\n    height: 60vh;\\\\r\\\\n    min-height: 420px;\\\\r\\\\n    overflow: hidden;\\\\r\\\\n    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);\\\\r\\\\n}\\\\r\\\\n.carousel-track {\\\\r\\\\n    position: relative;\\\\r\\\\n    height: 100%;\\\\r\\\\n    display: flex;\\\\r\\\\n    transition: transform 0.7s cubic-bezier(0.65, 0, 0.35, 1);\\\\r\\\\n}\\\\r\\\\n.slide {\\\\r\\\\n    position: relative;\\\\r\\\\n    flex: 1 0 100%;\\\\r\\\\n    width: 100%;\\\\r\\\\n    height: 100%;\\\\r\\\\n    background-size: cover;\\\\r\\\\n    background-position: center;\\\\r\\\\n    display: flex;\\\\r\\\\n    justify-content: center;\\\\r\\\\n    align-items: center;\\\\r\\\\n    color: white;\\\\r\\\\n}\\\\r\\\\n.slide::before {\\\\r\\\\n    content:\\'\\';\\\\r\\\\n    position: absolute;\\\\r\\\\n    top: 0;\\\\r\\\\n    left: 0;\\\\r\\\\n    width: 100%;\\\\r\\\\n    height: 100%;\\\\r\\\\n    background-color: rgba(0, 0, 0, 0.25);\\\\r\\\\n    z-index: 1;\\\\r\\\\n}\\\\r\\\\n.hero-text {\\\\r\\\\n    position: relative;\\\\r\\\\n    z-index: 2;\\\\r\\\\n    text-align: center;\\\\r\\\\n    max-width: 90%;\\\\r\\\\n    padding: 20px;\\\\r\\\\n    text-shadow: 0 2px 15px rgba(0, 0, 0, 0.4);\\\\r\\\\n    opacity: 0;\\\\r\\\\n    transform: translateY(25px);\\\\r\\\\n    transition: opacity 0.6s cubic-bezier(0.22, 1, 0.36, 1) 0.3s, transform 0.6s cubic-bezier(0.22, 1, 0.36, 1) 0.3s;\\\\r\\\\n}\\\\r\\\\n.current-slide .hero-text {\\\\r\\\\n    opacity: 1;\\\\r\\\\n    transform: translateY(0);\\\\r\\\\n}\\\\r\\\\n.hero-text h1 {\\\\r\\\\n    font-size: 3rem;\\\\r\\\\n    font-weight: 700;\\\\r\\\\n    margin-bottom: 0.75rem;\\\\r\\\\n    letter-spacing: 1px;\\\\r\\\\n}\\\\r\\\\n.hero-text p {\\\\r\\\\n    font-size: 1.2rem;\\\\r\\\\n    font-weight: 300;\\\\r\\\\n    max-width: 650px;\\\\r\\\\n    margin: 0 auto;\\\\r\\\\n    line-height: 1.6;\\\\r\\\\n}\\\\r\\\\n.arrow {\\\\r\\\\n    position: absolute;\\\\r\\\\n    top: 50%;\\\\r\\\\n    transform: translateY(-50%);\\\\r\\\\n    background: rgba(0, 0, 0, 0.2);\\\\r\\\\n    border: none;\\\\r\\\\n    color: rgba(255, 255, 255, 0.9);\\\\r\\\\n    font-size: 1.5rem;\\\\r\\\\n    padding: 10px;\\\\r\\\\n    width: 50px;\\\\r\\\\n    height: 50px;\\\\r\\\\n    border-radius: 4px;\\\\r\\\\n    cursor: pointer;\\\\r\\\\n    z-index: 10;\\\\r\\\\n    display: flex;\\\\r\\\\n    justify-content: center;\\\\r\\\\n    align-items: center;\\\\r\\\\n    transition: all 0.2s ease-in-out;\\\\r\\\\n}\\\\r\\\\n.arrow:hover {\\\\r\\\\n    background-color: rgba(0, 0, 0, 0.5);\\\\r\\\\n    transform: translateY(-50%) scale(1.05);\\\\r\\\\n}\\\\r\\\\n.left-arrow {\\\\r\\\\n    left: 30px;\\\\r\\\\n}\\\\r\\\\n.right-arrow {\\\\r\\\\n    right: 30px;\\\\r\\\\n}\\\\r\\\\n.carousel-dots {\\\\r\\\\n    position: absolute;\\\\r\\\\n    bottom: 25px;\\\\r\\\\n    left: 50%;\\\\r\\\\n    transform: translateX(-50%);\\\\r\\\\n    display: flex;\\\\r\\\\n    gap: 10px;\\\\r\\\\n    z-index: 10;\\\\r\\\\n}\\\\r\\\\n.dot {\\\\r\\\\n    border: none;\\\\r\\\\n    width: 25px;\\\\r\\\\n    height: 4px;\\\\r\\\\n    border-radius: 2px;\\\\r\\\\n    background-color: rgba(255, 255, 255, 0.4);\\\\r\\\\n    cursor: pointer;\\\\r\\\\n    transition: all 0.3s ease;\\\\r\\\\n}\\\\r\\\\n.dot.current-dot {\\\\r\\\\n    background-color: white;\\\\r\\\\n    width: 40px;\\\\r\\\\n}\\\\r\\\\n@media (max-width: 768px) {\\\\r\\\\n    .hero-text h1 {\\\\r\\\\n        font-size: 2rem;\\\\r\\\\n    }\\\\r\\\\n    .hero-text p {\\\\r\\\\n        font-size: 1rem;\\\\r\\\\n    }\\\\r\\\\n    .arrow {\\\\r\\\\n        display: none;\\\\r\\\\n    }\\\\r\\\\n}\\\"}' where `id` = 202", "type": "query", "params": [], "bindings": ["{\"html\":\"<div class=\\\"carousel\\\">\\r\\n    <div class=\\\"carousel-track\\\">\\r\\n        <div class=\\\"slide current-slide\\\" style=\\\"background-image: url('https:\\/\\/images.unsplash.com\\/photo-1506744038136-46273834b3fb?q=80&w=2070&auto=format&fit=crop');\\\">\\r\\n            <div class=\\\"hero-text\\\">\\r\\n                 <h1>Come un dolce raggio di sole<\\/h1>\\r\\n\\r\\n                <p>Scopri la nostra collezione eco-sostenibile, realizzata con materiali naturali che donano una sensazione delicata come un raggio di sole in una mattinata tranquilla.<\\/p>\\r\\n            <\\/div>\\r\\n        <\\/div>\\r\\n        <div class=\\\"slide\\\" style=\\\"background-image: url('https:\\/\\/images.unsplash.com\\/photo-1470770841072-f978cf4d019e?q=80&w=2070&auto=format&fit=crop');\\\">\\r\\n            <div class=\\\"hero-text\\\">\\r\\n                 <h1>Sussurri della Foresta<\\/h1>\\r\\n\\r\\n                <p>Viaggia attraverso i nostri ultimi design, ispirati ai verdi intensi e alle storie non raccontate sussurrate tra le volte dell'antica foresta.<\\/p>\\r\\n            <\\/div>\\r\\n        <\\/div>\\r\\n        <div class=\\\"slide\\\" style=\\\"background-image: url('https:\\/\\/images.unsplash.com\\/photo-1439405326853-58f2724f4d31?q=80&w=2070&auto=format&fit=crop');\\\">\\r\\n            <div class=\\\"hero-text\\\">\\r\\n                 <h1>Echi dell'Oceano<\\/h1>\\r\\n\\r\\n                <p>Abbraccia la calma e la potenza del mare con la nostra nuova linea, caratterizzata da colori sereni e design fluidi che catturano il ritmo delle onde.<\\/p>\\r\\n            <\\/div>\\r\\n        <\\/div>\\r\\n    <\\/div>\\r\\n    <button class=\\\"arrow left-arrow\\\">&#10094;<\\/button>\\r\\n    <button class=\\\"arrow right-arrow\\\">&#10095;<\\/button>\\r\\n    <div class=\\\"carousel-dots\\\"><\\/div>\\r\\n<\\/div>\",\"css\":\".carousel {\\r\\n  margin-top:4rem;\\r\\n    position: relative;\\r\\n    width: 100%;\\r\\n    height: 60vh;\\r\\n    min-height: 420px;\\r\\n    overflow: hidden;\\r\\n    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);\\r\\n}\\r\\n.carousel-track {\\r\\n    position: relative;\\r\\n    height: 100%;\\r\\n    display: flex;\\r\\n    transition: transform 0.7s cubic-bezier(0.65, 0, 0.35, 1);\\r\\n}\\r\\n.slide {\\r\\n    position: relative;\\r\\n    flex: 1 0 100%;\\r\\n    width: 100%;\\r\\n    height: 100%;\\r\\n    background-size: cover;\\r\\n    background-position: center;\\r\\n    display: flex;\\r\\n    justify-content: center;\\r\\n    align-items: center;\\r\\n    color: white;\\r\\n}\\r\\n.slide::before {\\r\\n    content:'';\\r\\n    position: absolute;\\r\\n    top: 0;\\r\\n    left: 0;\\r\\n    width: 100%;\\r\\n    height: 100%;\\r\\n    background-color: rgba(0, 0, 0, 0.25);\\r\\n    z-index: 1;\\r\\n}\\r\\n.hero-text {\\r\\n    position: relative;\\r\\n    z-index: 2;\\r\\n    text-align: center;\\r\\n    max-width: 90%;\\r\\n    padding: 20px;\\r\\n    text-shadow: 0 2px 15px rgba(0, 0, 0, 0.4);\\r\\n    opacity: 0;\\r\\n    transform: translateY(25px);\\r\\n    transition: opacity 0.6s cubic-bezier(0.22, 1, 0.36, 1) 0.3s, transform 0.6s cubic-bezier(0.22, 1, 0.36, 1) 0.3s;\\r\\n}\\r\\n.current-slide .hero-text {\\r\\n    opacity: 1;\\r\\n    transform: translateY(0);\\r\\n}\\r\\n.hero-text h1 {\\r\\n    font-size: 3rem;\\r\\n    font-weight: 700;\\r\\n    margin-bottom: 0.75rem;\\r\\n    letter-spacing: 1px;\\r\\n}\\r\\n.hero-text p {\\r\\n    font-size: 1.2rem;\\r\\n    font-weight: 300;\\r\\n    max-width: 650px;\\r\\n    margin: 0 auto;\\r\\n    line-height: 1.6;\\r\\n}\\r\\n.arrow {\\r\\n    position: absolute;\\r\\n    top: 50%;\\r\\n    transform: translateY(-50%);\\r\\n    background: rgba(0, 0, 0, 0.2);\\r\\n    border: none;\\r\\n    color: rgba(255, 255, 255, 0.9);\\r\\n    font-size: 1.5rem;\\r\\n    padding: 10px;\\r\\n    width: 50px;\\r\\n    height: 50px;\\r\\n    border-radius: 4px;\\r\\n    cursor: pointer;\\r\\n    z-index: 10;\\r\\n    display: flex;\\r\\n    justify-content: center;\\r\\n    align-items: center;\\r\\n    transition: all 0.2s ease-in-out;\\r\\n}\\r\\n.arrow:hover {\\r\\n    background-color: rgba(0, 0, 0, 0.5);\\r\\n    transform: translateY(-50%) scale(1.05);\\r\\n}\\r\\n.left-arrow {\\r\\n    left: 30px;\\r\\n}\\r\\n.right-arrow {\\r\\n    right: 30px;\\r\\n}\\r\\n.carousel-dots {\\r\\n    position: absolute;\\r\\n    bottom: 25px;\\r\\n    left: 50%;\\r\\n    transform: translateX(-50%);\\r\\n    display: flex;\\r\\n    gap: 10px;\\r\\n    z-index: 10;\\r\\n}\\r\\n.dot {\\r\\n    border: none;\\r\\n    width: 25px;\\r\\n    height: 4px;\\r\\n    border-radius: 2px;\\r\\n    background-color: rgba(255, 255, 255, 0.4);\\r\\n    cursor: pointer;\\r\\n    transition: all 0.3s ease;\\r\\n}\\r\\n.dot.current-dot {\\r\\n    background-color: white;\\r\\n    width: 40px;\\r\\n}\\r\\n@media (max-width: 768px) {\\r\\n    .hero-text h1 {\\r\\n        font-size: 2rem;\\r\\n    }\\r\\n    .hero-text p {\\r\\n        font-size: 1rem;\\r\\n    }\\r\\n    .arrow {\\r\\n        display: none;\\r\\n    }\\r\\n}\"}", 202], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 387}, {"index": 15, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 36}, {"index": 22, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 700}, {"index": 23, "namespace": null, "name": "packages/Webkul/Theme/src/Repositories/ThemeCustomizationRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Theme\\src\\Repositories\\ThemeCustomizationRepository.php", "line": 42}, {"index": 24, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Settings/ThemeController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Settings\\ThemeController.php", "line": 117}], "start": **********.3625598, "duration": 0.00217, "duration_str": "2.17ms", "memory": 0, "memory_str": null, "filename": "Translatable.php:387", "source": {"index": 14, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 387}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=387", "ajax": false, "filename": "Translatable.php", "line": "387"}, "connection": "mlk", "explain": null, "start_percent": 63.542, "width_percent": 28.255}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "packages/Webkul/Theme/src/Repositories/ThemeCustomizationRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Theme\\src\\Repositories\\ThemeCustomizationRepository.php", "line": 149}, {"index": 22, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Settings/ThemeController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Settings\\ThemeController.php", "line": 120}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.3694222, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 91.797, "width_percent": 3.516}, {"sql": "select * from `currencies` where `currencies`.`id` = 2 limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 22, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Core.php", "line": 390}, {"index": 23, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Core.php", "line": 433}, {"index": 24, "namespace": null, "name": "packages/Webkul/FPC/src/Hasher/DefaultHasher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\FPC\\src\\Hasher\\DefaultHasher.php", "line": 29}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-responsecache/src/Hasher/DefaultHasher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\spatie\\laravel-responsecache\\src\\Hasher\\DefaultHasher.php", "line": 18}], "start": **********.376751, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 20, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 95.313, "width_percent": 4.688}]}, "models": {"data": {"Webkul\\Theme\\Models\\ThemeCustomizationTranslation": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FTheme%2Fsrc%2FModels%2FThemeCustomizationTranslation.php&line=1", "ajax": false, "filename": "ThemeCustomizationTranslation.php", "line": "?"}}, "Webkul\\Core\\Models\\Locale": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FModels%2FLocale.php&line=1", "ajax": false, "filename": "Locale.php", "line": "?"}}, "Webkul\\Core\\Models\\Channel": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FModels%2FChannel.php&line=1", "ajax": false, "filename": "Channel.php", "line": "?"}}, "Webkul\\User\\Models\\Admin": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FUser%2Fsrc%2FModels%2FAdmin.php&line=1", "ajax": false, "filename": "Admin.php", "line": "?"}}, "Webkul\\User\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FUser%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "Webkul\\Theme\\Models\\ThemeCustomization": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FTheme%2Fsrc%2FModels%2FThemeCustomization.php&line=1", "ajax": false, "filename": "ThemeCustomization.php", "line": "?"}}, "Webkul\\Core\\Models\\Currency": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FModels%2FCurrency.php&line=1", "ajax": false, "filename": "Currency.php", "line": "?"}}}, "count": 15, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "302 Found", "full_url": "http://mlk.test/admin/settings/themes/edit/46", "action_name": "admin.settings.themes.update", "controller_action": "Webkul\\Admin\\Http\\Controllers\\Settings\\ThemeController@update", "uri": "POST admin/settings/themes/edit/{id}", "controller": "Webkul\\Admin\\Http\\Controllers\\Settings\\ThemeController@update<a href=\"phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FHttp%2FControllers%2FSettings%2FThemeController.php&line=90\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "admin/settings/themes", "file": "<a href=\"phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FHttp%2FControllers%2FSettings%2FThemeController.php&line=90\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">packages/Webkul/Admin/src/Http/Controllers/Settings/ThemeController.php:90-127</a>", "middleware": "web, admin_locale, Webkul\\Core\\Http\\Middleware\\PreventRequestsDuringMaintenance, admin, Webkul\\Core\\Http\\Middleware\\NoCacheMiddleware", "duration": "266ms", "peak_memory": "42MB", "response": "Redirect to http://mlk.test/admin/settings/themes", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-449839966 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-449839966\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-273484879 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">RL8eYWcLOX1nKpLrVKRAtWfTglgDAXuESTadrPQu</span>\"\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">it</span>\"\n  \"<span class=sf-dump-key>it</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>options</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>html</span>\" => \"\"\"\n        <span class=sf-dump-str title=\"1620 characters\">&lt;div class=&quot;carousel&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"1620 characters\">    &lt;div class=&quot;carousel-track&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"1620 characters\">        &lt;div class=&quot;slide current-slide&quot; style=&quot;background-image: url(&#039;https://images.unsplash.com/photo-1506744038136-46273834b3fb?q=80&amp;w=2070&amp;auto=format&amp;fit=crop&#039;);&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"1620 characters\">            &lt;div class=&quot;hero-text&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"1620 characters\">                 &lt;h1&gt;Come un dolce raggio di sole&lt;/h1&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"1620 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"1620 characters\">                &lt;p&gt;Scopri la nostra collezione eco-sostenibile, realizzata con materiali naturali che donano una sensazione delicata come un raggio di sole in una mattinata tranquilla.&lt;/p&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"1620 characters\">            &lt;/div&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"1620 characters\">        &lt;/div&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"1620 characters\">        &lt;div class=&quot;slide&quot; style=&quot;background-image: url(&#039;https://images.unsplash.com/photo-1470770841072-f978cf4d019e?q=80&amp;w=2070&amp;auto=format&amp;fit=crop&#039;);&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"1620 characters\">            &lt;div class=&quot;hero-text&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"1620 characters\">                 &lt;h1&gt;Sussurri della Foresta&lt;/h1&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"1620 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"1620 characters\">                &lt;p&gt;Viaggia attraverso i nostri ultimi design, ispirati ai verdi intensi e alle storie non raccontate sussurrate tra le volte dell&#039;antica foresta.&lt;/p&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"1620 characters\">            &lt;/div&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"1620 characters\">        &lt;/div&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"1620 characters\">        &lt;div class=&quot;slide&quot; style=&quot;background-image: url(&#039;https://images.unsplash.com/photo-1439405326853-58f2724f4d31?q=80&amp;w=2070&amp;auto=format&amp;fit=crop&#039;);&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"1620 characters\">            &lt;div class=&quot;hero-text&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"1620 characters\">                 &lt;h1&gt;Echi dell&#039;Oceano&lt;/h1&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"1620 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"1620 characters\">                &lt;p&gt;Abbraccia la calma e la potenza del mare con la nostra nuova linea, caratterizzata da colori sereni e design fluidi che catturano il ritmo delle onde.&lt;/p&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"1620 characters\">            &lt;/div&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"1620 characters\">        &lt;/div&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"1620 characters\">    &lt;/div&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"1620 characters\">    &lt;button class=&quot;arrow left-arrow&quot;&gt;&amp;#10094;&lt;/button&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"1620 characters\">    &lt;button class=&quot;arrow right-arrow&quot;&gt;&amp;#10095;&lt;/button&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"1620 characters\">    &lt;div class=&quot;carousel-dots&quot;&gt;&lt;/div&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"1620 characters\">&lt;/div&gt;</span>\n        \"\"\"\n      \"<span class=sf-dump-key>css</span>\" => \"\"\"\n        <span class=sf-dump-str title=\"2701 characters\">.carousel {<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2701 characters\">  margin-top:4rem;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2701 characters\">    position: relative;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2701 characters\">    width: 100%;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2701 characters\">    height: 60vh;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2701 characters\">    min-height: 420px;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2701 characters\">    overflow: hidden;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2701 characters\">    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2701 characters\">}<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2701 characters\">.carousel-track {<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2701 characters\">    position: relative;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2701 characters\">    height: 100%;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2701 characters\">    display: flex;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2701 characters\">    transition: transform 0.7s cubic-bezier(0.65, 0, 0.35, 1);<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2701 characters\">}<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2701 characters\">.slide {<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2701 characters\">    position: relative;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2701 characters\">    flex: 1 0 100%;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2701 characters\">    width: 100%;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2701 characters\">    height: 100%;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2701 characters\">    background-size: cover;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2701 characters\">    background-position: center;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2701 characters\">    display: flex;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2701 characters\">    justify-content: center;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2701 characters\">    align-items: center;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2701 characters\">    color: white;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2701 characters\">}<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2701 characters\">.slide::before {<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2701 characters\">    content:&#039;&#039;;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2701 characters\">    position: absolute;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2701 characters\">    top: 0;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2701 characters\">    left: 0;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2701 characters\">    width: 100%;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2701 characters\">    height: 100%;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2701 characters\">    background-color: rgba(0, 0, 0, 0.25);<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2701 characters\">    z-index: 1;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2701 characters\">}<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2701 characters\">.hero-text {<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2701 characters\">    position: relative;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2701 characters\">    z-index: 2;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2701 characters\">    text-align: center;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2701 characters\">    max-width: 90%;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2701 characters\">    padding: 20px;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2701 characters\">    text-shadow: 0 2px 15px rgba(0, 0, 0, 0.4);<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2701 characters\">    opacity: 0;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2701 characters\">    transform: translateY(25px);<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2701 characters\">    transition: opacity 0.6s cubic-bezier(0.22, 1, 0.36, 1) 0.3s, transform 0.6s cubic-bezier(0.22, 1, 0.36, 1) 0.3s;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2701 characters\">}<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2701 characters\">.current-slide .hero-text {<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2701 characters\">    opacity: 1;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2701 characters\">    transform: translateY(0);<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2701 characters\">}<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2701 characters\">.hero-text h1 {<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2701 characters\">    font-size: 3rem;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2701 characters\">    font-weight: 700;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2701 characters\">    margin-bottom: 0.75rem;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2701 characters\">    letter-spacing: 1px;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2701 characters\">}<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2701 characters\">.hero-text p {<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2701 characters\">    font-size: 1.2rem;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2701 characters\">    font-weight: 300;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2701 characters\">    max-width: 650px;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2701 characters\">    margin: 0 auto;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2701 characters\">    line-height: 1.6;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2701 characters\">}<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2701 characters\">.arrow {<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2701 characters\">    position: absolute;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2701 characters\">    top: 50%;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2701 characters\">    transform: translateY(-50%);<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2701 characters\">    background: rgba(0, 0, 0, 0.2);<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2701 characters\">    border: none;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2701 characters\">    color: rgba(255, 255, 255, 0.9);<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2701 characters\">    font-size: 1.5rem;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2701 characters\">    padding: 10px;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2701 characters\">    width: 50px;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2701 characters\">    height: 50px;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2701 characters\">    border-radius: 4px;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2701 characters\">    cursor: pointer;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2701 characters\">    z-index: 10;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2701 characters\">    display: flex;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2701 characters\">    justify-content: center;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2701 characters\">    align-items: center;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2701 characters\">    transition: all 0.2s ease-in-out;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2701 characters\">}<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2701 characters\">.arrow:hover {<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2701 characters\">    background-color: rgba(0, 0, 0, 0.5);<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2701 characters\">    transform: translateY(-50%) scale(1.05);<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2701 characters\">}<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2701 characters\">.left-arrow {<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2701 characters\">    left: 30px;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2701 characters\">}<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2701 characters\">.right-arrow {<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2701 characters\">    right: 30px;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2701 characters\">}<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2701 characters\">.carousel-dots {<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2701 characters\">    position: absolute;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2701 characters\">    bottom: 25px;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2701 characters\">    left: 50%;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2701 characters\">    transform: translateX(-50%);<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2701 characters\">    display: flex;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2701 characters\">    gap: 10px;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2701 characters\">    z-index: 10;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2701 characters\">}<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2701 characters\">.dot {<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2701 characters\">    border: none;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2701 characters\">    width: 25px;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2701 characters\">    height: 4px;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2701 characters\">    border-radius: 2px;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2701 characters\">    background-color: rgba(255, 255, 255, 0.4);<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2701 characters\">    cursor: pointer;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2701 characters\">    transition: all 0.3s ease;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2701 characters\">}<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2701 characters\">.dot.current-dot {<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2701 characters\">    background-color: white;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2701 characters\">    width: 40px;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2701 characters\">}<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2701 characters\">@media (max-width: 768px) {<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2701 characters\">    .hero-text h1 {<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2701 characters\">        font-size: 2rem;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2701 characters\">    }<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2701 characters\">    .hero-text p {<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2701 characters\">        font-size: 1rem;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2701 characters\">    }<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2701 characters\">    .arrow {<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2701 characters\">        display: none;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2701 characters\">    }<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2701 characters\">}</span>\n        \"\"\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"14 characters\">static_content</span>\"\n  \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">slogan</span>\"\n  \"<span class=sf-dump-key>sort_order</span>\" => \"<span class=sf-dump-str>8</span>\"\n  \"<span class=sf-dump-key>channel_id</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>theme_code</span>\" => \"<span class=sf-dump-str title=\"7 characters\">default</span>\"\n  \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"2 characters\">on</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-273484879\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-30447005 data-indent-pad=\"  \"><span class=sf-dump-note>array:13</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1018 characters\">admin_locale=eyJpdiI6InlVTlI4cytSTEhwU1oxNXhFNmszTHc9PSIsInZhbHVlIjoiL1djczhIanIvN1ZXTlhGdmpwMWJhNkpqMnNiMHcxMjBic21GQ0ltRzRZV25DczdBcXZaVUNJWlFEakx1bjV6byIsIm1hYyI6IjhmNjlhYzdkYWNjMTVkNjA5ZThmMzlhYmViM2UwYTFiZDQxZDY4ZjEwZTg3NGVjZGNhOTAzODA3ODhhNjllMzQiLCJ0YWciOiIifQ%3D%3D; sidebar_collapsed=0; dark_mode=0; XSRF-TOKEN=eyJpdiI6IjlkN3pZTmwxVVBrTnlsOFV5SGJoMEE9PSIsInZhbHVlIjoiSytBSjlQVXBhaHp6VDlCaWV2RHNrZ1BBOG13VEk0TFhvdDV0Q1doUGtteHRjbDM2TC83ejFqZHA1OWhDMk9ESDdsV1NRZEd4c3N3WTlIT0RwMnV6WEtockVCbTNRN1h2dTFuOGxNNGtDYVR1Y3J0SG5mQlo2bys4VjNFVlJ4b1YiLCJtYWMiOiJiYjkxZjlhZDA2MmE2NjQ4ODc4ZjgxNDkyNjZhMGVhZTJmMGYxMGUwODI3ZTNhZmZkOWRiOTZhYWRkYjYzMDFiIiwidGFnIjoiIn0%3D; mlk_session=eyJpdiI6IjlJeVNJTmxRMFZvRVVKdmp3RUwzcGc9PSIsInZhbHVlIjoiZktZNzM2OFl1V3V1UkZjNlRERXg5anNsVUthU3VtRHZTS3ZDMTN1UHJCWDB4T1p5dnJxMS9vV1NkM2R1OGY0dFM2bDFWbDdRMFlxWXJtMmpoRjk5Y0JKNy9jVkNrOFp3dERjdUhBaGVybVBxN3lHcWlqekRGMXZWamxVOEhhTnEiLCJtYWMiOiIxMDZmZDFhMjcwN2I3ZDc0OWU1YjlkMWZlZTAxNzc2ZTJhZmViYzc5YjcyZmI3NWRkZjY5YzUxMTJjOTUyNjViIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">zh-CN,zh;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"71 characters\">http://mlk.test/admin/settings/themes/edit/46?channel=default&amp;locale=it</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">multipart/form-data; boundary=----WebKitFormBoundaryeu6nxAN25oZ92Crm</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"15 characters\">http://mlk.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">5397</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">mlk.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-30447005\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1510109284 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>admin_locale</span>\" => \"<span class=sf-dump-str title=\"5 characters\">zh_CN</span>\"\n  \"<span class=sf-dump-key>sidebar_collapsed</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>dark_mode</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">RL8eYWcLOX1nKpLrVKRAtWfTglgDAXuESTadrPQu</span>\"\n  \"<span class=sf-dump-key>mlk_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Zao9ptAmXhjVm7WD2j4izotzmKLg3wYf4OgogoiC</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1510109284\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1479924994 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 07 Aug 2025 16:10:55 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://mlk.test/admin/settings/themes</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>0</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1479924994\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1601399315 data-indent-pad=\"  \"><span class=sf-dump-note>array:10</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">RL8eYWcLOX1nKpLrVKRAtWfTglgDAXuESTadrPQu</span>\"\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>currency</span>\" => \"<span class=sf-dump-str title=\"3 characters\">EUR</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"71 characters\">http://mlk.test/admin/settings/themes/edit/46?channel=default&amp;locale=it</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>admin_locale</span>\" => \"<span class=sf-dump-str title=\"5 characters\">zh_CN</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>success</span>\" => \"<span class=sf-dump-str title=\"6 characters\">&#25104;&#21151;&#26356;&#26032;&#20027;&#39064;</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1601399315\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "302 Found", "full_url": "http://mlk.test/admin/settings/themes/edit/46", "action_name": "admin.settings.themes.update", "controller_action": "Webkul\\Admin\\Http\\Controllers\\Settings\\ThemeController@update"}, "badge": "302 Found"}}