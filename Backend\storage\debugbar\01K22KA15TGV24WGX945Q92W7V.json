{"__meta": {"id": "01K22KA15TGV24WGX945Q92W7V", "datetime": "2025-08-07 16:45:01", "utime": **********.115178, "method": "GET", "uri": "/", "ip": "127.0.0.1"}, "modules": {"count": 3, "modules": [{"name": "Webkul\\Core", "models": ["Webkul\\Core\\Models\\Channel (1)", "Webkul\\Core\\Models\\Locale (12)", "Webkul\\Core\\Models\\Currency (1)", "Webkul\\Core\\Models\\ChannelTranslation (5)"], "views": [], "queries": [{"sql": "select * from `channels` where `hostname` in ('mlk.test', 'http://mlk.test', 'https://mlk.test')", "duration": 3.24, "duration_str": "3.24s", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 2.13, "duration_str": "2.13s", "connection": "mlk"}, {"sql": "select `currencies`.*, `channel_currencies`.`channel_id` as `pivot_channel_id`, `channel_currencies`.`currency_id` as `pivot_currency_id` from `currencies` inner join `channel_currencies` on `currencies`.`id` = `channel_currencies`.`currency_id` where `channel_currencies`.`channel_id` = 1", "duration": 4.22, "duration_str": "4.22s", "connection": "mlk"}, {"sql": "select count(*) as aggregate from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.38, "duration_str": "380ms", "connection": "mlk"}, {"sql": "select `name` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1 and `code` = 'it' order by `name` asc limit 1", "duration": 0.27, "duration_str": "270ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1 order by `name` asc", "duration": 0.29, "duration_str": "290ms", "connection": "mlk"}, {"sql": "select count(*) as aggregate from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.43, "duration_str": "430ms", "connection": "mlk"}, {"sql": "select `name` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1 and `code` = 'it' order by `name` asc limit 1", "duration": 0.24, "duration_str": "240ms", "connection": "mlk"}]}, {"name": "Webkul\\Theme", "models": ["Webkul\\Theme\\Models\\ThemeCustomization (14)", "Webkul\\Theme\\Models\\ThemeCustomizationTranslation (72)"], "views": [], "queries": [{"sql": "select * from `theme_customizations` where `status` = 1 and `channel_id` = 1 and `theme_code` = 'default' order by `sort_order` asc", "duration": 4.1, "duration_str": "4.1s", "connection": "mlk"}, {"sql": "select * from `theme_customization_translations` where `theme_customization_translations`.`theme_customization_id` in (14, 17, 18, 19, 20, 22, 23, 24, 28, 38, 39, 41, 44)", "duration": 2.09, "duration_str": "2.09s", "connection": "mlk"}, {"sql": "select * from `theme_customizations` where `type` = 'services_content' and `status` = 1 and `theme_code` = 'default' and `channel_id` = 1", "duration": 0.26, "duration_str": "260ms", "connection": "mlk"}, {"sql": "select * from `theme_customizations` where `type` = 'footer_links' and `status` = 1 and `theme_code` = 'default' and `channel_id` = 1", "duration": 0.27, "duration_str": "270ms", "connection": "mlk"}, {"sql": "select * from `theme_customization_translations` where `theme_customization_translations`.`theme_customization_id` in (24)", "duration": 0.23, "duration_str": "230ms", "connection": "mlk"}]}, {"name": "Webkul\\User", "models": [], "views": [], "queries": [{"sql": "select count(*) as aggregate from `admins`", "duration": 0.2, "duration_str": "200ms", "connection": "mlk"}, {"sql": "select count(*) as aggregate from `admins`", "duration": 0.15, "duration_str": "150ms", "connection": "mlk"}]}]}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.748383, "end": **********.122542, "duration": 0.3741588592529297, "duration_str": "374ms", "measures": [{"label": "Booting", "start": **********.748383, "relative_start": 0, "end": **********.919352, "relative_end": **********.919352, "duration": 0.*****************, "duration_str": "171ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.919363, "relative_start": 0.*****************, "end": **********.122544, "relative_end": 2.1457672119140625e-06, "duration": 0.*****************, "duration_str": "203ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.9295, "relative_start": 0.*****************, "end": **********.932199, "relative_end": **********.932199, "duration": 0.0026988983154296875, "duration_str": "2.7ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.00465, "relative_start": 0.*****************, "end": **********.113821, "relative_end": **********.113821, "duration": 0.*****************, "duration_str": "109ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: shop::home.index", "start": **********.006027, "relative_start": 0.****************, "end": **********.006027, "relative_end": **********.006027, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.carousel.index", "start": **********.019775, "relative_start": 0.****************, "end": **********.019775, "relative_end": **********.019775, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.media.images.lazy", "start": **********.020346, "relative_start": 0.27196288108825684, "end": **********.020346, "relative_end": **********.020346, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.products.carousel", "start": **********.024676, "relative_start": 0.2762930393218994, "end": **********.024676, "relative_end": **********.024676, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.shimmer.products.carousel", "start": **********.025289, "relative_start": 0.27690601348876953, "end": **********.025289, "relative_end": **********.025289, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.shimmer.products.cards.grid", "start": **********.025774, "relative_start": 0.2773909568786621, "end": **********.025774, "relative_end": **********.025774, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.products.card", "start": **********.026296, "relative_start": 0.27791285514831543, "end": **********.026296, "relative_end": **********.026296, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.media.images.lazy", "start": **********.026965, "relative_start": 0.2785818576812744, "end": **********.026965, "relative_end": **********.026965, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.products.ratings", "start": **********.028757, "relative_start": 0.28037405014038086, "end": **********.028757, "relative_end": **********.028757, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.media.images.lazy", "start": **********.032485, "relative_start": 0.2841019630432129, "end": **********.032485, "relative_end": **********.032485, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.products.ratings", "start": **********.034405, "relative_start": 0.2860219478607178, "end": **********.034405, "relative_end": **********.034405, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.button.index", "start": **********.035428, "relative_start": 0.2870450019836426, "end": **********.035428, "relative_end": **********.035428, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.shimmer.products.carousel", "start": **********.035811, "relative_start": 0.2874279022216797, "end": **********.035811, "relative_end": **********.035811, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.shimmer.products.cards.grid", "start": **********.036013, "relative_start": 0.2876298427581787, "end": **********.036013, "relative_end": **********.036013, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.categories.carousel", "start": **********.037722, "relative_start": 0.2893390655517578, "end": **********.037722, "relative_end": **********.037722, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.shimmer.categories.carousel", "start": **********.038236, "relative_start": 0.2898528575897217, "end": **********.038236, "relative_end": **********.038236, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.media.images.lazy", "start": **********.039471, "relative_start": 0.2910878658294678, "end": **********.039471, "relative_end": **********.039471, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.shimmer.categories.carousel", "start": **********.039694, "relative_start": 0.29131102561950684, "end": **********.039694, "relative_end": **********.039694, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.products.carousel", "start": **********.044609, "relative_start": 0.29622602462768555, "end": **********.044609, "relative_end": **********.044609, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.shimmer.products.carousel", "start": **********.04492, "relative_start": 0.296536922454834, "end": **********.04492, "relative_end": **********.04492, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.shimmer.products.cards.grid", "start": **********.045123, "relative_start": 0.2967400550842285, "end": **********.045123, "relative_end": **********.045123, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.products.carousel", "start": **********.046772, "relative_start": 0.2983889579772949, "end": **********.046772, "relative_end": **********.046772, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.shimmer.products.carousel", "start": **********.047054, "relative_start": 0.29867100715637207, "end": **********.047054, "relative_end": **********.047054, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.shimmer.products.cards.grid", "start": **********.047244, "relative_start": 0.298861026763916, "end": **********.047244, "relative_end": **********.047244, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.products.carousel", "start": **********.048814, "relative_start": 0.3004310131072998, "end": **********.048814, "relative_end": **********.048814, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.shimmer.products.carousel", "start": **********.049081, "relative_start": 0.30069804191589355, "end": **********.049081, "relative_end": **********.049081, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.shimmer.products.cards.grid", "start": **********.049268, "relative_start": 0.3008849620819092, "end": **********.049268, "relative_end": **********.049268, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.carousel.index", "start": **********.050881, "relative_start": 0.30249786376953125, "end": **********.050881, "relative_end": **********.050881, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.carousel.index", "start": **********.055086, "relative_start": 0.3067028522491455, "end": **********.055086, "relative_end": **********.055086, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.layouts.index", "start": **********.055518, "relative_start": 0.30713486671447754, "end": **********.055518, "relative_end": **********.055518, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.flash-group.index", "start": **********.059145, "relative_start": 0.3107619285583496, "end": **********.059145, "relative_end": **********.059145, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.flash-group.item", "start": **********.059603, "relative_start": 0.3112199306488037, "end": **********.059603, "relative_end": **********.059603, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.flash-group.item", "start": **********.059887, "relative_start": 0.31150388717651367, "end": **********.059887, "relative_end": **********.059887, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.modal.confirm", "start": **********.060257, "relative_start": 0.3118739128112793, "end": **********.060257, "relative_end": **********.060257, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.layouts.header.index", "start": **********.060719, "relative_start": 0.3123359680175781, "end": **********.060719, "relative_end": **********.060719, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.layouts.header.desktop.top", "start": **********.06254, "relative_start": 0.31415700912475586, "end": **********.06254, "relative_end": **********.06254, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.dropdown.index", "start": **********.063305, "relative_start": 0.31492185592651367, "end": **********.063305, "relative_end": **********.063305, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.dropdown.index", "start": **********.071005, "relative_start": 0.32262206077575684, "end": **********.071005, "relative_end": **********.071005, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.layouts.header.desktop.index", "start": **********.073443, "relative_start": 0.3250598907470703, "end": **********.073443, "relative_end": **********.073443, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.layouts.header.desktop.bottom", "start": **********.073905, "relative_start": 0.32552194595336914, "end": **********.073905, "relative_end": **********.073905, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::search.images.index", "start": **********.076962, "relative_start": 0.3285789489746094, "end": **********.076962, "relative_end": **********.076962, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::checkout.cart.mini-cart", "start": **********.078784, "relative_start": 0.3304009437561035, "end": **********.078784, "relative_end": **********.078784, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.quantity-changer.index", "start": **********.081754, "relative_start": 0.3333709239959717, "end": **********.081754, "relative_end": **********.081754, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.drawer.index", "start": **********.082437, "relative_start": 0.33405399322509766, "end": **********.082437, "relative_end": **********.082437, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.dropdown.index", "start": **********.084336, "relative_start": 0.3359529972076416, "end": **********.084336, "relative_end": **********.084336, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.drawer.index", "start": **********.085238, "relative_start": 0.3368549346923828, "end": **********.085238, "relative_end": **********.085238, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.layouts.header.mobile.index", "start": **********.085724, "relative_start": 0.3373410701751709, "end": **********.085724, "relative_end": **********.085724, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::checkout.cart.mini-cart", "start": **********.088281, "relative_start": 0.33989787101745605, "end": **********.088281, "relative_end": **********.088281, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.dropdown.index", "start": **********.088857, "relative_start": 0.34047389030456543, "end": **********.088857, "relative_end": **********.088857, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::search.images.index", "start": **********.089852, "relative_start": 0.34146904945373535, "end": **********.089852, "relative_end": **********.089852, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.drawer.index", "start": **********.091625, "relative_start": 0.34324193000793457, "end": **********.091625, "relative_end": **********.091625, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.drawer.index", "start": **********.093094, "relative_start": 0.3447110652923584, "end": **********.093094, "relative_end": **********.093094, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.drawer.index", "start": **********.093376, "relative_start": 0.34499287605285645, "end": **********.093376, "relative_end": **********.093376, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.layouts.services", "start": **********.094533, "relative_start": 0.34614992141723633, "end": **********.094533, "relative_end": **********.094533, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.layouts.footer.index", "start": **********.096354, "relative_start": 0.34797096252441406, "end": **********.096354, "relative_end": **********.096354, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.accordion.index", "start": **********.104859, "relative_start": 0.3564760684967041, "end": **********.104859, "relative_end": **********.104859, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.form.control-group.control", "start": **********.110367, "relative_start": 0.3619840145111084, "end": **********.110367, "relative_end": **********.110367, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.form.control-group.error", "start": **********.111074, "relative_start": 0.36269092559814453, "end": **********.111074, "relative_end": **********.111074, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.form.index", "start": **********.111418, "relative_start": 0.3630349636077881, "end": **********.111418, "relative_end": **********.111418, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core::blade.tracer.style", "start": **********.112093, "relative_start": 0.3637099266052246, "end": **********.112093, "relative_end": **********.112093, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: paypal::checkout.onepage.paypal-smart-button", "start": **********.112569, "relative_start": 0.36418604850769043, "end": **********.112569, "relative_end": **********.112569, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}]}, "memory": {"peak_usage": 45331400, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.2.0", "Environment": "local", "Debug Mode": "Enabled", "URL": "mlk.test", "Timezone": "Africa/Lagos", "Locale": "it"}}, "views": {"count": 61, "nb_templates": 61, "templates": [{"name": "1x shop::home.index", "param_count": null, "params": [], "start": **********.005998, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/home/<USER>", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fhome%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "shop::home.index"}, {"name": "3x shop::components.carousel.index", "param_count": null, "params": [], "start": **********.019754, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/components/carousel/index.blade.phpshop::components.carousel.index", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fcarousel%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 3, "name_original": "shop::components.carousel.index"}, {"name": "4x shop::components.media.images.lazy", "param_count": null, "params": [], "start": **********.020327, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/components/media/images/lazy.blade.phpshop::components.media.images.lazy", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fmedia%2Fimages%2Flazy.blade.php&line=1", "ajax": false, "filename": "lazy.blade.php", "line": "?"}, "render_count": 4, "name_original": "shop::components.media.images.lazy"}, {"name": "4x shop::components.products.carousel", "param_count": null, "params": [], "start": **********.024655, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/components/products/carousel.blade.phpshop::components.products.carousel", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fproducts%2Fcarousel.blade.php&line=1", "ajax": false, "filename": "carousel.blade.php", "line": "?"}, "render_count": 4, "name_original": "shop::components.products.carousel"}, {"name": "5x shop::components.shimmer.products.carousel", "param_count": null, "params": [], "start": **********.02527, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/components/shimmer/products/carousel.blade.phpshop::components.shimmer.products.carousel", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fshimmer%2Fproducts%2Fcarousel.blade.php&line=1", "ajax": false, "filename": "carousel.blade.php", "line": "?"}, "render_count": 5, "name_original": "shop::components.shimmer.products.carousel"}, {"name": "5x shop::components.shimmer.products.cards.grid", "param_count": null, "params": [], "start": **********.025743, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/components/shimmer/products/cards/grid.blade.phpshop::components.shimmer.products.cards.grid", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fshimmer%2Fproducts%2Fcards%2Fgrid.blade.php&line=1", "ajax": false, "filename": "grid.blade.php", "line": "?"}, "render_count": 5, "name_original": "shop::components.shimmer.products.cards.grid"}, {"name": "1x shop::components.products.card", "param_count": null, "params": [], "start": **********.026273, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/components/products/card.blade.phpshop::components.products.card", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fproducts%2Fcard.blade.php&line=1", "ajax": false, "filename": "card.blade.php", "line": "?"}, "render_count": 1, "name_original": "shop::components.products.card"}, {"name": "2x shop::components.products.ratings", "param_count": null, "params": [], "start": **********.028739, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/components/products/ratings.blade.phpshop::components.products.ratings", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fproducts%2Fratings.blade.php&line=1", "ajax": false, "filename": "ratings.blade.php", "line": "?"}, "render_count": 2, "name_original": "shop::components.products.ratings"}, {"name": "1x shop::components.button.index", "param_count": null, "params": [], "start": **********.035409, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/components/button/index.blade.phpshop::components.button.index", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fbutton%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "shop::components.button.index"}, {"name": "1x shop::components.categories.carousel", "param_count": null, "params": [], "start": **********.037703, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/components/categories/carousel.blade.phpshop::components.categories.carousel", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fcategories%2Fcarousel.blade.php&line=1", "ajax": false, "filename": "carousel.blade.php", "line": "?"}, "render_count": 1, "name_original": "shop::components.categories.carousel"}, {"name": "2x shop::components.shimmer.categories.carousel", "param_count": null, "params": [], "start": **********.038216, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/components/shimmer/categories/carousel.blade.phpshop::components.shimmer.categories.carousel", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fshimmer%2Fcategories%2Fcarousel.blade.php&line=1", "ajax": false, "filename": "carousel.blade.php", "line": "?"}, "render_count": 2, "name_original": "shop::components.shimmer.categories.carousel"}, {"name": "1x shop::components.layouts.index", "param_count": null, "params": [], "start": **********.055499, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/index.blade.phpshop::components.layouts.index", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Flayouts%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "shop::components.layouts.index"}, {"name": "1x shop::components.flash-group.index", "param_count": null, "params": [], "start": **********.059127, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/components/flash-group/index.blade.phpshop::components.flash-group.index", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fflash-group%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "shop::components.flash-group.index"}, {"name": "2x shop::components.flash-group.item", "param_count": null, "params": [], "start": **********.059584, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/components/flash-group/item.blade.phpshop::components.flash-group.item", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fflash-group%2Fitem.blade.php&line=1", "ajax": false, "filename": "item.blade.php", "line": "?"}, "render_count": 2, "name_original": "shop::components.flash-group.item"}, {"name": "1x shop::components.modal.confirm", "param_count": null, "params": [], "start": **********.060236, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/components/modal/confirm.blade.phpshop::components.modal.confirm", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fmodal%2Fconfirm.blade.php&line=1", "ajax": false, "filename": "confirm.blade.php", "line": "?"}, "render_count": 1, "name_original": "shop::components.modal.confirm"}, {"name": "1x shop::components.layouts.header.index", "param_count": null, "params": [], "start": **********.060699, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/header/index.blade.phpshop::components.layouts.header.index", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Flayouts%2Fheader%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "shop::components.layouts.header.index"}, {"name": "1x shop::components.layouts.header.desktop.top", "param_count": null, "params": [], "start": **********.062518, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/header/desktop/top.blade.phpshop::components.layouts.header.desktop.top", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Flayouts%2Fheader%2Fdesktop%2Ftop.blade.php&line=1", "ajax": false, "filename": "top.blade.php", "line": "?"}, "render_count": 1, "name_original": "shop::components.layouts.header.desktop.top"}, {"name": "4x shop::components.dropdown.index", "param_count": null, "params": [], "start": **********.06328, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/components/dropdown/index.blade.phpshop::components.dropdown.index", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fdropdown%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 4, "name_original": "shop::components.dropdown.index"}, {"name": "1x shop::components.layouts.header.desktop.index", "param_count": null, "params": [], "start": **********.07342, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/header/desktop/index.blade.phpshop::components.layouts.header.desktop.index", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Flayouts%2Fheader%2Fdesktop%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "shop::components.layouts.header.desktop.index"}, {"name": "1x shop::components.layouts.header.desktop.bottom", "param_count": null, "params": [], "start": **********.073879, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/header/desktop/bottom.blade.phpshop::components.layouts.header.desktop.bottom", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Flayouts%2Fheader%2Fdesktop%2Fbottom.blade.php&line=1", "ajax": false, "filename": "bottom.blade.php", "line": "?"}, "render_count": 1, "name_original": "shop::components.layouts.header.desktop.bottom"}, {"name": "2x shop::search.images.index", "param_count": null, "params": [], "start": **********.076943, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/search/images/index.blade.phpshop::search.images.index", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fsearch%2Fimages%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 2, "name_original": "shop::search.images.index"}, {"name": "2x shop::checkout.cart.mini-cart", "param_count": null, "params": [], "start": **********.078765, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/checkout/cart/mini-cart.blade.phpshop::checkout.cart.mini-cart", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcheckout%2Fcart%2Fmini-cart.blade.php&line=1", "ajax": false, "filename": "mini-cart.blade.php", "line": "?"}, "render_count": 2, "name_original": "shop::checkout.cart.mini-cart"}, {"name": "1x shop::components.quantity-changer.index", "param_count": null, "params": [], "start": **********.081736, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/components/quantity-changer/index.blade.phpshop::components.quantity-changer.index", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fquantity-changer%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "shop::components.quantity-changer.index"}, {"name": "5x shop::components.drawer.index", "param_count": null, "params": [], "start": **********.082413, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/components/drawer/index.blade.phpshop::components.drawer.index", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fdrawer%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 5, "name_original": "shop::components.drawer.index"}, {"name": "1x shop::components.layouts.header.mobile.index", "param_count": null, "params": [], "start": **********.085706, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/header/mobile/index.blade.phpshop::components.layouts.header.mobile.index", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Flayouts%2Fheader%2Fmobile%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "shop::components.layouts.header.mobile.index"}, {"name": "1x shop::components.layouts.services", "param_count": null, "params": [], "start": **********.094515, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/services.blade.phpshop::components.layouts.services", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Flayouts%2Fservices.blade.php&line=1", "ajax": false, "filename": "services.blade.php", "line": "?"}, "render_count": 1, "name_original": "shop::components.layouts.services"}, {"name": "1x shop::components.layouts.footer.index", "param_count": null, "params": [], "start": **********.096336, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/footer/index.blade.phpshop::components.layouts.footer.index", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Flayouts%2Ffooter%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "shop::components.layouts.footer.index"}, {"name": "1x shop::components.accordion.index", "param_count": null, "params": [], "start": **********.104837, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/components/accordion/index.blade.phpshop::components.accordion.index", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Faccordion%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "shop::components.accordion.index"}, {"name": "1x shop::components.form.control-group.control", "param_count": null, "params": [], "start": **********.110347, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/components/form/control-group/control.blade.phpshop::components.form.control-group.control", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fform%2Fcontrol-group%2Fcontrol.blade.php&line=1", "ajax": false, "filename": "control.blade.php", "line": "?"}, "render_count": 1, "name_original": "shop::components.form.control-group.control"}, {"name": "1x shop::components.form.control-group.error", "param_count": null, "params": [], "start": **********.111055, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/components/form/control-group/error.blade.phpshop::components.form.control-group.error", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fform%2Fcontrol-group%2Ferror.blade.php&line=1", "ajax": false, "filename": "error.blade.php", "line": "?"}, "render_count": 1, "name_original": "shop::components.form.control-group.error"}, {"name": "1x shop::components.form.index", "param_count": null, "params": [], "start": **********.1114, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/components/form/index.blade.phpshop::components.form.index", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fform%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "shop::components.form.index"}, {"name": "1x core::blade.tracer.style", "param_count": null, "params": [], "start": **********.112076, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src/resources/views/blade/tracer/style.blade.phpcore::blade.tracer.style", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FResources%2Fviews%2Fblade%2Ftracer%2Fstyle.blade.php&line=1", "ajax": false, "filename": "style.blade.php", "line": "?"}, "render_count": 1, "name_original": "core::blade.tracer.style"}, {"name": "1x paypal::checkout.onepage.paypal-smart-button", "param_count": null, "params": [], "start": **********.112551, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Paypal\\src/resources/views/checkout/onepage/paypal-smart-button.blade.phppaypal::checkout.onepage.paypal-smart-button", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FPaypal%2Fsrc%2FResources%2Fviews%2Fcheckout%2Fonepage%2Fpaypal-smart-button.blade.php&line=1", "ajax": false, "filename": "paypal-smart-button.blade.php", "line": "?"}, "render_count": 1, "name_original": "paypal::checkout.onepage.paypal-smart-button"}]}, "queries": {"count": 19, "nb_statements": 19, "nb_visible_statements": 19, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.0255, "accumulated_duration_str": "25.5ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `channels` where `hostname` in ('mlk.test', 'http://mlk.test', 'https://mlk.test')", "type": "query", "params": [], "bindings": ["mlk.test", "http://mlk.test", "https://mlk.test"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 578}, {"index": 16, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Core.php", "line": 143}, {"index": 17, "namespace": "middleware", "name": "theme", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Middleware\\Theme.php", "line": 19}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.945953, "duration": 0.0032400000000000003, "duration_str": "3.24ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:578", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 578}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=578", "ajax": false, "filename": "BaseRepository.php", "line": "578"}, "connection": "mlk", "explain": null, "start_percent": 0, "width_percent": 12.706}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": "middleware", "name": "admin_locale", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Middleware\\Locale.php", "line": 25}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 23, "namespace": "middleware", "name": "theme", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Middleware\\Theme.php", "line": 32}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.952992, "duration": 0.00213, "duration_str": "2.13ms", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 12.706, "width_percent": 8.353}, {"sql": "select `currencies`.*, `channel_currencies`.`channel_id` as `pivot_channel_id`, `channel_currencies`.`currency_id` as `pivot_currency_id` from `currencies` inner join `channel_currencies` on `currencies`.`id` = `channel_currencies`.`currency_id` where `channel_currencies`.`channel_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": "middleware", "name": "currency", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Middleware\\Currency.php", "line": 25}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 23, "namespace": "middleware", "name": "admin_locale", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Middleware\\Locale.php", "line": 40}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.957464, "duration": 0.00422, "duration_str": "4.22ms", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 21.059, "width_percent": 16.549}, {"sql": "select exists (select 1 from information_schema.tables where table_schema = 'mlk' and table_name = 'admins' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 32}, {"index": 14, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 44}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 1004}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 832}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 1078}], "start": **********.967072, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "DatabaseManager.php:32", "source": {"index": 13, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 32}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FInstaller%2Fsrc%2FHelpers%2FDatabaseManager.php&line=32", "ajax": false, "filename": "DatabaseManager.php", "line": "32"}, "connection": "mlk", "explain": null, "start_percent": 37.608, "width_percent": 1.333}, {"sql": "select count(*) as aggregate from `admins`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 38}, {"index": 16, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 44}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 1004}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 832}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 1078}], "start": **********.968189, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "DatabaseManager.php:38", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 38}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FInstaller%2Fsrc%2FHelpers%2FDatabaseManager.php&line=38", "ajax": false, "filename": "DatabaseManager.php", "line": "38"}, "connection": "mlk", "explain": null, "start_percent": 38.941, "width_percent": 0.784}, {"sql": "select exists (select 1 from information_schema.tables where table_schema = 'mlk' and table_name = 'admins' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 32}, {"index": 14, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 59}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 16, "namespace": "middleware", "name": "currency", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Middleware\\Currency.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.969115, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "DatabaseManager.php:32", "source": {"index": 13, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 32}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FInstaller%2Fsrc%2FHelpers%2FDatabaseManager.php&line=32", "ajax": false, "filename": "DatabaseManager.php", "line": "32"}, "connection": "mlk", "explain": null, "start_percent": 39.725, "width_percent": 0.902}, {"sql": "select count(*) as aggregate from `admins`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 38}, {"index": 16, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 18, "namespace": "middleware", "name": "currency", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Middleware\\Currency.php", "line": 40}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.970016, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "DatabaseManager.php:38", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 38}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FInstaller%2Fsrc%2FHelpers%2FDatabaseManager.php&line=38", "ajax": false, "filename": "DatabaseManager.php", "line": "38"}, "connection": "mlk", "explain": null, "start_percent": 40.627, "width_percent": 0.588}, {"sql": "insert into `jobs` (`queue`, `attempts`, `reserved_at`, `available_at`, `created_at`, `payload`) values ('default', 0, null, **********, **********, '{\\\"uuid\\\":\\\"91f7e518-735b-453e-a4ee-46698c287d18\\\",\\\"displayName\\\":\\\"Webkul\\\\\\\\Core\\\\\\\\Jobs\\\\\\\\UpdateCreateVisitIndex\\\",\\\"job\\\":\\\"Illuminate\\\\\\\\Queue\\\\\\\\CallQueuedHandler@call\\\",\\\"maxTries\\\":null,\\\"maxExceptions\\\":null,\\\"failOnTimeout\\\":false,\\\"backoff\\\":null,\\\"timeout\\\":null,\\\"retryUntil\\\":null,\\\"data\\\":{\\\"commandName\\\":\\\"Webkul\\\\\\\\Core\\\\\\\\Jobs\\\\\\\\UpdateCreateVisitIndex\\\",\\\"command\\\":\\\"O:39:\\\\\\\"Webkul\\\\\\\\Core\\\\\\\\Jobs\\\\\\\\UpdateCreateVisitIndex\\\\\\\":1:{s:6:\\\\\\\"\\\\u0000*\\\\u0000log\\\\\\\";a:14:{s:6:\\\\\\\"method\\\\\\\";s:3:\\\\\\\"GET\\\\\\\";s:7:\\\\\\\"request\\\\\\\";a:0:{}s:3:\\\\\\\"url\\\\\\\";s:15:\\\\\\\"http:\\\\/\\\\/mlk.test\\\\\\\";s:7:\\\\\\\"referer\\\\\\\";s:16:\\\\\\\"http:\\\\/\\\\/mlk.test\\\\/\\\\\\\";s:9:\\\\\\\"languages\\\\\\\";a:0:{}s:9:\\\\\\\"useragent\\\\\\\";s:111:\\\\\\\"Mozilla\\\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\\\/537.36 (KHTML, like Gecko) Chrome\\\\/********* Safari\\\\/537.36\\\\\\\";s:7:\\\\\\\"headers\\\\\\\";a:10:{s:6:\\\\\\\"cookie\\\\\\\";a:1:{i:0;s:743:\\\\\\\"sidebar_collapsed=0; dark_mode=0; XSRF-TOKEN=eyJpdiI6IkhGbE5MbVdRc3RFMDVOYVBVb3FZN0E9PSIsInZhbHVlIjoiK2Nad1Iza2pIWDUrWFBQdStUelNwaFkyeExTM01HUi8vWkxXV3VyMW9GVlBndHVnbUovYzdlL2xaUHE5TVJWRFZWV3NBMGN3bU13ZTVkRHV0QXBGMEtMUG45eGtmajI3dnBGVGM4cE5OY2VSaldyT1drYXVOcWZBWHZhK0g0Y3kiLCJtYWMiOiI2MjAzMGE5YjcwMTc3Y2Q3MDk3NDRjOTFiY2I0N2RjMDA0NTNjZDlkZjI1NDRhNzBlZjI0ZTAxOTZiOTYzODQxIiwidGFnIjoiIn0%3D; mlk_session=eyJpdiI6IkQvMGhuN3RFVGtQMVk2N0YraThFc0E9PSIsInZhbHVlIjoiZFNqdVhlbXVPWCtZVE9lTE5wL2wvS3hPajZlMlV2M1U2OThldkJBOFRYTFNVY25FL2Zoc08yZ1FQdlRWTHlJdExESGJseFF2SDJVSjdTcldULzhhR2Y0K1JxOXJrd3BIckV1eW15RDB3aTBBb0t1eTZtTzFXZUZTSlY3ZkNzZlQiLCJtYWMiOiJkMWE2ZWFjNTBmNjRjMDdkYjE0OWQ1MTk4MWM0NDFjY2RjYjMwMTIwYmJlMGMxNjVmZjdlZThhOTc2MzEzZjM3IiwidGFnIjoiIn0%3D\\\\\\\";}s:15:\\\\\\\"accept-language\\\\\\\";a:1:{i:0;s:23:\\\\\\\"zh-CN,zh;q=0.9,en;q=0.8\\\\\\\";}s:15:\\\\\\\"accept-encoding\\\\\\\";a:1:{i:0;s:13:\\\\\\\"gzip, deflate\\\\\\\";}s:7:\\\\\\\"referer\\\\\\\";a:1:{i:0;s:16:\\\\\\\"http:\\\\/\\\\/mlk.test\\\\/\\\\\\\";}s:6:\\\\\\\"accept\\\\\\\";a:1:{i:0;s:135:\\\\\\\"text\\\\/html,application\\\\/xhtml+xml,application\\\\/xml;q=0.9,image\\\\/avif,image\\\\/webp,image\\\\/apng,*\\\\/*;q=0.8,application\\\\/signed-exchange;v=b3;q=0.7\\\\\\\";}s:10:\\\\\\\"user-agent\\\\\\\";a:1:{i:0;s:111:\\\\\\\"Mozilla\\\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\\\/537.36 (KHTML, like Gecko) Chrome\\\\/********* Safari\\\\/537.36\\\\\\\";}s:25:\\\\\\\"upgrade-insecure-requests\\\\\\\";a:1:{i:0;s:1:\\\\\\\"1\\\\\\\";}s:13:\\\\\\\"cache-control\\\\\\\";a:1:{i:0;s:9:\\\\\\\"max-age=0\\\\\\\";}s:10:\\\\\\\"connection\\\\\\\";a:1:{i:0;s:10:\\\\\\\"keep-alive\\\\\\\";}s:4:\\\\\\\"host\\\\\\\";a:1:{i:0;s:8:\\\\\\\"mlk.test\\\\\\\";}}s:6:\\\\\\\"device\\\\\\\";s:0:\\\\\\\"\\\\\\\";s:8:\\\\\\\"platform\\\\\\\";s:7:\\\\\\\"Windows\\\\\\\";s:7:\\\\\\\"browser\\\\\\\";s:6:\\\\\\\"Chrome\\\\\\\";s:2:\\\\\\\"ip\\\\\\\";s:9:\\\\\\\"127.0.0.1\\\\\\\";s:10:\\\\\\\"visitor_id\\\\\\\";N;s:12:\\\\\\\"visitor_type\\\\\\\";N;s:10:\\\\\\\"channel_id\\\\\\\";i:1;}}\\\"}}')", "type": "query", "params": [], "bindings": ["default", 0, null, **********, **********, "{\"uuid\":\"91f7e518-735b-453e-a4ee-46698c287d18\",\"displayName\":\"Webkul\\\\Core\\\\Jobs\\\\UpdateCreateVisitIndex\",\"job\":\"Illuminate\\\\Queue\\\\CallQ<PERSON>ued<PERSON><PERSON><PERSON>@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"Webkul\\\\Core\\\\Jobs\\\\UpdateCreateVisitIndex\",\"command\":\"O:39:\\\"Webkul\\\\Core\\\\Jobs\\\\UpdateCreateVisitIndex\\\":1:{s:6:\\\"\\u0000*\\u0000log\\\";a:14:{s:6:\\\"method\\\";s:3:\\\"GET\\\";s:7:\\\"request\\\";a:0:{}s:3:\\\"url\\\";s:15:\\\"http:\\/\\/mlk.test\\\";s:7:\\\"referer\\\";s:16:\\\"http:\\/\\/mlk.test\\/\\\";s:9:\\\"languages\\\";a:0:{}s:9:\\\"useragent\\\";s:111:\\\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\\\";s:7:\\\"headers\\\";a:10:{s:6:\\\"cookie\\\";a:1:{i:0;s:743:\\\"sidebar_collapsed=0; dark_mode=0; XSRF-TOKEN=eyJpdiI6IkhGbE5MbVdRc3RFMDVOYVBVb3FZN0E9PSIsInZhbHVlIjoiK2Nad1Iza2pIWDUrWFBQdStUelNwaFkyeExTM01HUi8vWkxXV3VyMW9GVlBndHVnbUovYzdlL2xaUHE5TVJWRFZWV3NBMGN3bU13ZTVkRHV0QXBGMEtMUG45eGtmajI3dnBGVGM4cE5OY2VSaldyT1drYXVOcWZBWHZhK0g0Y3kiLCJtYWMiOiI2MjAzMGE5YjcwMTc3Y2Q3MDk3NDRjOTFiY2I0N2RjMDA0NTNjZDlkZjI1NDRhNzBlZjI0ZTAxOTZiOTYzODQxIiwidGFnIjoiIn0%3D; mlk_session=eyJpdiI6IkQvMGhuN3RFVGtQMVk2N0YraThFc0E9PSIsInZhbHVlIjoiZFNqdVhlbXVPWCtZVE9lTE5wL2wvS3hPajZlMlV2M1U2OThldkJBOFRYTFNVY25FL2Zoc08yZ1FQdlRWTHlJdExESGJseFF2SDJVSjdTcldULzhhR2Y0K1JxOXJrd3BIckV1eW15RDB3aTBBb0t1eTZtTzFXZUZTSlY3ZkNzZlQiLCJtYWMiOiJkMWE2ZWFjNTBmNjRjMDdkYjE0OWQ1MTk4MWM0NDFjY2RjYjMwMTIwYmJlMGMxNjVmZjdlZThhOTc2MzEzZjM3IiwidGFnIjoiIn0%3D\\\";}s:15:\\\"accept-language\\\";a:1:{i:0;s:23:\\\"zh-CN,zh;q=0.9,en;q=0.8\\\";}s:15:\\\"accept-encoding\\\";a:1:{i:0;s:13:\\\"gzip, deflate\\\";}s:7:\\\"referer\\\";a:1:{i:0;s:16:\\\"http:\\/\\/mlk.test\\/\\\";}s:6:\\\"accept\\\";a:1:{i:0;s:135:\\\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,image\\/avif,image\\/webp,image\\/apng,*\\/*;q=0.8,application\\/signed-exchange;v=b3;q=0.7\\\";}s:10:\\\"user-agent\\\";a:1:{i:0;s:111:\\\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\\\";}s:25:\\\"upgrade-insecure-requests\\\";a:1:{i:0;s:1:\\\"1\\\";}s:13:\\\"cache-control\\\";a:1:{i:0;s:9:\\\"max-age=0\\\";}s:10:\\\"connection\\\";a:1:{i:0;s:10:\\\"keep-alive\\\";}s:4:\\\"host\\\";a:1:{i:0;s:8:\\\"mlk.test\\\";}}s:6:\\\"device\\\";s:0:\\\"\\\";s:8:\\\"platform\\\";s:7:\\\"Windows\\\";s:7:\\\"browser\\\";s:6:\\\"Chrome\\\";s:2:\\\"ip\\\";s:9:\\\"127.0.0.1\\\";s:10:\\\"visitor_id\\\";N;s:12:\\\"visitor_type\\\";N;s:10:\\\"channel_id\\\";i:1;}}\"}}"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/DatabaseQueue.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php", "line": 188}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/DatabaseQueue.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php", "line": 99}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/Queue.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Queue.php", "line": 338}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/DatabaseQueue.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php", "line": 93}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Bus/Dispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\Dispatcher.php", "line": 244}], "start": **********.987796, "duration": 0.00424, "duration_str": "4.24ms", "memory": 0, "memory_str": null, "filename": "DatabaseQueue.php:188", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/DatabaseQueue.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php", "line": 188}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FQueue%2FDatabaseQueue.php&line=188", "ajax": false, "filename": "DatabaseQueue.php", "line": "188"}, "connection": "mlk", "explain": null, "start_percent": 41.216, "width_percent": 16.627}, {"sql": "select * from `theme_customizations` where `status` = 1 and `channel_id` = 1 and `theme_code` = 'default' order by `sort_order` asc", "type": "query", "params": [], "bindings": [1, 1, "default"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 315}, {"index": 17, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/HomeController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\HomeController.php", "line": 33}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.9938998, "duration": 0.0040999999999999995, "duration_str": "4.1ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "mlk", "explain": null, "start_percent": 57.843, "width_percent": 16.078}, {"sql": "select * from `theme_customization_translations` where `theme_customization_translations`.`theme_customization_id` in (14, 17, 18, 19, 20, 22, 23, 24, 28, 38, 39, 41, 44)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 21, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 315}, {"index": 22, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/HomeController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\HomeController.php", "line": 33}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.9999921, "duration": 0.00209, "duration_str": "2.09ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "mlk", "explain": null, "start_percent": 73.922, "width_percent": 8.196}, {"sql": "select * from `channel_translations` where `channel_translations`.`channel_id` = 1 and `channel_translations`.`channel_id` is not null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 445}, {"index": 22, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 200}, {"index": 23, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 148}, {"index": 25, "namespace": "view", "name": "shop::home.index", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/home/<USER>", "line": 9}], "start": **********.008405, "duration": 0.00219, "duration_str": "2.19ms", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 82.118, "width_percent": 8.588}, {"sql": "select count(*) as aggregate from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": "view", "name": "shop::components.layouts.header.index", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/header/index.blade.php", "line": 4}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.061194, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "shop::components.layouts.header.index:4", "source": {"index": 19, "namespace": "view", "name": "shop::components.layouts.header.index", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/header/index.blade.php", "line": 4}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Flayouts%2Fheader%2Findex.blade.php&line=4", "ajax": false, "filename": "index.blade.php", "line": "4"}, "connection": "mlk", "explain": null, "start_percent": 90.706, "width_percent": 1.49}, {"sql": "select `name` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1 and `code` = 'it' order by `name` asc limit 1", "type": "query", "params": [], "bindings": [1, "it"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "shop::components.layouts.header.desktop.top", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/header/desktop/top.blade.php", "line": 162}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.0700982, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "shop::components.layouts.header.desktop.top:162", "source": {"index": 20, "namespace": "view", "name": "shop::components.layouts.header.desktop.top", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/header/desktop/top.blade.php", "line": 162}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Flayouts%2Fheader%2Fdesktop%2Ftop.blade.php&line=162", "ajax": false, "filename": "top.blade.php", "line": "162"}, "connection": "mlk", "explain": null, "start_percent": 92.196, "width_percent": 1.059}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1 order by `name` asc", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": "view", "name": "shop::components.layouts.header.desktop.top", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/header/desktop/top.blade.php", "line": 270}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.071749, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "shop::components.layouts.header.desktop.top:270", "source": {"index": 15, "namespace": "view", "name": "shop::components.layouts.header.desktop.top", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/header/desktop/top.blade.php", "line": 270}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Flayouts%2Fheader%2Fdesktop%2Ftop.blade.php&line=270", "ajax": false, "filename": "top.blade.php", "line": "270"}, "connection": "mlk", "explain": null, "start_percent": 93.255, "width_percent": 1.137}, {"sql": "select count(*) as aggregate from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": "view", "name": "shop::components.layouts.header.mobile.index", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/header/mobile/index.blade.php", "line": 359}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.0904858, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "shop::components.layouts.header.mobile.index:359", "source": {"index": 19, "namespace": "view", "name": "shop::components.layouts.header.mobile.index", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/header/mobile/index.blade.php", "line": 359}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Flayouts%2Fheader%2Fmobile%2Findex.blade.php&line=359", "ajax": false, "filename": "index.blade.php", "line": "359"}, "connection": "mlk", "explain": null, "start_percent": 94.392, "width_percent": 1.686}, {"sql": "select `name` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1 and `code` = 'it' order by `name` asc limit 1", "type": "query", "params": [], "bindings": [1, "it"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": "view", "name": "shop::components.layouts.header.mobile.index", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/header/mobile/index.blade.php", "line": 442}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.092093, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "shop::components.layouts.header.mobile.index:442", "source": {"index": 20, "namespace": "view", "name": "shop::components.layouts.header.mobile.index", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/header/mobile/index.blade.php", "line": 442}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Flayouts%2Fheader%2Fmobile%2Findex.blade.php&line=442", "ajax": false, "filename": "index.blade.php", "line": "442"}, "connection": "mlk", "explain": null, "start_percent": 96.078, "width_percent": 0.941}, {"sql": "select * from `theme_customizations` where `type` = 'services_content' and `status` = 1 and `theme_code` = 'default' and `channel_id` = 1", "type": "query", "params": [], "bindings": ["services_content", 1, "default", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 315}, {"index": 17, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 119}, {"index": 18, "namespace": "view", "name": "shop::components.layouts.services", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/services.blade.php", "line": 13}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.0949678, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "mlk", "explain": null, "start_percent": 97.02, "width_percent": 1.02}, {"sql": "select * from `theme_customizations` where `type` = 'footer_links' and `status` = 1 and `theme_code` = 'default' and `channel_id` = 1", "type": "query", "params": [], "bindings": ["footer_links", 1, "default", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 315}, {"index": 17, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 119}, {"index": 18, "namespace": "view", "name": "shop::components.layouts.footer.index", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/footer/index.blade.php", "line": 17}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.0969932, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "mlk", "explain": null, "start_percent": 98.039, "width_percent": 1.059}, {"sql": "select * from `theme_customization_translations` where `theme_customization_translations`.`theme_customization_id` in (24)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 21, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 315}, {"index": 22, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 119}, {"index": 23, "namespace": "view", "name": "shop::components.layouts.footer.index", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/footer/index.blade.php", "line": 17}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.098223, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "mlk", "explain": null, "start_percent": 99.098, "width_percent": 0.902}]}, "models": {"data": {"Webkul\\Theme\\Models\\ThemeCustomizationTranslation": {"value": 72, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FTheme%2Fsrc%2FModels%2FThemeCustomizationTranslation.php&line=1", "ajax": false, "filename": "ThemeCustomizationTranslation.php", "line": "?"}}, "Webkul\\Theme\\Models\\ThemeCustomization": {"value": 14, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FTheme%2Fsrc%2FModels%2FThemeCustomization.php&line=1", "ajax": false, "filename": "ThemeCustomization.php", "line": "?"}}, "Webkul\\Core\\Models\\Locale": {"value": 12, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FModels%2FLocale.php&line=1", "ajax": false, "filename": "Locale.php", "line": "?"}}, "Webkul\\Core\\Models\\ChannelTranslation": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FModels%2FChannelTranslation.php&line=1", "ajax": false, "filename": "ChannelTranslation.php", "line": "?"}}, "Webkul\\Core\\Models\\Channel": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FModels%2FChannel.php&line=1", "ajax": false, "filename": "Channel.php", "line": "?"}}, "Webkul\\Core\\Models\\Currency": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FModels%2FCurrency.php&line=1", "ajax": false, "filename": "Currency.php", "line": "?"}}}, "count": 105, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://mlk.test", "action_name": "shop.home.index", "controller_action": "Webkul\\Shop\\Http\\Controllers\\HomeController@index", "uri": "GET /", "controller": "Webkul\\Shop\\Http\\Controllers\\HomeController@index<a href=\"phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FHttp%2FControllers%2FHomeController.php&line=29\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FShop%2Fsrc%2FHttp%2FControllers%2FHomeController.php&line=29\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">packages/Webkul/Shop/src/Http/Controllers/HomeController.php:29-40</a>", "middleware": "web, shop, Webkul\\Core\\Http\\Middleware\\PreventRequestsDuringMaintenance, cache.response", "duration": "376ms", "peak_memory": "44MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1515372273 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1515372273\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-23996421 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-23996421\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1914391929 data-indent-pad=\"  \"><span class=sf-dump-note>array:10</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"743 characters\">sidebar_collapsed=0; dark_mode=0; XSRF-TOKEN=eyJpdiI6IkhGbE5MbVdRc3RFMDVOYVBVb3FZN0E9PSIsInZhbHVlIjoiK2Nad1Iza2pIWDUrWFBQdStUelNwaFkyeExTM01HUi8vWkxXV3VyMW9GVlBndHVnbUovYzdlL2xaUHE5TVJWRFZWV3NBMGN3bU13ZTVkRHV0QXBGMEtMUG45eGtmajI3dnBGVGM4cE5OY2VSaldyT1drYXVOcWZBWHZhK0g0Y3kiLCJtYWMiOiI2MjAzMGE5YjcwMTc3Y2Q3MDk3NDRjOTFiY2I0N2RjMDA0NTNjZDlkZjI1NDRhNzBlZjI0ZTAxOTZiOTYzODQxIiwidGFnIjoiIn0%3D; mlk_session=eyJpdiI6IkQvMGhuN3RFVGtQMVk2N0YraThFc0E9PSIsInZhbHVlIjoiZFNqdVhlbXVPWCtZVE9lTE5wL2wvS3hPajZlMlV2M1U2OThldkJBOFRYTFNVY25FL2Zoc08yZ1FQdlRWTHlJdExESGJseFF2SDJVSjdTcldULzhhR2Y0K1JxOXJrd3BIckV1eW15RDB3aTBBb0t1eTZtTzFXZUZTSlY3ZkNzZlQiLCJtYWMiOiJkMWE2ZWFjNTBmNjRjMDdkYjE0OWQ1MTk4MWM0NDFjY2RjYjMwMTIwYmJlMGMxNjVmZjdlZThhOTc2MzEzZjM3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">zh-CN,zh;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://mlk.test/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">mlk.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1914391929\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-797824266 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>sidebar_collapsed</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>dark_mode</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">RL8eYWcLOX1nKpLrVKRAtWfTglgDAXuESTadrPQu</span>\"\n  \"<span class=sf-dump-key>mlk_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Zao9ptAmXhjVm7WD2j4izotzmKLg3wYf4OgogoiC</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-797824266\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1898541658 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 07 Aug 2025 15:45:01 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1898541658\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1795057790 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">RL8eYWcLOX1nKpLrVKRAtWfTglgDAXuESTadrPQu</span>\"\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">it</span>\"\n  \"<span class=sf-dump-key>currency</span>\" => \"<span class=sf-dump-str title=\"3 characters\">EUR</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://mlk.test/audio</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>admin_locale</span>\" => \"<span class=sf-dump-str title=\"5 characters\">zh_CN</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1795057790\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://mlk.test", "action_name": "shop.home.index", "controller_action": "Webkul\\Shop\\Http\\Controllers\\HomeController@index"}, "badge": null}}