{"__meta": {"id": "01K22BKNN2K3HTBBJNP2S3JQA5", "datetime": "2025-08-07 14:30:28", "utime": **********.386908, "method": "GET", "uri": "/cache/medium/theme/14/e1wtKrzDUyubDp9uhjCBAqwKV0bYpxIHDSXUhvsT.webp", "ip": "127.0.0.1"}, "modules": {"count": 0, "modules": []}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.175434, "end": **********.39636, "duration": 0.22092580795288086, "duration_str": "221ms", "measures": [{"label": "Booting", "start": **********.175434, "relative_start": 0, "end": **********.365963, "relative_end": **********.365963, "duration": 0.*****************, "duration_str": "191ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.365974, "relative_start": 0.*****************, "end": **********.396362, "relative_end": 2.1457672119140625e-06, "duration": 0.*****************, "duration_str": "30.39ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.376417, "relative_start": 0.*****************, "end": **********.379912, "relative_end": **********.379912, "duration": 0.0034949779510498047, "duration_str": "3.49ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.385345, "relative_start": 0.*****************, "end": **********.385461, "relative_end": **********.385461, "duration": 0.00011610984802246094, "duration_str": "116μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.385477, "relative_start": 0.*****************, "end": **********.385492, "relative_end": **********.385492, "duration": 1.5020370483398438e-05, "duration_str": "15μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "32MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.2.0", "Environment": "local", "Debug Mode": "Enabled", "URL": "mlk.test", "Timezone": "Africa/Lagos", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 0, "nb_statements": 0, "nb_visible_statements": 0, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://mlk.test/cache/medium/theme/14/e1wtKrzDUyubDp9uhjCBAqwKV0bYpxIHDSXUhvsT.webp", "action_name": "imagecache", "controller_action": "Webkul\\Core\\ImageCache\\Controller@getResponse", "uri": "GET cache/{template}/{filename}", "controller": "Webkul\\Core\\ImageCache\\Controller@getResponse<a href=\"phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FImageCache%2FController.php&line=34\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FImageCache%2FController.php&line=34\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">packages/Webkul/Core/src/ImageCache/Controller.php:34-46</a>", "duration": "221ms", "peak_memory": "34MB", "response": "image/webp", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1094818611 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1094818611\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1785570509 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1785570509\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1654489168 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"743 characters\">sidebar_collapsed=0; dark_mode=0; XSRF-TOKEN=eyJpdiI6IlB3RGVueEFkWlU5R1FZQTJ3ZWJWRlE9PSIsInZhbHVlIjoiT0g4UStVKzdVNHJmc0hjeFVQc1V5bGlRc3pxSExjZ091V1hBWWszdWQrbU5LQUdjOHVPaXRnZWM3dGJVeXBKVU5sRVBETFZmNVEwYWEzZE96UG91N2JCQ1BoVXRtZDgwMjU4K25TbkJkbGpsWHVXZWh6eFZXVnplVXdoNW1QbDkiLCJtYWMiOiJlNDFkZDFmMTBmMjRkZjkwMmRiNTdlYWQ4Mjc2ZDg0YTllMjc3YmM1ZDgzMjE0MGUxNWYzZmY0YWVjNDY1NTE3IiwidGFnIjoiIn0%3D; mlk_session=eyJpdiI6Im1CeTJZdy9TeFQ1Tk1jdlprYWhZaXc9PSIsInZhbHVlIjoiby9mRzFqQ3lZaThOajF3TEhLei9IK01HclV1dWMzd0l5djFBdnprMm5OTU1kQTFzR09PNGxodmNmNU1oOTJ1azNpajFRNTNrZ3ZFZURScXRQbmRPRitWWmNadWdYRGNRT1lLQTRvKzNjSzFodFBzUVZjb1BhZldSNjk3elJjcUIiLCJtYWMiOiI1Y2YxZTcxMDQxMjgxMTJlNjlkNjE4OTU4ZGEwNzc5MTA3ODk1NTA2MWZiZmZlMmJkNWZlNTNmMjE5OTY4NWExIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">zh-CN,zh;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://mlk.test/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">mlk.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1654489168\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1332535065 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>sidebar_collapsed</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>dark_mode</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IlB3RGVueEFkWlU5R1FZQTJ3ZWJWRlE9PSIsInZhbHVlIjoiT0g4UStVKzdVNHJmc0hjeFVQc1V5bGlRc3pxSExjZ091V1hBWWszdWQrbU5LQUdjOHVPaXRnZWM3dGJVeXBKVU5sRVBETFZmNVEwYWEzZE96UG91N2JCQ1BoVXRtZDgwMjU4K25TbkJkbGpsWHVXZWh6eFZXVnplVXdoNW1QbDkiLCJtYWMiOiJlNDFkZDFmMTBmMjRkZjkwMmRiNTdlYWQ4Mjc2ZDg0YTllMjc3YmM1ZDgzMjE0MGUxNWYzZmY0YWVjNDY1NTE3IiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>mlk_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6Im1CeTJZdy9TeFQ1Tk1jdlprYWhZaXc9PSIsInZhbHVlIjoiby9mRzFqQ3lZaThOajF3TEhLei9IK01HclV1dWMzd0l5djFBdnprMm5OTU1kQTFzR09PNGxodmNmNU1oOTJ1azNpajFRNTNrZ3ZFZURScXRQbmRPRitWWmNadWdYRGNRT1lLQTRvKzNjSzFodFBzUVZjb1BhZldSNjk3elJjcUIiLCJtYWMiOiI1Y2YxZTcxMDQxMjgxMTJlNjlkNjE4OTU4ZGEwNzc5MTA3ODk1NTA2MWZiZmZlMmJkNWZlNTNmMjE5OTY4NWExIiwidGFnIjoiIn0=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1332535065\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-157919153 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">image/webp</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">max-age=31536000, public</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">38380</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>etag</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">7830b97cd20453e4d5dfdeda5d484a5f</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 07 Aug 2025 13:30:28 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-157919153\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1925094154 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1925094154\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://mlk.test/cache/medium/theme/14/e1wtKrzDUyubDp9uhjCBAqwKV0bYpxIHDSXUhvsT.webp", "action_name": "imagecache", "controller_action": "Webkul\\Core\\ImageCache\\Controller@getResponse"}, "badge": null}}