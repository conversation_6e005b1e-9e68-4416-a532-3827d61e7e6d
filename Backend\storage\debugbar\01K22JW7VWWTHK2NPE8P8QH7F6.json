{"__meta": {"id": "01K22JW7VWWTHK2NPE8P8QH7F6", "datetime": "2025-08-07 16:37:29", "utime": **********.213337, "method": "POST", "uri": "/admin/settings/themes/edit/43", "ip": "127.0.0.1"}, "modules": {"count": 3, "modules": [{"name": "Webkul\\Core", "models": ["Webkul\\Core\\Models\\Channel (1)", "Webkul\\Core\\Models\\Locale (5)", "Webkul\\Core\\Models\\Currency (1)"], "views": [], "queries": [{"sql": "select * from `channels` where `hostname` in ('mlk.test', 'http://mlk.test', 'https://mlk.test')", "duration": 0.36, "duration_str": "360ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.46, "duration_str": "460ms", "connection": "mlk"}, {"sql": "select * from `currencies` where `currencies`.`id` = 2 limit 1", "duration": 0.25, "duration_str": "250ms", "connection": "mlk"}]}, {"name": "Webkul\\Theme", "models": ["Webkul\\Theme\\Models\\ThemeCustomization (1)"], "views": [], "queries": [{"sql": "select * from `theme_customizations` where `theme_customizations`.`id` = 43 limit 1", "duration": 0.29, "duration_str": "290ms", "connection": "mlk"}, {"sql": "select * from `theme_customization_translations` where `theme_customization_translations`.`theme_customization_id` in (43)", "duration": 0.25, "duration_str": "250ms", "connection": "mlk"}, {"sql": "update `theme_customizations` set `status` = 1, `theme_customizations`.`updated_at` = '2025-08-07 16:37:29' where `id` = 43", "duration": 3.49, "duration_str": "3.49s", "connection": "mlk"}]}, {"name": "Webkul\\User", "models": ["Webkul\\User\\Models\\Admin (1)", "Webkul\\User\\Models\\Role (1)"], "views": [], "queries": [{"sql": "select count(*) as aggregate from `admins`", "duration": 0.28, "duration_str": "280ms", "connection": "mlk"}, {"sql": "select count(*) as aggregate from `admins`", "duration": 0.17, "duration_str": "170ms", "connection": "mlk"}, {"sql": "select * from `admins` where `id` = 1 limit 1", "duration": 0.23, "duration_str": "230ms", "connection": "mlk"}, {"sql": "select * from `roles` where `roles`.`id` = 1 limit 1", "duration": 0.22, "duration_str": "220ms", "connection": "mlk"}]}]}, "messages": {"count": 0, "messages": []}, "time": {"start": 1754581048.858372, "end": **********.221892, "duration": 0.36352014541625977, "duration_str": "364ms", "measures": [{"label": "Booting", "start": 1754581048.858372, "relative_start": 0, "end": **********.100806, "relative_end": **********.100806, "duration": 0.*****************, "duration_str": "242ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.100817, "relative_start": 0.****************, "end": **********.221894, "relative_end": 1.9073486328125e-06, "duration": 0.*****************, "duration_str": "121ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.114399, "relative_start": 0.****************, "end": **********.116913, "relative_end": **********.116913, "duration": 0.0025141239166259766, "duration_str": "2.51ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.21133, "relative_start": 0.*****************, "end": **********.211679, "relative_end": **********.211679, "duration": 0.0003490447998046875, "duration_str": "349μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "39MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.2.0", "Environment": "local", "Debug Mode": "Enabled", "URL": "mlk.test", "Timezone": "Africa/Lagos", "Locale": "zh_CN"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 17, "nb_statements": 17, "nb_visible_statements": 17, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.01752, "accumulated_duration_str": "17.52ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select exists (select 1 from information_schema.tables where table_schema = 'mlk' and table_name = 'admins' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 32}, {"index": 14, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 44}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 1004}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 832}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 1078}], "start": **********.13139, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "DatabaseManager.php:32", "source": {"index": 13, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 32}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FInstaller%2Fsrc%2FHelpers%2FDatabaseManager.php&line=32", "ajax": false, "filename": "DatabaseManager.php", "line": "32"}, "connection": "mlk", "explain": null, "start_percent": 0, "width_percent": 2.626}, {"sql": "select count(*) as aggregate from `admins`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 38}, {"index": 16, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 44}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 1004}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 832}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 1078}], "start": **********.1359382, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "DatabaseManager.php:38", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 38}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FInstaller%2Fsrc%2FHelpers%2FDatabaseManager.php&line=38", "ajax": false, "filename": "DatabaseManager.php", "line": "38"}, "connection": "mlk", "explain": null, "start_percent": 2.626, "width_percent": 1.598}, {"sql": "select * from `channels` where `hostname` in ('mlk.test', 'http://mlk.test', 'https://mlk.test')", "type": "query", "params": [], "bindings": ["mlk.test", "http://mlk.test", "https://mlk.test"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 578}, {"index": 16, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Core.php", "line": 143}, {"index": 17, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 127}, {"index": 18, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 45}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 1004}], "start": **********.143652, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:578", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 578}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=578", "ajax": false, "filename": "BaseRepository.php", "line": "578"}, "connection": "mlk", "explain": null, "start_percent": 4.224, "width_percent": 2.055}, {"sql": "select exists (select 1 from information_schema.tables where table_schema = 'mlk' and table_name = 'admins' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 32}, {"index": 14, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 59}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 16, "namespace": "middleware", "name": "admin_locale", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Middleware\\AdminLocale.php", "line": 46}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.14611, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "DatabaseManager.php:32", "source": {"index": 13, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 32}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FInstaller%2Fsrc%2FHelpers%2FDatabaseManager.php&line=32", "ajax": false, "filename": "DatabaseManager.php", "line": "32"}, "connection": "mlk", "explain": null, "start_percent": 6.279, "width_percent": 1.998}, {"sql": "select count(*) as aggregate from `admins`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 38}, {"index": 16, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 18, "namespace": "middleware", "name": "admin_locale", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Middleware\\AdminLocale.php", "line": 46}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.147607, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "DatabaseManager.php:38", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 38}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FInstaller%2Fsrc%2FHelpers%2FDatabaseManager.php&line=38", "ajax": false, "filename": "DatabaseManager.php", "line": "38"}, "connection": "mlk", "explain": null, "start_percent": 8.276, "width_percent": 0.97}, {"sql": "select * from `admins` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "admin", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\User\\src\\Http\\Middleware\\Bouncer.php", "line": 18}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.1518679, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "mlk", "explain": null, "start_percent": 9.247, "width_percent": 1.313}, {"sql": "select * from `roles` where `roles`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": "middleware", "name": "admin", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\User\\src\\Http\\Middleware\\Bouncer.php", "line": 54}, {"index": 22, "namespace": "middleware", "name": "admin", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\User\\src\\Http\\Middleware\\Bouncer.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 24, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 119}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.154655, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "admin:54", "source": {"index": 21, "namespace": "middleware", "name": "admin", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\User\\src\\Http\\Middleware\\Bouncer.php", "line": 54}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FUser%2Fsrc%2FHttp%2FMiddleware%2FBouncer.php&line=54", "ajax": false, "filename": "Bouncer.php", "line": "54"}, "connection": "mlk", "explain": null, "start_percent": 10.559, "width_percent": 1.256}, {"sql": "select * from `theme_customizations` where `theme_customizations`.`id` = 43 limit 1", "type": "query", "params": [], "bindings": [43], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, {"index": 21, "namespace": null, "name": "packages/Webkul/Theme/src/Repositories/ThemeCustomizationRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Theme\\src\\Repositories\\ThemeCustomizationRepository.php", "line": 42}, {"index": 22, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Settings/ThemeController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Settings\\ThemeController.php", "line": 117}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.159287, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:695", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=695", "ajax": false, "filename": "BaseRepository.php", "line": "695"}, "connection": "mlk", "explain": null, "start_percent": 11.815, "width_percent": 1.655}, {"sql": "select * from `theme_customization_translations` where `theme_customization_translations`.`theme_customization_id` in (43)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 25, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, {"index": 26, "namespace": null, "name": "packages/Webkul/Theme/src/Repositories/ThemeCustomizationRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Theme\\src\\Repositories\\ThemeCustomizationRepository.php", "line": 42}, {"index": 27, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Settings/ThemeController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Settings\\ThemeController.php", "line": 117}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.1617758, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:695", "source": {"index": 25, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=695", "ajax": false, "filename": "BaseRepository.php", "line": "695"}, "connection": "mlk", "explain": null, "start_percent": 13.47, "width_percent": 1.427}, {"sql": "update `theme_customizations` set `status` = 1, `theme_customizations`.`updated_at` = '2025-08-07 16:37:29' where `id` = 43", "type": "query", "params": [], "bindings": [1, "2025-08-07 16:37:29", 43], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 700}, {"index": 15, "namespace": null, "name": "packages/Webkul/Theme/src/Repositories/ThemeCustomizationRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Theme\\src\\Repositories\\ThemeCustomizationRepository.php", "line": 42}, {"index": 16, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Settings/ThemeController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Settings\\ThemeController.php", "line": 117}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.174058, "duration": 0.00349, "duration_str": "3.49ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:700", "source": {"index": 14, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 700}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=700", "ajax": false, "filename": "BaseRepository.php", "line": "700"}, "connection": "mlk", "explain": null, "start_percent": 14.897, "width_percent": 19.92}, {"sql": "insert into `theme_customization_translations` (`locale`, `theme_customization_id`, `options`) values ('en', 43, '{\\\"html\\\":\\\"<section class=\\\\\\\"shop-by-brand-section\\\\\\\">\\\\r\\\\n        <div class=\\\\\\\"container\\\\\\\">\\\\r\\\\n            <h2 class=\\\\\\\"section-title\\\\\\\">\\\\r\\\\n                SHOP BY BRAND\\\\r\\\\n            <\\\\/h2>\\\\r\\\\n\\\\r\\\\n            <div class=\\\\\\\"brand-grid\\\\\\\">\\\\r\\\\n                <a href=\\\\\\\"#\\\\\\\" class=\\\\\\\"brand-logo-link\\\\\\\">\\\\r\\\\n                    <img src=\\\\\\\"\\\\/storage\\\\/theme\\\\/42\\\\/wG6bHeSQVQVxYO67SU6nS1K3eQXLaRVdHMLpreCC.webp\\\\\\\" alt=\\\\\\\"Apple\\\\\\\" class=\\\\\\\"brand-logo logo-apple\\\\\\\">\\\\r\\\\n                <\\\\/a>\\\\r\\\\n                <a href=\\\\\\\"#\\\\\\\" class=\\\\\\\"brand-logo-link\\\\\\\">\\\\r\\\\n                    <img src=\\\\\\\"\\\\/storage\\\\/theme\\\\/42\\\\/lEBIAJiOip7tcMCDZn6dXngX4ieMbpqct900OgYp.webp\\\\\\\" alt=\\\\\\\"Samsung\\\\\\\" class=\\\\\\\"brand-logo logo-samsung\\\\\\\">\\\\r\\\\n                <\\\\/a>\\\\r\\\\n                <a href=\\\\\\\"#\\\\\\\" class=\\\\\\\"brand-logo-link\\\\\\\">\\\\r\\\\n                    <img src=\\\\\\\"\\\\/storage\\\\/theme\\\\/42\\\\/ZlhWdHlUNqBbsr9ap91NemQT8Vaas46AiwVEbOYF.webp\\\\\\\" alt=\\\\\\\"Motorola\\\\\\\" class=\\\\\\\"brand-logo logo-motorola\\\\\\\">\\\\r\\\\n                <\\\\/a>\\\\r\\\\n                <a href=\\\\\\\"#\\\\\\\" class=\\\\\\\"brand-logo-link\\\\\\\">\\\\r\\\\n                    <img src=\\\\\\\"\\\\/storage\\\\/theme\\\\/42\\\\/mKjaC7o96snLqlvrNiWUhre6ToYNnCrRQ5iJAAkn.webp\\\\\\\" alt=\\\\\\\"Huawei\\\\\\\" class=\\\\\\\"brand-logo logo-huawei\\\\\\\">\\\\r\\\\n                <\\\\/a>\\\\r\\\\n                <a href=\\\\\\\"#\\\\\\\" class=\\\\\\\"brand-logo-link\\\\\\\">\\\\r\\\\n                    <img src=\\\\\\\"\\\\/storage\\\\/theme\\\\/42\\\\/aouSOFFs541Cv4Bu7YKER4I418y8GkVpA7jr9owz.webp\\\\\\\" alt=\\\\\\\"Xiaomi\\\\\\\" class=\\\\\\\"brand-logo logo-xiaomi\\\\\\\">\\\\r\\\\n                <\\\\/a>\\\\r\\\\n                <a href=\\\\\\\"#\\\\\\\" class=\\\\\\\"brand-logo-link\\\\\\\">\\\\r\\\\n                    <img src=\\\\\\\"\\\\/storage\\\\/theme\\\\/42\\\\/8EKdYRB1UZgov1PO1rbI9Jrj2hZR5xEn8XkShRCo.webp\\\\\\\" alt=\\\\\\\"Oppo\\\\\\\" class=\\\\\\\"brand-logo logo-oppo\\\\\\\">\\\\r\\\\n                <\\\\/a>\\\\r\\\\n            <\\\\/div>\\\\r\\\\n        <\\\\/div>\\\\r\\\\n    <\\\\/section>\\\",\\\"css\\\":\\\".shop-by-brand-section {\\\\r\\\\n            background-color: #ffffff;\\\\r\\\\n            padding-top: 3rem;\\\\r\\\\n            padding-bottom: 3rem;\\\\r\\\\n        }\\\\r\\\\n\\\\r\\\\n        .container {\\\\r\\\\n            max-width: 80rem;\\\\r\\\\n            margin-left: auto;\\\\r\\\\n            margin-right: auto;\\\\r\\\\n            padding-left: 1.5rem;\\\\r\\\\n            padding-right: 1.5rem;\\\\r\\\\n        }\\\\r\\\\n\\\\r\\\\n        .section-title {\\\\r\\\\n            text-align: center;\\\\r\\\\n            font-size: 1.5rem;\\\\r\\\\n            font-weight: 700;\\\\r\\\\n            letter-spacing: 0.1em;\\\\r\\\\n            color: rgb(31 41 55);\\\\r\\\\n        }\\\\r\\\\n\\\\r\\\\n        .brand-grid {\\\\r\\\\n            margin-top: 2.5rem;\\\\r\\\\n            display: grid;\\\\r\\\\n            grid-template-columns: repeat(2, 1fr);\\\\r\\\\n            align-items: center;\\\\r\\\\n            column-gap: 1.5rem;\\\\r\\\\n            row-gap: 2rem;\\\\r\\\\n            max-width: 32rem;\\\\r\\\\n            margin-left: auto;\\\\r\\\\n            margin-right: auto;\\\\r\\\\n        }\\\\r\\\\n\\\\r\\\\n        .brand-logo-link {\\\\r\\\\n            display: flex;\\\\r\\\\n            justify-content: center;\\\\r\\\\n            padding: 0.5rem;\\\\r\\\\n            transition: opacity 0.2s ease-in-out;\\\\r\\\\n        }\\\\r\\\\n\\\\r\\\\n        .brand-logo-link:hover {\\\\r\\\\n            opacity: 0.7;\\\\r\\\\n        }\\\\r\\\\n\\\\r\\\\n        .brand-logo {\\\\r\\\\n            object-fit: contain;\\\\r\\\\n        }\\\\r\\\\n\\\\r\\\\n        .logo-apple, .logo-motorola, .logo-huawei {\\\\r\\\\n             height: 3.25rem;\\\\r\\\\n        }\\\\r\\\\n        .logo-samsung, .logo-oppo {\\\\r\\\\n             height: 2.5rem;\\\\r\\\\n        }\\\\r\\\\n        .logo-xiaomi {\\\\r\\\\n             height: 3rem;\\\\r\\\\n        }\\\\r\\\\n\\\\r\\\\n        @media (min-width: 640px) {\\\\r\\\\n            .shop-by-brand-section {\\\\r\\\\n                padding-top: 4rem;\\\\r\\\\n                padding-bottom: 4rem;\\\\r\\\\n            }\\\\r\\\\n            .brand-grid {\\\\r\\\\n                max-width: 36rem;\\\\r\\\\n                grid-template-columns: repeat(3, 1fr);\\\\r\\\\n                column-gap: 2rem;\\\\r\\\\n            }\\\\r\\\\n        }\\\\r\\\\n\\\\r\\\\n        @media (min-width: 1024px) {\\\\r\\\\n            .container {\\\\r\\\\n                padding-left: 2rem;\\\\r\\\\n                padding-right: 2rem;\\\\r\\\\n            }\\\\r\\\\n            .brand-grid {\\\\r\\\\n                max-width: none;\\\\r\\\\n                margin-left: 0;\\\\r\\\\n                margin-right: 0;\\\\r\\\\n                grid-template-columns: repeat(6, 1fr);\\\\r\\\\n            }\\\\r\\\\n        }\\\"}')", "type": "query", "params": [], "bindings": ["en", 43, "{\"html\":\"<section class=\\\"shop-by-brand-section\\\">\\r\\n        <div class=\\\"container\\\">\\r\\n            <h2 class=\\\"section-title\\\">\\r\\n                SHOP BY BRAND\\r\\n            <\\/h2>\\r\\n\\r\\n            <div class=\\\"brand-grid\\\">\\r\\n                <a href=\\\"#\\\" class=\\\"brand-logo-link\\\">\\r\\n                    <img src=\\\"\\/storage\\/theme\\/42\\/wG6bHeSQVQVxYO67SU6nS1K3eQXLaRVdHMLpreCC.webp\\\" alt=\\\"Apple\\\" class=\\\"brand-logo logo-apple\\\">\\r\\n                <\\/a>\\r\\n                <a href=\\\"#\\\" class=\\\"brand-logo-link\\\">\\r\\n                    <img src=\\\"\\/storage\\/theme\\/42\\/lEBIAJiOip7tcMCDZn6dXngX4ieMbpqct900OgYp.webp\\\" alt=\\\"Samsung\\\" class=\\\"brand-logo logo-samsung\\\">\\r\\n                <\\/a>\\r\\n                <a href=\\\"#\\\" class=\\\"brand-logo-link\\\">\\r\\n                    <img src=\\\"\\/storage\\/theme\\/42\\/ZlhWdHlUNqBbsr9ap91NemQT8Vaas46AiwVEbOYF.webp\\\" alt=\\\"Motorola\\\" class=\\\"brand-logo logo-motorola\\\">\\r\\n                <\\/a>\\r\\n                <a href=\\\"#\\\" class=\\\"brand-logo-link\\\">\\r\\n                    <img src=\\\"\\/storage\\/theme\\/42\\/mKjaC7o96snLqlvrNiWUhre6ToYNnCrRQ5iJAAkn.webp\\\" alt=\\\"Huawei\\\" class=\\\"brand-logo logo-huawei\\\">\\r\\n                <\\/a>\\r\\n                <a href=\\\"#\\\" class=\\\"brand-logo-link\\\">\\r\\n                    <img src=\\\"\\/storage\\/theme\\/42\\/aouSOFFs541Cv4Bu7YKER4I418y8GkVpA7jr9owz.webp\\\" alt=\\\"Xiaomi\\\" class=\\\"brand-logo logo-xiaomi\\\">\\r\\n                <\\/a>\\r\\n                <a href=\\\"#\\\" class=\\\"brand-logo-link\\\">\\r\\n                    <img src=\\\"\\/storage\\/theme\\/42\\/8EKdYRB1UZgov1PO1rbI9Jrj2hZR5xEn8XkShRCo.webp\\\" alt=\\\"Oppo\\\" class=\\\"brand-logo logo-oppo\\\">\\r\\n                <\\/a>\\r\\n            <\\/div>\\r\\n        <\\/div>\\r\\n    <\\/section>\",\"css\":\".shop-by-brand-section {\\r\\n            background-color: #ffffff;\\r\\n            padding-top: 3rem;\\r\\n            padding-bottom: 3rem;\\r\\n        }\\r\\n\\r\\n        .container {\\r\\n            max-width: 80rem;\\r\\n            margin-left: auto;\\r\\n            margin-right: auto;\\r\\n            padding-left: 1.5rem;\\r\\n            padding-right: 1.5rem;\\r\\n        }\\r\\n\\r\\n        .section-title {\\r\\n            text-align: center;\\r\\n            font-size: 1.5rem;\\r\\n            font-weight: 700;\\r\\n            letter-spacing: 0.1em;\\r\\n            color: rgb(31 41 55);\\r\\n        }\\r\\n\\r\\n        .brand-grid {\\r\\n            margin-top: 2.5rem;\\r\\n            display: grid;\\r\\n            grid-template-columns: repeat(2, 1fr);\\r\\n            align-items: center;\\r\\n            column-gap: 1.5rem;\\r\\n            row-gap: 2rem;\\r\\n            max-width: 32rem;\\r\\n            margin-left: auto;\\r\\n            margin-right: auto;\\r\\n        }\\r\\n\\r\\n        .brand-logo-link {\\r\\n            display: flex;\\r\\n            justify-content: center;\\r\\n            padding: 0.5rem;\\r\\n            transition: opacity 0.2s ease-in-out;\\r\\n        }\\r\\n\\r\\n        .brand-logo-link:hover {\\r\\n            opacity: 0.7;\\r\\n        }\\r\\n\\r\\n        .brand-logo {\\r\\n            object-fit: contain;\\r\\n        }\\r\\n\\r\\n        .logo-apple, .logo-motorola, .logo-huawei {\\r\\n             height: 3.25rem;\\r\\n        }\\r\\n        .logo-samsung, .logo-oppo {\\r\\n             height: 2.5rem;\\r\\n        }\\r\\n        .logo-xiaomi {\\r\\n             height: 3rem;\\r\\n        }\\r\\n\\r\\n        @media (min-width: 640px) {\\r\\n            .shop-by-brand-section {\\r\\n                padding-top: 4rem;\\r\\n                padding-bottom: 4rem;\\r\\n            }\\r\\n            .brand-grid {\\r\\n                max-width: 36rem;\\r\\n                grid-template-columns: repeat(3, 1fr);\\r\\n                column-gap: 2rem;\\r\\n            }\\r\\n        }\\r\\n\\r\\n        @media (min-width: 1024px) {\\r\\n            .container {\\r\\n                padding-left: 2rem;\\r\\n                padding-right: 2rem;\\r\\n            }\\r\\n            .brand-grid {\\r\\n                max-width: none;\\r\\n                margin-left: 0;\\r\\n                margin-right: 0;\\r\\n                grid-template-columns: repeat(6, 1fr);\\r\\n            }\\r\\n        }\"}"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 387}, {"index": 16, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 700}, {"index": 24, "namespace": null, "name": "packages/Webkul/Theme/src/Repositories/ThemeCustomizationRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Theme\\src\\Repositories\\ThemeCustomizationRepository.php", "line": 42}, {"index": 25, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Settings/ThemeController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Settings\\ThemeController.php", "line": 117}], "start": **********.178714, "duration": 0.00232, "duration_str": "2.32ms", "memory": 0, "memory_str": null, "filename": "Translatable.php:387", "source": {"index": 15, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 387}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=387", "ajax": false, "filename": "Translatable.php", "line": "387"}, "connection": "mlk", "explain": null, "start_percent": 34.817, "width_percent": 13.242}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "packages/Webkul/Theme/src/Repositories/ThemeCustomizationRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Theme\\src\\Repositories\\ThemeCustomizationRepository.php", "line": 149}, {"index": 22, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Settings/ThemeController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Settings\\ThemeController.php", "line": 120}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.186059, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 48.059, "width_percent": 2.626}, {"sql": "insert into `theme_customization_translations` (`theme_customization_id`, `locale`, `options`) values (43, 'it', '{\\\"html\\\":\\\"<section class=\\\\\\\"shop-by-brand-section\\\\\\\">\\\\r\\\\n        <div class=\\\\\\\"container\\\\\\\">\\\\r\\\\n            <h2 class=\\\\\\\"section-title\\\\\\\">\\\\r\\\\n                SHOP BY BRAND\\\\r\\\\n            <\\\\/h2>\\\\r\\\\n\\\\r\\\\n            <div class=\\\\\\\"brand-grid\\\\\\\">\\\\r\\\\n                <a href=\\\\\\\"#\\\\\\\" class=\\\\\\\"brand-logo-link\\\\\\\">\\\\r\\\\n                    <img src=\\\\\\\"\\\\/storage\\\\/theme\\\\/42\\\\/wG6bHeSQVQVxYO67SU6nS1K3eQXLaRVdHMLpreCC.webp\\\\\\\" alt=\\\\\\\"Apple\\\\\\\" class=\\\\\\\"brand-logo logo-apple\\\\\\\">\\\\r\\\\n                <\\\\/a>\\\\r\\\\n                <a href=\\\\\\\"#\\\\\\\" class=\\\\\\\"brand-logo-link\\\\\\\">\\\\r\\\\n                    <img src=\\\\\\\"\\\\/storage\\\\/theme\\\\/42\\\\/lEBIAJiOip7tcMCDZn6dXngX4ieMbpqct900OgYp.webp\\\\\\\" alt=\\\\\\\"Samsung\\\\\\\" class=\\\\\\\"brand-logo logo-samsung\\\\\\\">\\\\r\\\\n                <\\\\/a>\\\\r\\\\n                <a href=\\\\\\\"#\\\\\\\" class=\\\\\\\"brand-logo-link\\\\\\\">\\\\r\\\\n                    <img src=\\\\\\\"\\\\/storage\\\\/theme\\\\/42\\\\/ZlhWdHlUNqBbsr9ap91NemQT8Vaas46AiwVEbOYF.webp\\\\\\\" alt=\\\\\\\"Motorola\\\\\\\" class=\\\\\\\"brand-logo logo-motorola\\\\\\\">\\\\r\\\\n                <\\\\/a>\\\\r\\\\n                <a href=\\\\\\\"#\\\\\\\" class=\\\\\\\"brand-logo-link\\\\\\\">\\\\r\\\\n                    <img src=\\\\\\\"\\\\/storage\\\\/theme\\\\/42\\\\/mKjaC7o96snLqlvrNiWUhre6ToYNnCrRQ5iJAAkn.webp\\\\\\\" alt=\\\\\\\"Huawei\\\\\\\" class=\\\\\\\"brand-logo logo-huawei\\\\\\\">\\\\r\\\\n                <\\\\/a>\\\\r\\\\n                <a href=\\\\\\\"#\\\\\\\" class=\\\\\\\"brand-logo-link\\\\\\\">\\\\r\\\\n                    <img src=\\\\\\\"\\\\/storage\\\\/theme\\\\/42\\\\/aouSOFFs541Cv4Bu7YKER4I418y8GkVpA7jr9owz.webp\\\\\\\" alt=\\\\\\\"Xiaomi\\\\\\\" class=\\\\\\\"brand-logo logo-xiaomi\\\\\\\">\\\\r\\\\n                <\\\\/a>\\\\r\\\\n                <a href=\\\\\\\"#\\\\\\\" class=\\\\\\\"brand-logo-link\\\\\\\">\\\\r\\\\n                    <img src=\\\\\\\"\\\\/storage\\\\/theme\\\\/42\\\\/8EKdYRB1UZgov1PO1rbI9Jrj2hZR5xEn8XkShRCo.webp\\\\\\\" alt=\\\\\\\"Oppo\\\\\\\" class=\\\\\\\"brand-logo logo-oppo\\\\\\\">\\\\r\\\\n                <\\\\/a>\\\\r\\\\n            <\\\\/div>\\\\r\\\\n        <\\\\/div>\\\\r\\\\n    <\\\\/section>\\\",\\\"css\\\":\\\".shop-by-brand-section {\\\\r\\\\n            background-color: #ffffff;\\\\r\\\\n            padding-top: 3rem;\\\\r\\\\n            padding-bottom: 3rem;\\\\r\\\\n        }\\\\r\\\\n\\\\r\\\\n        .container {\\\\r\\\\n            max-width: 80rem;\\\\r\\\\n            margin-left: auto;\\\\r\\\\n            margin-right: auto;\\\\r\\\\n            padding-left: 1.5rem;\\\\r\\\\n            padding-right: 1.5rem;\\\\r\\\\n        }\\\\r\\\\n\\\\r\\\\n        .section-title {\\\\r\\\\n            text-align: center;\\\\r\\\\n            font-size: 1.5rem;\\\\r\\\\n            font-weight: 700;\\\\r\\\\n            letter-spacing: 0.1em;\\\\r\\\\n            color: rgb(31 41 55);\\\\r\\\\n        }\\\\r\\\\n\\\\r\\\\n        .brand-grid {\\\\r\\\\n            margin-top: 2.5rem;\\\\r\\\\n            display: grid;\\\\r\\\\n            grid-template-columns: repeat(2, 1fr);\\\\r\\\\n            align-items: center;\\\\r\\\\n            column-gap: 1.5rem;\\\\r\\\\n            row-gap: 2rem;\\\\r\\\\n            max-width: 32rem;\\\\r\\\\n            margin-left: auto;\\\\r\\\\n            margin-right: auto;\\\\r\\\\n        }\\\\r\\\\n\\\\r\\\\n        .brand-logo-link {\\\\r\\\\n            display: flex;\\\\r\\\\n            justify-content: center;\\\\r\\\\n            padding: 0.5rem;\\\\r\\\\n            transition: opacity 0.2s ease-in-out;\\\\r\\\\n        }\\\\r\\\\n\\\\r\\\\n        .brand-logo-link:hover {\\\\r\\\\n            opacity: 0.7;\\\\r\\\\n        }\\\\r\\\\n\\\\r\\\\n        .brand-logo {\\\\r\\\\n            object-fit: contain;\\\\r\\\\n        }\\\\r\\\\n\\\\r\\\\n        .logo-apple, .logo-motorola, .logo-huawei {\\\\r\\\\n             height: 3.25rem;\\\\r\\\\n        }\\\\r\\\\n        .logo-samsung, .logo-oppo {\\\\r\\\\n             height: 2.5rem;\\\\r\\\\n        }\\\\r\\\\n        .logo-xiaomi {\\\\r\\\\n             height: 3rem;\\\\r\\\\n        }\\\\r\\\\n\\\\r\\\\n        @media (min-width: 640px) {\\\\r\\\\n            .shop-by-brand-section {\\\\r\\\\n                padding-top: 4rem;\\\\r\\\\n                padding-bottom: 4rem;\\\\r\\\\n            }\\\\r\\\\n            .brand-grid {\\\\r\\\\n                max-width: 36rem;\\\\r\\\\n                grid-template-columns: repeat(3, 1fr);\\\\r\\\\n                column-gap: 2rem;\\\\r\\\\n            }\\\\r\\\\n        }\\\\r\\\\n\\\\r\\\\n        @media (min-width: 1024px) {\\\\r\\\\n            .container {\\\\r\\\\n                padding-left: 2rem;\\\\r\\\\n                padding-right: 2rem;\\\\r\\\\n            }\\\\r\\\\n            .brand-grid {\\\\r\\\\n                max-width: none;\\\\r\\\\n                margin-left: 0;\\\\r\\\\n                margin-right: 0;\\\\r\\\\n                grid-template-columns: repeat(6, 1fr);\\\\r\\\\n            }\\\\r\\\\n        }\\\"}')", "type": "query", "params": [], "bindings": [43, "it", "{\"html\":\"<section class=\\\"shop-by-brand-section\\\">\\r\\n        <div class=\\\"container\\\">\\r\\n            <h2 class=\\\"section-title\\\">\\r\\n                SHOP BY BRAND\\r\\n            <\\/h2>\\r\\n\\r\\n            <div class=\\\"brand-grid\\\">\\r\\n                <a href=\\\"#\\\" class=\\\"brand-logo-link\\\">\\r\\n                    <img src=\\\"\\/storage\\/theme\\/42\\/wG6bHeSQVQVxYO67SU6nS1K3eQXLaRVdHMLpreCC.webp\\\" alt=\\\"Apple\\\" class=\\\"brand-logo logo-apple\\\">\\r\\n                <\\/a>\\r\\n                <a href=\\\"#\\\" class=\\\"brand-logo-link\\\">\\r\\n                    <img src=\\\"\\/storage\\/theme\\/42\\/lEBIAJiOip7tcMCDZn6dXngX4ieMbpqct900OgYp.webp\\\" alt=\\\"Samsung\\\" class=\\\"brand-logo logo-samsung\\\">\\r\\n                <\\/a>\\r\\n                <a href=\\\"#\\\" class=\\\"brand-logo-link\\\">\\r\\n                    <img src=\\\"\\/storage\\/theme\\/42\\/ZlhWdHlUNqBbsr9ap91NemQT8Vaas46AiwVEbOYF.webp\\\" alt=\\\"Motorola\\\" class=\\\"brand-logo logo-motorola\\\">\\r\\n                <\\/a>\\r\\n                <a href=\\\"#\\\" class=\\\"brand-logo-link\\\">\\r\\n                    <img src=\\\"\\/storage\\/theme\\/42\\/mKjaC7o96snLqlvrNiWUhre6ToYNnCrRQ5iJAAkn.webp\\\" alt=\\\"Huawei\\\" class=\\\"brand-logo logo-huawei\\\">\\r\\n                <\\/a>\\r\\n                <a href=\\\"#\\\" class=\\\"brand-logo-link\\\">\\r\\n                    <img src=\\\"\\/storage\\/theme\\/42\\/aouSOFFs541Cv4Bu7YKER4I418y8GkVpA7jr9owz.webp\\\" alt=\\\"Xiaomi\\\" class=\\\"brand-logo logo-xiaomi\\\">\\r\\n                <\\/a>\\r\\n                <a href=\\\"#\\\" class=\\\"brand-logo-link\\\">\\r\\n                    <img src=\\\"\\/storage\\/theme\\/42\\/8EKdYRB1UZgov1PO1rbI9Jrj2hZR5xEn8XkShRCo.webp\\\" alt=\\\"Oppo\\\" class=\\\"brand-logo logo-oppo\\\">\\r\\n                <\\/a>\\r\\n            <\\/div>\\r\\n        <\\/div>\\r\\n    <\\/section>\",\"css\":\".shop-by-brand-section {\\r\\n            background-color: #ffffff;\\r\\n            padding-top: 3rem;\\r\\n            padding-bottom: 3rem;\\r\\n        }\\r\\n\\r\\n        .container {\\r\\n            max-width: 80rem;\\r\\n            margin-left: auto;\\r\\n            margin-right: auto;\\r\\n            padding-left: 1.5rem;\\r\\n            padding-right: 1.5rem;\\r\\n        }\\r\\n\\r\\n        .section-title {\\r\\n            text-align: center;\\r\\n            font-size: 1.5rem;\\r\\n            font-weight: 700;\\r\\n            letter-spacing: 0.1em;\\r\\n            color: rgb(31 41 55);\\r\\n        }\\r\\n\\r\\n        .brand-grid {\\r\\n            margin-top: 2.5rem;\\r\\n            display: grid;\\r\\n            grid-template-columns: repeat(2, 1fr);\\r\\n            align-items: center;\\r\\n            column-gap: 1.5rem;\\r\\n            row-gap: 2rem;\\r\\n            max-width: 32rem;\\r\\n            margin-left: auto;\\r\\n            margin-right: auto;\\r\\n        }\\r\\n\\r\\n        .brand-logo-link {\\r\\n            display: flex;\\r\\n            justify-content: center;\\r\\n            padding: 0.5rem;\\r\\n            transition: opacity 0.2s ease-in-out;\\r\\n        }\\r\\n\\r\\n        .brand-logo-link:hover {\\r\\n            opacity: 0.7;\\r\\n        }\\r\\n\\r\\n        .brand-logo {\\r\\n            object-fit: contain;\\r\\n        }\\r\\n\\r\\n        .logo-apple, .logo-motorola, .logo-huawei {\\r\\n             height: 3.25rem;\\r\\n        }\\r\\n        .logo-samsung, .logo-oppo {\\r\\n             height: 2.5rem;\\r\\n        }\\r\\n        .logo-xiaomi {\\r\\n             height: 3rem;\\r\\n        }\\r\\n\\r\\n        @media (min-width: 640px) {\\r\\n            .shop-by-brand-section {\\r\\n                padding-top: 4rem;\\r\\n                padding-bottom: 4rem;\\r\\n            }\\r\\n            .brand-grid {\\r\\n                max-width: 36rem;\\r\\n                grid-template-columns: repeat(3, 1fr);\\r\\n                column-gap: 2rem;\\r\\n            }\\r\\n        }\\r\\n\\r\\n        @media (min-width: 1024px) {\\r\\n            .container {\\r\\n                padding-left: 2rem;\\r\\n                padding-right: 2rem;\\r\\n            }\\r\\n            .brand-grid {\\r\\n                max-width: none;\\r\\n                margin-left: 0;\\r\\n                margin-right: 0;\\r\\n                grid-template-columns: repeat(6, 1fr);\\r\\n            }\\r\\n        }\"}"], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "packages/Webkul/Theme/src/Repositories/ThemeCustomizationRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Theme\\src\\Repositories\\ThemeCustomizationRepository.php", "line": 172}, {"index": 19, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Settings/ThemeController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Settings\\ThemeController.php", "line": 120}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.188097, "duration": 0.00215, "duration_str": "2.15ms", "memory": 0, "memory_str": null, "filename": "ThemeCustomizationRepository.php:172", "source": {"index": 18, "namespace": null, "name": "packages/Webkul/Theme/src/Repositories/ThemeCustomizationRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Theme\\src\\Repositories\\ThemeCustomizationRepository.php", "line": 172}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FTheme%2Fsrc%2FRepositories%2FThemeCustomizationRepository.php&line=172", "ajax": false, "filename": "ThemeCustomizationRepository.php", "line": "172"}, "connection": "mlk", "explain": null, "start_percent": 50.685, "width_percent": 12.272}, {"sql": "insert into `theme_customization_translations` (`theme_customization_id`, `locale`, `options`) values (43, 'de', '{\\\"html\\\":\\\"<section class=\\\\\\\"shop-by-brand-section\\\\\\\">\\\\r\\\\n        <div class=\\\\\\\"container\\\\\\\">\\\\r\\\\n            <h2 class=\\\\\\\"section-title\\\\\\\">\\\\r\\\\n                SHOP BY BRAND\\\\r\\\\n            <\\\\/h2>\\\\r\\\\n\\\\r\\\\n            <div class=\\\\\\\"brand-grid\\\\\\\">\\\\r\\\\n                <a href=\\\\\\\"#\\\\\\\" class=\\\\\\\"brand-logo-link\\\\\\\">\\\\r\\\\n                    <img src=\\\\\\\"\\\\/storage\\\\/theme\\\\/42\\\\/wG6bHeSQVQVxYO67SU6nS1K3eQXLaRVdHMLpreCC.webp\\\\\\\" alt=\\\\\\\"Apple\\\\\\\" class=\\\\\\\"brand-logo logo-apple\\\\\\\">\\\\r\\\\n                <\\\\/a>\\\\r\\\\n                <a href=\\\\\\\"#\\\\\\\" class=\\\\\\\"brand-logo-link\\\\\\\">\\\\r\\\\n                    <img src=\\\\\\\"\\\\/storage\\\\/theme\\\\/42\\\\/lEBIAJiOip7tcMCDZn6dXngX4ieMbpqct900OgYp.webp\\\\\\\" alt=\\\\\\\"Samsung\\\\\\\" class=\\\\\\\"brand-logo logo-samsung\\\\\\\">\\\\r\\\\n                <\\\\/a>\\\\r\\\\n                <a href=\\\\\\\"#\\\\\\\" class=\\\\\\\"brand-logo-link\\\\\\\">\\\\r\\\\n                    <img src=\\\\\\\"\\\\/storage\\\\/theme\\\\/42\\\\/ZlhWdHlUNqBbsr9ap91NemQT8Vaas46AiwVEbOYF.webp\\\\\\\" alt=\\\\\\\"Motorola\\\\\\\" class=\\\\\\\"brand-logo logo-motorola\\\\\\\">\\\\r\\\\n                <\\\\/a>\\\\r\\\\n                <a href=\\\\\\\"#\\\\\\\" class=\\\\\\\"brand-logo-link\\\\\\\">\\\\r\\\\n                    <img src=\\\\\\\"\\\\/storage\\\\/theme\\\\/42\\\\/mKjaC7o96snLqlvrNiWUhre6ToYNnCrRQ5iJAAkn.webp\\\\\\\" alt=\\\\\\\"Huawei\\\\\\\" class=\\\\\\\"brand-logo logo-huawei\\\\\\\">\\\\r\\\\n                <\\\\/a>\\\\r\\\\n                <a href=\\\\\\\"#\\\\\\\" class=\\\\\\\"brand-logo-link\\\\\\\">\\\\r\\\\n                    <img src=\\\\\\\"\\\\/storage\\\\/theme\\\\/42\\\\/aouSOFFs541Cv4Bu7YKER4I418y8GkVpA7jr9owz.webp\\\\\\\" alt=\\\\\\\"Xiaomi\\\\\\\" class=\\\\\\\"brand-logo logo-xiaomi\\\\\\\">\\\\r\\\\n                <\\\\/a>\\\\r\\\\n                <a href=\\\\\\\"#\\\\\\\" class=\\\\\\\"brand-logo-link\\\\\\\">\\\\r\\\\n                    <img src=\\\\\\\"\\\\/storage\\\\/theme\\\\/42\\\\/8EKdYRB1UZgov1PO1rbI9Jrj2hZR5xEn8XkShRCo.webp\\\\\\\" alt=\\\\\\\"Oppo\\\\\\\" class=\\\\\\\"brand-logo logo-oppo\\\\\\\">\\\\r\\\\n                <\\\\/a>\\\\r\\\\n            <\\\\/div>\\\\r\\\\n        <\\\\/div>\\\\r\\\\n    <\\\\/section>\\\",\\\"css\\\":\\\".shop-by-brand-section {\\\\r\\\\n            background-color: #ffffff;\\\\r\\\\n            padding-top: 3rem;\\\\r\\\\n            padding-bottom: 3rem;\\\\r\\\\n        }\\\\r\\\\n\\\\r\\\\n        .container {\\\\r\\\\n            max-width: 80rem;\\\\r\\\\n            margin-left: auto;\\\\r\\\\n            margin-right: auto;\\\\r\\\\n            padding-left: 1.5rem;\\\\r\\\\n            padding-right: 1.5rem;\\\\r\\\\n        }\\\\r\\\\n\\\\r\\\\n        .section-title {\\\\r\\\\n            text-align: center;\\\\r\\\\n            font-size: 1.5rem;\\\\r\\\\n            font-weight: 700;\\\\r\\\\n            letter-spacing: 0.1em;\\\\r\\\\n            color: rgb(31 41 55);\\\\r\\\\n        }\\\\r\\\\n\\\\r\\\\n        .brand-grid {\\\\r\\\\n            margin-top: 2.5rem;\\\\r\\\\n            display: grid;\\\\r\\\\n            grid-template-columns: repeat(2, 1fr);\\\\r\\\\n            align-items: center;\\\\r\\\\n            column-gap: 1.5rem;\\\\r\\\\n            row-gap: 2rem;\\\\r\\\\n            max-width: 32rem;\\\\r\\\\n            margin-left: auto;\\\\r\\\\n            margin-right: auto;\\\\r\\\\n        }\\\\r\\\\n\\\\r\\\\n        .brand-logo-link {\\\\r\\\\n            display: flex;\\\\r\\\\n            justify-content: center;\\\\r\\\\n            padding: 0.5rem;\\\\r\\\\n            transition: opacity 0.2s ease-in-out;\\\\r\\\\n        }\\\\r\\\\n\\\\r\\\\n        .brand-logo-link:hover {\\\\r\\\\n            opacity: 0.7;\\\\r\\\\n        }\\\\r\\\\n\\\\r\\\\n        .brand-logo {\\\\r\\\\n            object-fit: contain;\\\\r\\\\n        }\\\\r\\\\n\\\\r\\\\n        .logo-apple, .logo-motorola, .logo-huawei {\\\\r\\\\n             height: 3.25rem;\\\\r\\\\n        }\\\\r\\\\n        .logo-samsung, .logo-oppo {\\\\r\\\\n             height: 2.5rem;\\\\r\\\\n        }\\\\r\\\\n        .logo-xiaomi {\\\\r\\\\n             height: 3rem;\\\\r\\\\n        }\\\\r\\\\n\\\\r\\\\n        @media (min-width: 640px) {\\\\r\\\\n            .shop-by-brand-section {\\\\r\\\\n                padding-top: 4rem;\\\\r\\\\n                padding-bottom: 4rem;\\\\r\\\\n            }\\\\r\\\\n            .brand-grid {\\\\r\\\\n                max-width: 36rem;\\\\r\\\\n                grid-template-columns: repeat(3, 1fr);\\\\r\\\\n                column-gap: 2rem;\\\\r\\\\n            }\\\\r\\\\n        }\\\\r\\\\n\\\\r\\\\n        @media (min-width: 1024px) {\\\\r\\\\n            .container {\\\\r\\\\n                padding-left: 2rem;\\\\r\\\\n                padding-right: 2rem;\\\\r\\\\n            }\\\\r\\\\n            .brand-grid {\\\\r\\\\n                max-width: none;\\\\r\\\\n                margin-left: 0;\\\\r\\\\n                margin-right: 0;\\\\r\\\\n                grid-template-columns: repeat(6, 1fr);\\\\r\\\\n            }\\\\r\\\\n        }\\\"}')", "type": "query", "params": [], "bindings": [43, "de", "{\"html\":\"<section class=\\\"shop-by-brand-section\\\">\\r\\n        <div class=\\\"container\\\">\\r\\n            <h2 class=\\\"section-title\\\">\\r\\n                SHOP BY BRAND\\r\\n            <\\/h2>\\r\\n\\r\\n            <div class=\\\"brand-grid\\\">\\r\\n                <a href=\\\"#\\\" class=\\\"brand-logo-link\\\">\\r\\n                    <img src=\\\"\\/storage\\/theme\\/42\\/wG6bHeSQVQVxYO67SU6nS1K3eQXLaRVdHMLpreCC.webp\\\" alt=\\\"Apple\\\" class=\\\"brand-logo logo-apple\\\">\\r\\n                <\\/a>\\r\\n                <a href=\\\"#\\\" class=\\\"brand-logo-link\\\">\\r\\n                    <img src=\\\"\\/storage\\/theme\\/42\\/lEBIAJiOip7tcMCDZn6dXngX4ieMbpqct900OgYp.webp\\\" alt=\\\"Samsung\\\" class=\\\"brand-logo logo-samsung\\\">\\r\\n                <\\/a>\\r\\n                <a href=\\\"#\\\" class=\\\"brand-logo-link\\\">\\r\\n                    <img src=\\\"\\/storage\\/theme\\/42\\/ZlhWdHlUNqBbsr9ap91NemQT8Vaas46AiwVEbOYF.webp\\\" alt=\\\"Motorola\\\" class=\\\"brand-logo logo-motorola\\\">\\r\\n                <\\/a>\\r\\n                <a href=\\\"#\\\" class=\\\"brand-logo-link\\\">\\r\\n                    <img src=\\\"\\/storage\\/theme\\/42\\/mKjaC7o96snLqlvrNiWUhre6ToYNnCrRQ5iJAAkn.webp\\\" alt=\\\"Huawei\\\" class=\\\"brand-logo logo-huawei\\\">\\r\\n                <\\/a>\\r\\n                <a href=\\\"#\\\" class=\\\"brand-logo-link\\\">\\r\\n                    <img src=\\\"\\/storage\\/theme\\/42\\/aouSOFFs541Cv4Bu7YKER4I418y8GkVpA7jr9owz.webp\\\" alt=\\\"Xiaomi\\\" class=\\\"brand-logo logo-xiaomi\\\">\\r\\n                <\\/a>\\r\\n                <a href=\\\"#\\\" class=\\\"brand-logo-link\\\">\\r\\n                    <img src=\\\"\\/storage\\/theme\\/42\\/8EKdYRB1UZgov1PO1rbI9Jrj2hZR5xEn8XkShRCo.webp\\\" alt=\\\"Oppo\\\" class=\\\"brand-logo logo-oppo\\\">\\r\\n                <\\/a>\\r\\n            <\\/div>\\r\\n        <\\/div>\\r\\n    <\\/section>\",\"css\":\".shop-by-brand-section {\\r\\n            background-color: #ffffff;\\r\\n            padding-top: 3rem;\\r\\n            padding-bottom: 3rem;\\r\\n        }\\r\\n\\r\\n        .container {\\r\\n            max-width: 80rem;\\r\\n            margin-left: auto;\\r\\n            margin-right: auto;\\r\\n            padding-left: 1.5rem;\\r\\n            padding-right: 1.5rem;\\r\\n        }\\r\\n\\r\\n        .section-title {\\r\\n            text-align: center;\\r\\n            font-size: 1.5rem;\\r\\n            font-weight: 700;\\r\\n            letter-spacing: 0.1em;\\r\\n            color: rgb(31 41 55);\\r\\n        }\\r\\n\\r\\n        .brand-grid {\\r\\n            margin-top: 2.5rem;\\r\\n            display: grid;\\r\\n            grid-template-columns: repeat(2, 1fr);\\r\\n            align-items: center;\\r\\n            column-gap: 1.5rem;\\r\\n            row-gap: 2rem;\\r\\n            max-width: 32rem;\\r\\n            margin-left: auto;\\r\\n            margin-right: auto;\\r\\n        }\\r\\n\\r\\n        .brand-logo-link {\\r\\n            display: flex;\\r\\n            justify-content: center;\\r\\n            padding: 0.5rem;\\r\\n            transition: opacity 0.2s ease-in-out;\\r\\n        }\\r\\n\\r\\n        .brand-logo-link:hover {\\r\\n            opacity: 0.7;\\r\\n        }\\r\\n\\r\\n        .brand-logo {\\r\\n            object-fit: contain;\\r\\n        }\\r\\n\\r\\n        .logo-apple, .logo-motorola, .logo-huawei {\\r\\n             height: 3.25rem;\\r\\n        }\\r\\n        .logo-samsung, .logo-oppo {\\r\\n             height: 2.5rem;\\r\\n        }\\r\\n        .logo-xiaomi {\\r\\n             height: 3rem;\\r\\n        }\\r\\n\\r\\n        @media (min-width: 640px) {\\r\\n            .shop-by-brand-section {\\r\\n                padding-top: 4rem;\\r\\n                padding-bottom: 4rem;\\r\\n            }\\r\\n            .brand-grid {\\r\\n                max-width: 36rem;\\r\\n                grid-template-columns: repeat(3, 1fr);\\r\\n                column-gap: 2rem;\\r\\n            }\\r\\n        }\\r\\n\\r\\n        @media (min-width: 1024px) {\\r\\n            .container {\\r\\n                padding-left: 2rem;\\r\\n                padding-right: 2rem;\\r\\n            }\\r\\n            .brand-grid {\\r\\n                max-width: none;\\r\\n                margin-left: 0;\\r\\n                margin-right: 0;\\r\\n                grid-template-columns: repeat(6, 1fr);\\r\\n            }\\r\\n        }\"}"], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "packages/Webkul/Theme/src/Repositories/ThemeCustomizationRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Theme\\src\\Repositories\\ThemeCustomizationRepository.php", "line": 172}, {"index": 19, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Settings/ThemeController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Settings\\ThemeController.php", "line": 120}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.1914198, "duration": 0.00214, "duration_str": "2.14ms", "memory": 0, "memory_str": null, "filename": "ThemeCustomizationRepository.php:172", "source": {"index": 18, "namespace": null, "name": "packages/Webkul/Theme/src/Repositories/ThemeCustomizationRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Theme\\src\\Repositories\\ThemeCustomizationRepository.php", "line": 172}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FTheme%2Fsrc%2FRepositories%2FThemeCustomizationRepository.php&line=172", "ajax": false, "filename": "ThemeCustomizationRepository.php", "line": "172"}, "connection": "mlk", "explain": null, "start_percent": 62.957, "width_percent": 12.215}, {"sql": "insert into `theme_customization_translations` (`theme_customization_id`, `locale`, `options`) values (43, 'fr', '{\\\"html\\\":\\\"<section class=\\\\\\\"shop-by-brand-section\\\\\\\">\\\\r\\\\n        <div class=\\\\\\\"container\\\\\\\">\\\\r\\\\n            <h2 class=\\\\\\\"section-title\\\\\\\">\\\\r\\\\n                SHOP BY BRAND\\\\r\\\\n            <\\\\/h2>\\\\r\\\\n\\\\r\\\\n            <div class=\\\\\\\"brand-grid\\\\\\\">\\\\r\\\\n                <a href=\\\\\\\"#\\\\\\\" class=\\\\\\\"brand-logo-link\\\\\\\">\\\\r\\\\n                    <img src=\\\\\\\"\\\\/storage\\\\/theme\\\\/42\\\\/wG6bHeSQVQVxYO67SU6nS1K3eQXLaRVdHMLpreCC.webp\\\\\\\" alt=\\\\\\\"Apple\\\\\\\" class=\\\\\\\"brand-logo logo-apple\\\\\\\">\\\\r\\\\n                <\\\\/a>\\\\r\\\\n                <a href=\\\\\\\"#\\\\\\\" class=\\\\\\\"brand-logo-link\\\\\\\">\\\\r\\\\n                    <img src=\\\\\\\"\\\\/storage\\\\/theme\\\\/42\\\\/lEBIAJiOip7tcMCDZn6dXngX4ieMbpqct900OgYp.webp\\\\\\\" alt=\\\\\\\"Samsung\\\\\\\" class=\\\\\\\"brand-logo logo-samsung\\\\\\\">\\\\r\\\\n                <\\\\/a>\\\\r\\\\n                <a href=\\\\\\\"#\\\\\\\" class=\\\\\\\"brand-logo-link\\\\\\\">\\\\r\\\\n                    <img src=\\\\\\\"\\\\/storage\\\\/theme\\\\/42\\\\/ZlhWdHlUNqBbsr9ap91NemQT8Vaas46AiwVEbOYF.webp\\\\\\\" alt=\\\\\\\"Motorola\\\\\\\" class=\\\\\\\"brand-logo logo-motorola\\\\\\\">\\\\r\\\\n                <\\\\/a>\\\\r\\\\n                <a href=\\\\\\\"#\\\\\\\" class=\\\\\\\"brand-logo-link\\\\\\\">\\\\r\\\\n                    <img src=\\\\\\\"\\\\/storage\\\\/theme\\\\/42\\\\/mKjaC7o96snLqlvrNiWUhre6ToYNnCrRQ5iJAAkn.webp\\\\\\\" alt=\\\\\\\"Huawei\\\\\\\" class=\\\\\\\"brand-logo logo-huawei\\\\\\\">\\\\r\\\\n                <\\\\/a>\\\\r\\\\n                <a href=\\\\\\\"#\\\\\\\" class=\\\\\\\"brand-logo-link\\\\\\\">\\\\r\\\\n                    <img src=\\\\\\\"\\\\/storage\\\\/theme\\\\/42\\\\/aouSOFFs541Cv4Bu7YKER4I418y8GkVpA7jr9owz.webp\\\\\\\" alt=\\\\\\\"Xiaomi\\\\\\\" class=\\\\\\\"brand-logo logo-xiaomi\\\\\\\">\\\\r\\\\n                <\\\\/a>\\\\r\\\\n                <a href=\\\\\\\"#\\\\\\\" class=\\\\\\\"brand-logo-link\\\\\\\">\\\\r\\\\n                    <img src=\\\\\\\"\\\\/storage\\\\/theme\\\\/42\\\\/8EKdYRB1UZgov1PO1rbI9Jrj2hZR5xEn8XkShRCo.webp\\\\\\\" alt=\\\\\\\"Oppo\\\\\\\" class=\\\\\\\"brand-logo logo-oppo\\\\\\\">\\\\r\\\\n                <\\\\/a>\\\\r\\\\n            <\\\\/div>\\\\r\\\\n        <\\\\/div>\\\\r\\\\n    <\\\\/section>\\\",\\\"css\\\":\\\".shop-by-brand-section {\\\\r\\\\n            background-color: #ffffff;\\\\r\\\\n            padding-top: 3rem;\\\\r\\\\n            padding-bottom: 3rem;\\\\r\\\\n        }\\\\r\\\\n\\\\r\\\\n        .container {\\\\r\\\\n            max-width: 80rem;\\\\r\\\\n            margin-left: auto;\\\\r\\\\n            margin-right: auto;\\\\r\\\\n            padding-left: 1.5rem;\\\\r\\\\n            padding-right: 1.5rem;\\\\r\\\\n        }\\\\r\\\\n\\\\r\\\\n        .section-title {\\\\r\\\\n            text-align: center;\\\\r\\\\n            font-size: 1.5rem;\\\\r\\\\n            font-weight: 700;\\\\r\\\\n            letter-spacing: 0.1em;\\\\r\\\\n            color: rgb(31 41 55);\\\\r\\\\n        }\\\\r\\\\n\\\\r\\\\n        .brand-grid {\\\\r\\\\n            margin-top: 2.5rem;\\\\r\\\\n            display: grid;\\\\r\\\\n            grid-template-columns: repeat(2, 1fr);\\\\r\\\\n            align-items: center;\\\\r\\\\n            column-gap: 1.5rem;\\\\r\\\\n            row-gap: 2rem;\\\\r\\\\n            max-width: 32rem;\\\\r\\\\n            margin-left: auto;\\\\r\\\\n            margin-right: auto;\\\\r\\\\n        }\\\\r\\\\n\\\\r\\\\n        .brand-logo-link {\\\\r\\\\n            display: flex;\\\\r\\\\n            justify-content: center;\\\\r\\\\n            padding: 0.5rem;\\\\r\\\\n            transition: opacity 0.2s ease-in-out;\\\\r\\\\n        }\\\\r\\\\n\\\\r\\\\n        .brand-logo-link:hover {\\\\r\\\\n            opacity: 0.7;\\\\r\\\\n        }\\\\r\\\\n\\\\r\\\\n        .brand-logo {\\\\r\\\\n            object-fit: contain;\\\\r\\\\n        }\\\\r\\\\n\\\\r\\\\n        .logo-apple, .logo-motorola, .logo-huawei {\\\\r\\\\n             height: 3.25rem;\\\\r\\\\n        }\\\\r\\\\n        .logo-samsung, .logo-oppo {\\\\r\\\\n             height: 2.5rem;\\\\r\\\\n        }\\\\r\\\\n        .logo-xiaomi {\\\\r\\\\n             height: 3rem;\\\\r\\\\n        }\\\\r\\\\n\\\\r\\\\n        @media (min-width: 640px) {\\\\r\\\\n            .shop-by-brand-section {\\\\r\\\\n                padding-top: 4rem;\\\\r\\\\n                padding-bottom: 4rem;\\\\r\\\\n            }\\\\r\\\\n            .brand-grid {\\\\r\\\\n                max-width: 36rem;\\\\r\\\\n                grid-template-columns: repeat(3, 1fr);\\\\r\\\\n                column-gap: 2rem;\\\\r\\\\n            }\\\\r\\\\n        }\\\\r\\\\n\\\\r\\\\n        @media (min-width: 1024px) {\\\\r\\\\n            .container {\\\\r\\\\n                padding-left: 2rem;\\\\r\\\\n                padding-right: 2rem;\\\\r\\\\n            }\\\\r\\\\n            .brand-grid {\\\\r\\\\n                max-width: none;\\\\r\\\\n                margin-left: 0;\\\\r\\\\n                margin-right: 0;\\\\r\\\\n                grid-template-columns: repeat(6, 1fr);\\\\r\\\\n            }\\\\r\\\\n        }\\\"}')", "type": "query", "params": [], "bindings": [43, "fr", "{\"html\":\"<section class=\\\"shop-by-brand-section\\\">\\r\\n        <div class=\\\"container\\\">\\r\\n            <h2 class=\\\"section-title\\\">\\r\\n                SHOP BY BRAND\\r\\n            <\\/h2>\\r\\n\\r\\n            <div class=\\\"brand-grid\\\">\\r\\n                <a href=\\\"#\\\" class=\\\"brand-logo-link\\\">\\r\\n                    <img src=\\\"\\/storage\\/theme\\/42\\/wG6bHeSQVQVxYO67SU6nS1K3eQXLaRVdHMLpreCC.webp\\\" alt=\\\"Apple\\\" class=\\\"brand-logo logo-apple\\\">\\r\\n                <\\/a>\\r\\n                <a href=\\\"#\\\" class=\\\"brand-logo-link\\\">\\r\\n                    <img src=\\\"\\/storage\\/theme\\/42\\/lEBIAJiOip7tcMCDZn6dXngX4ieMbpqct900OgYp.webp\\\" alt=\\\"Samsung\\\" class=\\\"brand-logo logo-samsung\\\">\\r\\n                <\\/a>\\r\\n                <a href=\\\"#\\\" class=\\\"brand-logo-link\\\">\\r\\n                    <img src=\\\"\\/storage\\/theme\\/42\\/ZlhWdHlUNqBbsr9ap91NemQT8Vaas46AiwVEbOYF.webp\\\" alt=\\\"Motorola\\\" class=\\\"brand-logo logo-motorola\\\">\\r\\n                <\\/a>\\r\\n                <a href=\\\"#\\\" class=\\\"brand-logo-link\\\">\\r\\n                    <img src=\\\"\\/storage\\/theme\\/42\\/mKjaC7o96snLqlvrNiWUhre6ToYNnCrRQ5iJAAkn.webp\\\" alt=\\\"Huawei\\\" class=\\\"brand-logo logo-huawei\\\">\\r\\n                <\\/a>\\r\\n                <a href=\\\"#\\\" class=\\\"brand-logo-link\\\">\\r\\n                    <img src=\\\"\\/storage\\/theme\\/42\\/aouSOFFs541Cv4Bu7YKER4I418y8GkVpA7jr9owz.webp\\\" alt=\\\"Xiaomi\\\" class=\\\"brand-logo logo-xiaomi\\\">\\r\\n                <\\/a>\\r\\n                <a href=\\\"#\\\" class=\\\"brand-logo-link\\\">\\r\\n                    <img src=\\\"\\/storage\\/theme\\/42\\/8EKdYRB1UZgov1PO1rbI9Jrj2hZR5xEn8XkShRCo.webp\\\" alt=\\\"Oppo\\\" class=\\\"brand-logo logo-oppo\\\">\\r\\n                <\\/a>\\r\\n            <\\/div>\\r\\n        <\\/div>\\r\\n    <\\/section>\",\"css\":\".shop-by-brand-section {\\r\\n            background-color: #ffffff;\\r\\n            padding-top: 3rem;\\r\\n            padding-bottom: 3rem;\\r\\n        }\\r\\n\\r\\n        .container {\\r\\n            max-width: 80rem;\\r\\n            margin-left: auto;\\r\\n            margin-right: auto;\\r\\n            padding-left: 1.5rem;\\r\\n            padding-right: 1.5rem;\\r\\n        }\\r\\n\\r\\n        .section-title {\\r\\n            text-align: center;\\r\\n            font-size: 1.5rem;\\r\\n            font-weight: 700;\\r\\n            letter-spacing: 0.1em;\\r\\n            color: rgb(31 41 55);\\r\\n        }\\r\\n\\r\\n        .brand-grid {\\r\\n            margin-top: 2.5rem;\\r\\n            display: grid;\\r\\n            grid-template-columns: repeat(2, 1fr);\\r\\n            align-items: center;\\r\\n            column-gap: 1.5rem;\\r\\n            row-gap: 2rem;\\r\\n            max-width: 32rem;\\r\\n            margin-left: auto;\\r\\n            margin-right: auto;\\r\\n        }\\r\\n\\r\\n        .brand-logo-link {\\r\\n            display: flex;\\r\\n            justify-content: center;\\r\\n            padding: 0.5rem;\\r\\n            transition: opacity 0.2s ease-in-out;\\r\\n        }\\r\\n\\r\\n        .brand-logo-link:hover {\\r\\n            opacity: 0.7;\\r\\n        }\\r\\n\\r\\n        .brand-logo {\\r\\n            object-fit: contain;\\r\\n        }\\r\\n\\r\\n        .logo-apple, .logo-motorola, .logo-huawei {\\r\\n             height: 3.25rem;\\r\\n        }\\r\\n        .logo-samsung, .logo-oppo {\\r\\n             height: 2.5rem;\\r\\n        }\\r\\n        .logo-xiaomi {\\r\\n             height: 3rem;\\r\\n        }\\r\\n\\r\\n        @media (min-width: 640px) {\\r\\n            .shop-by-brand-section {\\r\\n                padding-top: 4rem;\\r\\n                padding-bottom: 4rem;\\r\\n            }\\r\\n            .brand-grid {\\r\\n                max-width: 36rem;\\r\\n                grid-template-columns: repeat(3, 1fr);\\r\\n                column-gap: 2rem;\\r\\n            }\\r\\n        }\\r\\n\\r\\n        @media (min-width: 1024px) {\\r\\n            .container {\\r\\n                padding-left: 2rem;\\r\\n                padding-right: 2rem;\\r\\n            }\\r\\n            .brand-grid {\\r\\n                max-width: none;\\r\\n                margin-left: 0;\\r\\n                margin-right: 0;\\r\\n                grid-template-columns: repeat(6, 1fr);\\r\\n            }\\r\\n        }\"}"], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "packages/Webkul/Theme/src/Repositories/ThemeCustomizationRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Theme\\src\\Repositories\\ThemeCustomizationRepository.php", "line": 172}, {"index": 19, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Settings/ThemeController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Settings\\ThemeController.php", "line": 120}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.194745, "duration": 0.00206, "duration_str": "2.06ms", "memory": 0, "memory_str": null, "filename": "ThemeCustomizationRepository.php:172", "source": {"index": 18, "namespace": null, "name": "packages/Webkul/Theme/src/Repositories/ThemeCustomizationRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Theme\\src\\Repositories\\ThemeCustomizationRepository.php", "line": 172}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FTheme%2Fsrc%2FRepositories%2FThemeCustomizationRepository.php&line=172", "ajax": false, "filename": "ThemeCustomizationRepository.php", "line": "172"}, "connection": "mlk", "explain": null, "start_percent": 75.171, "width_percent": 11.758}, {"sql": "insert into `theme_customization_translations` (`theme_customization_id`, `locale`, `options`) values (43, 'gr', '{\\\"html\\\":\\\"<section class=\\\\\\\"shop-by-brand-section\\\\\\\">\\\\r\\\\n        <div class=\\\\\\\"container\\\\\\\">\\\\r\\\\n            <h2 class=\\\\\\\"section-title\\\\\\\">\\\\r\\\\n                SHOP BY BRAND\\\\r\\\\n            <\\\\/h2>\\\\r\\\\n\\\\r\\\\n            <div class=\\\\\\\"brand-grid\\\\\\\">\\\\r\\\\n                <a href=\\\\\\\"#\\\\\\\" class=\\\\\\\"brand-logo-link\\\\\\\">\\\\r\\\\n                    <img src=\\\\\\\"\\\\/storage\\\\/theme\\\\/42\\\\/wG6bHeSQVQVxYO67SU6nS1K3eQXLaRVdHMLpreCC.webp\\\\\\\" alt=\\\\\\\"Apple\\\\\\\" class=\\\\\\\"brand-logo logo-apple\\\\\\\">\\\\r\\\\n                <\\\\/a>\\\\r\\\\n                <a href=\\\\\\\"#\\\\\\\" class=\\\\\\\"brand-logo-link\\\\\\\">\\\\r\\\\n                    <img src=\\\\\\\"\\\\/storage\\\\/theme\\\\/42\\\\/lEBIAJiOip7tcMCDZn6dXngX4ieMbpqct900OgYp.webp\\\\\\\" alt=\\\\\\\"Samsung\\\\\\\" class=\\\\\\\"brand-logo logo-samsung\\\\\\\">\\\\r\\\\n                <\\\\/a>\\\\r\\\\n                <a href=\\\\\\\"#\\\\\\\" class=\\\\\\\"brand-logo-link\\\\\\\">\\\\r\\\\n                    <img src=\\\\\\\"\\\\/storage\\\\/theme\\\\/42\\\\/ZlhWdHlUNqBbsr9ap91NemQT8Vaas46AiwVEbOYF.webp\\\\\\\" alt=\\\\\\\"Motorola\\\\\\\" class=\\\\\\\"brand-logo logo-motorola\\\\\\\">\\\\r\\\\n                <\\\\/a>\\\\r\\\\n                <a href=\\\\\\\"#\\\\\\\" class=\\\\\\\"brand-logo-link\\\\\\\">\\\\r\\\\n                    <img src=\\\\\\\"\\\\/storage\\\\/theme\\\\/42\\\\/mKjaC7o96snLqlvrNiWUhre6ToYNnCrRQ5iJAAkn.webp\\\\\\\" alt=\\\\\\\"Huawei\\\\\\\" class=\\\\\\\"brand-logo logo-huawei\\\\\\\">\\\\r\\\\n                <\\\\/a>\\\\r\\\\n                <a href=\\\\\\\"#\\\\\\\" class=\\\\\\\"brand-logo-link\\\\\\\">\\\\r\\\\n                    <img src=\\\\\\\"\\\\/storage\\\\/theme\\\\/42\\\\/aouSOFFs541Cv4Bu7YKER4I418y8GkVpA7jr9owz.webp\\\\\\\" alt=\\\\\\\"Xiaomi\\\\\\\" class=\\\\\\\"brand-logo logo-xiaomi\\\\\\\">\\\\r\\\\n                <\\\\/a>\\\\r\\\\n                <a href=\\\\\\\"#\\\\\\\" class=\\\\\\\"brand-logo-link\\\\\\\">\\\\r\\\\n                    <img src=\\\\\\\"\\\\/storage\\\\/theme\\\\/42\\\\/8EKdYRB1UZgov1PO1rbI9Jrj2hZR5xEn8XkShRCo.webp\\\\\\\" alt=\\\\\\\"Oppo\\\\\\\" class=\\\\\\\"brand-logo logo-oppo\\\\\\\">\\\\r\\\\n                <\\\\/a>\\\\r\\\\n            <\\\\/div>\\\\r\\\\n        <\\\\/div>\\\\r\\\\n    <\\\\/section>\\\",\\\"css\\\":\\\".shop-by-brand-section {\\\\r\\\\n            background-color: #ffffff;\\\\r\\\\n            padding-top: 3rem;\\\\r\\\\n            padding-bottom: 3rem;\\\\r\\\\n        }\\\\r\\\\n\\\\r\\\\n        .container {\\\\r\\\\n            max-width: 80rem;\\\\r\\\\n            margin-left: auto;\\\\r\\\\n            margin-right: auto;\\\\r\\\\n            padding-left: 1.5rem;\\\\r\\\\n            padding-right: 1.5rem;\\\\r\\\\n        }\\\\r\\\\n\\\\r\\\\n        .section-title {\\\\r\\\\n            text-align: center;\\\\r\\\\n            font-size: 1.5rem;\\\\r\\\\n            font-weight: 700;\\\\r\\\\n            letter-spacing: 0.1em;\\\\r\\\\n            color: rgb(31 41 55);\\\\r\\\\n        }\\\\r\\\\n\\\\r\\\\n        .brand-grid {\\\\r\\\\n            margin-top: 2.5rem;\\\\r\\\\n            display: grid;\\\\r\\\\n            grid-template-columns: repeat(2, 1fr);\\\\r\\\\n            align-items: center;\\\\r\\\\n            column-gap: 1.5rem;\\\\r\\\\n            row-gap: 2rem;\\\\r\\\\n            max-width: 32rem;\\\\r\\\\n            margin-left: auto;\\\\r\\\\n            margin-right: auto;\\\\r\\\\n        }\\\\r\\\\n\\\\r\\\\n        .brand-logo-link {\\\\r\\\\n            display: flex;\\\\r\\\\n            justify-content: center;\\\\r\\\\n            padding: 0.5rem;\\\\r\\\\n            transition: opacity 0.2s ease-in-out;\\\\r\\\\n        }\\\\r\\\\n\\\\r\\\\n        .brand-logo-link:hover {\\\\r\\\\n            opacity: 0.7;\\\\r\\\\n        }\\\\r\\\\n\\\\r\\\\n        .brand-logo {\\\\r\\\\n            object-fit: contain;\\\\r\\\\n        }\\\\r\\\\n\\\\r\\\\n        .logo-apple, .logo-motorola, .logo-huawei {\\\\r\\\\n             height: 3.25rem;\\\\r\\\\n        }\\\\r\\\\n        .logo-samsung, .logo-oppo {\\\\r\\\\n             height: 2.5rem;\\\\r\\\\n        }\\\\r\\\\n        .logo-xiaomi {\\\\r\\\\n             height: 3rem;\\\\r\\\\n        }\\\\r\\\\n\\\\r\\\\n        @media (min-width: 640px) {\\\\r\\\\n            .shop-by-brand-section {\\\\r\\\\n                padding-top: 4rem;\\\\r\\\\n                padding-bottom: 4rem;\\\\r\\\\n            }\\\\r\\\\n            .brand-grid {\\\\r\\\\n                max-width: 36rem;\\\\r\\\\n                grid-template-columns: repeat(3, 1fr);\\\\r\\\\n                column-gap: 2rem;\\\\r\\\\n            }\\\\r\\\\n        }\\\\r\\\\n\\\\r\\\\n        @media (min-width: 1024px) {\\\\r\\\\n            .container {\\\\r\\\\n                padding-left: 2rem;\\\\r\\\\n                padding-right: 2rem;\\\\r\\\\n            }\\\\r\\\\n            .brand-grid {\\\\r\\\\n                max-width: none;\\\\r\\\\n                margin-left: 0;\\\\r\\\\n                margin-right: 0;\\\\r\\\\n                grid-template-columns: repeat(6, 1fr);\\\\r\\\\n            }\\\\r\\\\n        }\\\"}')", "type": "query", "params": [], "bindings": [43, "gr", "{\"html\":\"<section class=\\\"shop-by-brand-section\\\">\\r\\n        <div class=\\\"container\\\">\\r\\n            <h2 class=\\\"section-title\\\">\\r\\n                SHOP BY BRAND\\r\\n            <\\/h2>\\r\\n\\r\\n            <div class=\\\"brand-grid\\\">\\r\\n                <a href=\\\"#\\\" class=\\\"brand-logo-link\\\">\\r\\n                    <img src=\\\"\\/storage\\/theme\\/42\\/wG6bHeSQVQVxYO67SU6nS1K3eQXLaRVdHMLpreCC.webp\\\" alt=\\\"Apple\\\" class=\\\"brand-logo logo-apple\\\">\\r\\n                <\\/a>\\r\\n                <a href=\\\"#\\\" class=\\\"brand-logo-link\\\">\\r\\n                    <img src=\\\"\\/storage\\/theme\\/42\\/lEBIAJiOip7tcMCDZn6dXngX4ieMbpqct900OgYp.webp\\\" alt=\\\"Samsung\\\" class=\\\"brand-logo logo-samsung\\\">\\r\\n                <\\/a>\\r\\n                <a href=\\\"#\\\" class=\\\"brand-logo-link\\\">\\r\\n                    <img src=\\\"\\/storage\\/theme\\/42\\/ZlhWdHlUNqBbsr9ap91NemQT8Vaas46AiwVEbOYF.webp\\\" alt=\\\"Motorola\\\" class=\\\"brand-logo logo-motorola\\\">\\r\\n                <\\/a>\\r\\n                <a href=\\\"#\\\" class=\\\"brand-logo-link\\\">\\r\\n                    <img src=\\\"\\/storage\\/theme\\/42\\/mKjaC7o96snLqlvrNiWUhre6ToYNnCrRQ5iJAAkn.webp\\\" alt=\\\"Huawei\\\" class=\\\"brand-logo logo-huawei\\\">\\r\\n                <\\/a>\\r\\n                <a href=\\\"#\\\" class=\\\"brand-logo-link\\\">\\r\\n                    <img src=\\\"\\/storage\\/theme\\/42\\/aouSOFFs541Cv4Bu7YKER4I418y8GkVpA7jr9owz.webp\\\" alt=\\\"Xiaomi\\\" class=\\\"brand-logo logo-xiaomi\\\">\\r\\n                <\\/a>\\r\\n                <a href=\\\"#\\\" class=\\\"brand-logo-link\\\">\\r\\n                    <img src=\\\"\\/storage\\/theme\\/42\\/8EKdYRB1UZgov1PO1rbI9Jrj2hZR5xEn8XkShRCo.webp\\\" alt=\\\"Oppo\\\" class=\\\"brand-logo logo-oppo\\\">\\r\\n                <\\/a>\\r\\n            <\\/div>\\r\\n        <\\/div>\\r\\n    <\\/section>\",\"css\":\".shop-by-brand-section {\\r\\n            background-color: #ffffff;\\r\\n            padding-top: 3rem;\\r\\n            padding-bottom: 3rem;\\r\\n        }\\r\\n\\r\\n        .container {\\r\\n            max-width: 80rem;\\r\\n            margin-left: auto;\\r\\n            margin-right: auto;\\r\\n            padding-left: 1.5rem;\\r\\n            padding-right: 1.5rem;\\r\\n        }\\r\\n\\r\\n        .section-title {\\r\\n            text-align: center;\\r\\n            font-size: 1.5rem;\\r\\n            font-weight: 700;\\r\\n            letter-spacing: 0.1em;\\r\\n            color: rgb(31 41 55);\\r\\n        }\\r\\n\\r\\n        .brand-grid {\\r\\n            margin-top: 2.5rem;\\r\\n            display: grid;\\r\\n            grid-template-columns: repeat(2, 1fr);\\r\\n            align-items: center;\\r\\n            column-gap: 1.5rem;\\r\\n            row-gap: 2rem;\\r\\n            max-width: 32rem;\\r\\n            margin-left: auto;\\r\\n            margin-right: auto;\\r\\n        }\\r\\n\\r\\n        .brand-logo-link {\\r\\n            display: flex;\\r\\n            justify-content: center;\\r\\n            padding: 0.5rem;\\r\\n            transition: opacity 0.2s ease-in-out;\\r\\n        }\\r\\n\\r\\n        .brand-logo-link:hover {\\r\\n            opacity: 0.7;\\r\\n        }\\r\\n\\r\\n        .brand-logo {\\r\\n            object-fit: contain;\\r\\n        }\\r\\n\\r\\n        .logo-apple, .logo-motorola, .logo-huawei {\\r\\n             height: 3.25rem;\\r\\n        }\\r\\n        .logo-samsung, .logo-oppo {\\r\\n             height: 2.5rem;\\r\\n        }\\r\\n        .logo-xiaomi {\\r\\n             height: 3rem;\\r\\n        }\\r\\n\\r\\n        @media (min-width: 640px) {\\r\\n            .shop-by-brand-section {\\r\\n                padding-top: 4rem;\\r\\n                padding-bottom: 4rem;\\r\\n            }\\r\\n            .brand-grid {\\r\\n                max-width: 36rem;\\r\\n                grid-template-columns: repeat(3, 1fr);\\r\\n                column-gap: 2rem;\\r\\n            }\\r\\n        }\\r\\n\\r\\n        @media (min-width: 1024px) {\\r\\n            .container {\\r\\n                padding-left: 2rem;\\r\\n                padding-right: 2rem;\\r\\n            }\\r\\n            .brand-grid {\\r\\n                max-width: none;\\r\\n                margin-left: 0;\\r\\n                margin-right: 0;\\r\\n                grid-template-columns: repeat(6, 1fr);\\r\\n            }\\r\\n        }\"}"], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "packages/Webkul/Theme/src/Repositories/ThemeCustomizationRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Theme\\src\\Repositories\\ThemeCustomizationRepository.php", "line": 172}, {"index": 19, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Settings/ThemeController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Settings\\ThemeController.php", "line": 120}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.1978352, "duration": 0.00204, "duration_str": "2.04ms", "memory": 0, "memory_str": null, "filename": "ThemeCustomizationRepository.php:172", "source": {"index": 18, "namespace": null, "name": "packages/Webkul/Theme/src/Repositories/ThemeCustomizationRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Theme\\src\\Repositories\\ThemeCustomizationRepository.php", "line": 172}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FTheme%2Fsrc%2FRepositories%2FThemeCustomizationRepository.php&line=172", "ajax": false, "filename": "ThemeCustomizationRepository.php", "line": "172"}, "connection": "mlk", "explain": null, "start_percent": 86.929, "width_percent": 11.644}, {"sql": "select * from `currencies` where `currencies`.`id` = 2 limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 22, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Core.php", "line": 390}, {"index": 23, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Core.php", "line": 433}, {"index": 24, "namespace": null, "name": "packages/Webkul/FPC/src/Hasher/DefaultHasher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\FPC\\src\\Hasher\\DefaultHasher.php", "line": 29}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-responsecache/src/Hasher/DefaultHasher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\spatie\\laravel-responsecache\\src\\Hasher\\DefaultHasher.php", "line": 18}], "start": **********.20542, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 20, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 98.573, "width_percent": 1.427}]}, "models": {"data": {"Webkul\\Core\\Models\\Locale": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FModels%2FLocale.php&line=1", "ajax": false, "filename": "Locale.php", "line": "?"}}, "Webkul\\Core\\Models\\Channel": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FModels%2FChannel.php&line=1", "ajax": false, "filename": "Channel.php", "line": "?"}}, "Webkul\\User\\Models\\Admin": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FUser%2Fsrc%2FModels%2FAdmin.php&line=1", "ajax": false, "filename": "Admin.php", "line": "?"}}, "Webkul\\User\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FUser%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "Webkul\\Theme\\Models\\ThemeCustomization": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FTheme%2Fsrc%2FModels%2FThemeCustomization.php&line=1", "ajax": false, "filename": "ThemeCustomization.php", "line": "?"}}, "Webkul\\Core\\Models\\Currency": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FModels%2FCurrency.php&line=1", "ajax": false, "filename": "Currency.php", "line": "?"}}}, "count": 10, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "302 Found", "full_url": "http://mlk.test/admin/settings/themes/edit/43", "action_name": "admin.settings.themes.update", "controller_action": "Webkul\\Admin\\Http\\Controllers\\Settings\\ThemeController@update", "uri": "POST admin/settings/themes/edit/{id}", "controller": "Webkul\\Admin\\Http\\Controllers\\Settings\\ThemeController@update<a href=\"phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FHttp%2FControllers%2FSettings%2FThemeController.php&line=90\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "admin/settings/themes", "file": "<a href=\"phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FHttp%2FControllers%2FSettings%2FThemeController.php&line=90\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">packages/Webkul/Admin/src/Http/Controllers/Settings/ThemeController.php:90-127</a>", "middleware": "web, admin_locale, Webkul\\Core\\Http\\Middleware\\PreventRequestsDuringMaintenance, admin, Webkul\\Core\\Http\\Middleware\\NoCacheMiddleware", "duration": "366ms", "peak_memory": "42MB", "response": "Redirect to http://mlk.test/admin/settings/themes", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-821966621 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-821966621\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-644637571 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">RL8eYWcLOX1nKpLrVKRAtWfTglgDAXuESTadrPQu</span>\"\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>en</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>options</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>html</span>\" => \"\"\"\n        <span class=sf-dump-str title=\"1556 characters\">&lt;section class=&quot;shop-by-brand-section&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"1556 characters\">        &lt;div class=&quot;container&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"1556 characters\">            &lt;h2 class=&quot;section-title&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"1556 characters\">                SHOP BY BRAND<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"1556 characters\">            &lt;/h2&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"1556 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"1556 characters\">            &lt;div class=&quot;brand-grid&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"1556 characters\">                &lt;a href=&quot;#&quot; class=&quot;brand-logo-link&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"1556 characters\">                    &lt;img src=&quot;/storage/theme/42/wG6bHeSQVQVxYO67SU6nS1K3eQXLaRVdHMLpreCC.webp&quot; alt=&quot;Apple&quot; class=&quot;brand-logo logo-apple&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"1556 characters\">                &lt;/a&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"1556 characters\">                &lt;a href=&quot;#&quot; class=&quot;brand-logo-link&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"1556 characters\">                    &lt;img src=&quot;/storage/theme/42/lEBIAJiOip7tcMCDZn6dXngX4ieMbpqct900OgYp.webp&quot; alt=&quot;Samsung&quot; class=&quot;brand-logo logo-samsung&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"1556 characters\">                &lt;/a&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"1556 characters\">                &lt;a href=&quot;#&quot; class=&quot;brand-logo-link&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"1556 characters\">                    &lt;img src=&quot;/storage/theme/42/ZlhWdHlUNqBbsr9ap91NemQT8Vaas46AiwVEbOYF.webp&quot; alt=&quot;Motorola&quot; class=&quot;brand-logo logo-motorola&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"1556 characters\">                &lt;/a&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"1556 characters\">                &lt;a href=&quot;#&quot; class=&quot;brand-logo-link&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"1556 characters\">                    &lt;img src=&quot;/storage/theme/42/mKjaC7o96snLqlvrNiWUhre6ToYNnCrRQ5iJAAkn.webp&quot; alt=&quot;Huawei&quot; class=&quot;brand-logo logo-huawei&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"1556 characters\">                &lt;/a&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"1556 characters\">                &lt;a href=&quot;#&quot; class=&quot;brand-logo-link&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"1556 characters\">                    &lt;img src=&quot;/storage/theme/42/aouSOFFs541Cv4Bu7YKER4I418y8GkVpA7jr9owz.webp&quot; alt=&quot;Xiaomi&quot; class=&quot;brand-logo logo-xiaomi&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"1556 characters\">                &lt;/a&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"1556 characters\">                &lt;a href=&quot;#&quot; class=&quot;brand-logo-link&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"1556 characters\">                    &lt;img src=&quot;/storage/theme/42/8EKdYRB1UZgov1PO1rbI9Jrj2hZR5xEn8XkShRCo.webp&quot; alt=&quot;Oppo&quot; class=&quot;brand-logo logo-oppo&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"1556 characters\">                &lt;/a&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"1556 characters\">            &lt;/div&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"1556 characters\">        &lt;/div&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"1556 characters\">    &lt;/section&gt;</span>\n        \"\"\"\n      \"<span class=sf-dump-key>css</span>\" => \"\"\"\n        <span class=sf-dump-str title=\"2172 characters\">.shop-by-brand-section {<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2172 characters\">            background-color: #ffffff;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2172 characters\">            padding-top: 3rem;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2172 characters\">            padding-bottom: 3rem;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2172 characters\">        }<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2172 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2172 characters\">        .container {<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2172 characters\">            max-width: 80rem;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2172 characters\">            margin-left: auto;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2172 characters\">            margin-right: auto;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2172 characters\">            padding-left: 1.5rem;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2172 characters\">            padding-right: 1.5rem;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2172 characters\">        }<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2172 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2172 characters\">        .section-title {<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2172 characters\">            text-align: center;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2172 characters\">            font-size: 1.5rem;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2172 characters\">            font-weight: 700;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2172 characters\">            letter-spacing: 0.1em;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2172 characters\">            color: rgb(31 41 55);<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2172 characters\">        }<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2172 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2172 characters\">        .brand-grid {<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2172 characters\">            margin-top: 2.5rem;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2172 characters\">            display: grid;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2172 characters\">            grid-template-columns: repeat(2, 1fr);<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2172 characters\">            align-items: center;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2172 characters\">            column-gap: 1.5rem;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2172 characters\">            row-gap: 2rem;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2172 characters\">            max-width: 32rem;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2172 characters\">            margin-left: auto;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2172 characters\">            margin-right: auto;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2172 characters\">        }<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2172 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2172 characters\">        .brand-logo-link {<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2172 characters\">            display: flex;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2172 characters\">            justify-content: center;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2172 characters\">            padding: 0.5rem;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2172 characters\">            transition: opacity 0.2s ease-in-out;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2172 characters\">        }<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2172 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2172 characters\">        .brand-logo-link:hover {<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2172 characters\">            opacity: 0.7;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2172 characters\">        }<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2172 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2172 characters\">        .brand-logo {<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2172 characters\">            object-fit: contain;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2172 characters\">        }<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2172 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2172 characters\">        .logo-apple, .logo-motorola, .logo-huawei {<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2172 characters\">             height: 3.25rem;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2172 characters\">        }<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2172 characters\">        .logo-samsung, .logo-oppo {<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2172 characters\">             height: 2.5rem;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2172 characters\">        }<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2172 characters\">        .logo-xiaomi {<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2172 characters\">             height: 3rem;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2172 characters\">        }<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2172 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2172 characters\">        @media (min-width: 640px) {<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2172 characters\">            .shop-by-brand-section {<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2172 characters\">                padding-top: 4rem;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2172 characters\">                padding-bottom: 4rem;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2172 characters\">            }<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2172 characters\">            .brand-grid {<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2172 characters\">                max-width: 36rem;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2172 characters\">                grid-template-columns: repeat(3, 1fr);<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2172 characters\">                column-gap: 2rem;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2172 characters\">            }<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2172 characters\">        }<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2172 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2172 characters\">        @media (min-width: 1024px) {<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2172 characters\">            .container {<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2172 characters\">                padding-left: 2rem;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2172 characters\">                padding-right: 2rem;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2172 characters\">            }<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2172 characters\">            .brand-grid {<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2172 characters\">                max-width: none;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2172 characters\">                margin-left: 0;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2172 characters\">                margin-right: 0;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2172 characters\">                grid-template-columns: repeat(6, 1fr);<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2172 characters\">            }<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2172 characters\">        }</span>\n        \"\"\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"14 characters\">static_content</span>\"\n  \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"5 characters\">brand</span>\"\n  \"<span class=sf-dump-key>sort_order</span>\" => \"<span class=sf-dump-str>4</span>\"\n  \"<span class=sf-dump-key>channel_id</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>theme_code</span>\" => \"<span class=sf-dump-str title=\"7 characters\">default</span>\"\n  \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"2 characters\">on</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-644637571\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-612111711 data-indent-pad=\"  \"><span class=sf-dump-note>array:13</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1018 characters\">admin_locale=eyJpdiI6InlVTlI4cytSTEhwU1oxNXhFNmszTHc9PSIsInZhbHVlIjoiL1djczhIanIvN1ZXTlhGdmpwMWJhNkpqMnNiMHcxMjBic21GQ0ltRzRZV25DczdBcXZaVUNJWlFEakx1bjV6byIsIm1hYyI6IjhmNjlhYzdkYWNjMTVkNjA5ZThmMzlhYmViM2UwYTFiZDQxZDY4ZjEwZTg3NGVjZGNhOTAzODA3ODhhNjllMzQiLCJ0YWciOiIifQ%3D%3D; sidebar_collapsed=0; dark_mode=0; XSRF-TOKEN=eyJpdiI6ImNZZkRpM2hFSlF3b2dyanZZTkg2bGc9PSIsInZhbHVlIjoiWHh0dm8rbHQwd2NMUjdEckJIVFVMZExIK08vSjNIaWIyQ2ZTTnhTNWV4OHl5SDg4bGtBWWlUYzNmUXA5ZVJaQnk1bFFtdnlUV3RtZVRmSzFJRkJLYnYxam5Ga1FhcEVydzFWTkFwMlNBL1hOS0FOMUxRa2phU09oWnRPUHAwcDYiLCJtYWMiOiI4Y2Q5MzA0NTYzMTQ1ZDFmNjcwMzI1ZmVjMjZjMWVmNjQ4Yzc2ZjhkOWYxZDVlMDhmOWQwZWIyNjJkZTUwN2NkIiwidGFnIjoiIn0%3D; mlk_session=eyJpdiI6IjhWelBFTkM2OVV6bXp3VldyckUwMVE9PSIsInZhbHVlIjoiWlFvT3p2SThublN5YlVBc2JmQ3BndnZJdWZQdzkyTmhwNWJUeDFhZUE5SC83KzBKd2x1L2hQNmJDVVNOVm9UNTd0Y09HZk11aWZzcmZLSkhkbE1yV3lJUHRCSGlSUlNsZ0wzZ1oyQUgwQUYzMXhmMDVlYVo3ei9XdnV5UGVCZFciLCJtYWMiOiJhM2NjZjVmYjJhODc2OWIyNWVhZmMwOGU0YWQ5NDE5ZTA1NDFlMWEzMjZmN2ExMzBkZWU1ODE4ZmZlYzQ1YTNhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">zh-CN,zh;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"45 characters\">http://mlk.test/admin/settings/themes/edit/43</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">multipart/form-data; boundary=----WebKitFormBoundary8S9vyl16Dqsbf6Yw</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"15 characters\">http://mlk.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">4959</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">mlk.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-612111711\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-84798447 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>admin_locale</span>\" => \"<span class=sf-dump-str title=\"5 characters\">zh_CN</span>\"\n  \"<span class=sf-dump-key>sidebar_collapsed</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>dark_mode</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">RL8eYWcLOX1nKpLrVKRAtWfTglgDAXuESTadrPQu</span>\"\n  \"<span class=sf-dump-key>mlk_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Zao9ptAmXhjVm7WD2j4izotzmKLg3wYf4OgogoiC</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-84798447\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-135965304 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 07 Aug 2025 15:37:29 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://mlk.test/admin/settings/themes</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>0</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-135965304\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-147855040 data-indent-pad=\"  \"><span class=sf-dump-note>array:10</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">RL8eYWcLOX1nKpLrVKRAtWfTglgDAXuESTadrPQu</span>\"\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">it</span>\"\n  \"<span class=sf-dump-key>currency</span>\" => \"<span class=sf-dump-str title=\"3 characters\">EUR</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"45 characters\">http://mlk.test/admin/settings/themes/edit/43</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>admin_locale</span>\" => \"<span class=sf-dump-str title=\"5 characters\">zh_CN</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>success</span>\" => \"<span class=sf-dump-str title=\"6 characters\">&#25104;&#21151;&#26356;&#26032;&#20027;&#39064;</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-147855040\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "302 Found", "full_url": "http://mlk.test/admin/settings/themes/edit/43", "action_name": "admin.settings.themes.update", "controller_action": "Webkul\\Admin\\Http\\Controllers\\Settings\\ThemeController@update"}, "badge": "302 Found"}}