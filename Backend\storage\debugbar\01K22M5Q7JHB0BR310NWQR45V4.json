{"__meta": {"id": "01K22M5Q7JHB0BR310NWQR45V4", "datetime": "2025-08-07 17:00:08", "utime": **********.434596, "method": "POST", "uri": "/admin/settings/themes/edit/45", "ip": "127.0.0.1"}, "modules": {"count": 3, "modules": [{"name": "Webkul\\Core", "models": ["Webkul\\Core\\Models\\Channel (1)", "Webkul\\Core\\Models\\Locale (5)", "Webkul\\Core\\Models\\Currency (1)"], "views": [], "queries": [{"sql": "select * from `channels` where `hostname` in ('mlk.test', 'http://mlk.test', 'https://mlk.test')", "duration": 1.92, "duration_str": "1.92s", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 1.9, "duration_str": "1.9s", "connection": "mlk"}, {"sql": "select * from `currencies` where `currencies`.`id` = 2 limit 1", "duration": 1.78, "duration_str": "1.78s", "connection": "mlk"}]}, {"name": "Webkul\\Theme", "models": ["Webkul\\Theme\\Models\\ThemeCustomization (1)", "Webkul\\Theme\\Models\\ThemeCustomizationTranslation (5)"], "views": [], "queries": [{"sql": "select * from `theme_customizations` where `theme_customizations`.`id` = 45 limit 1", "duration": 1.74, "duration_str": "1.74s", "connection": "mlk"}, {"sql": "select * from `theme_customization_translations` where `theme_customization_translations`.`theme_customization_id` in (45)", "duration": 1.87, "duration_str": "1.87s", "connection": "mlk"}, {"sql": "update `theme_customizations` set `status` = 1, `theme_customizations`.`updated_at` = '2025-08-07 17:00:08' where `id` = 45", "duration": 3.86, "duration_str": "3.86s", "connection": "mlk"}, {"sql": "update `theme_customization_translations` set `options` = '{\\\"html\\\":\\\"<div class=\\\\\"carousel\\\\\">\\r\\n    <div class=\\\\\"carousel-track\\\\\">\\r\\n        <div class=\\\\\"slide current-slide\\\\\" style=\\\\\"background-image: url(\\'https:\\/\\/images.unsplash.com\\/photo-1506744038136-46273834b3fb?q=80&w=2070&auto=format&fit=crop\\');\\\\\">\\r\\n            <div class=\\\\\"hero-text\\\\\">\\r\\n                 <h1>Like gentle sunshine<\\/h1>\\r\\n\\r\\n                <p>on quiet leaves<\\/p>\\r\\n            <\\/div>\\r\\n        <\\/div>\\r\\n        <div class=\\\\\"slide\\\\\" style=\\\\\"background-image: url(\\'https:\\/\\/images.unsplash.com\\/photo-1470770841072-f978cf4d019e?q=80&w=2070&auto=format&fit=crop\\');\\\\\">\\r\\n            <div class=\\\\\"hero-text\\\\\">\\r\\n                 <h1>Whispers of the Forest<\\/h1>\\r\\n\\r\\n                <p>a story in every branch<\\/p>\\r\\n            <\\/div>\\r\\n        <\\/div>\\r\\n        <div class=\\\\\"slide\\\\\" style=\\\\\"background-image: url(\\'https:\\/\\/images.unsplash.com\\/photo-1439405326853-58f2724f4d31?q=80&w=2070&auto=format&fit=crop\\');\\\\\">\\r\\n            <div class=\\\\\"hero-text\\\\\">\\r\\n                 <h1>Echoes of the Ocean<\\/h1>\\r\\n\\r\\n                <p>deep and serene<\\/p>\\r\\n            <\\/div>\\r\\n        <\\/div>\\r\\n    <\\/div>\\r\\n    <button class=\\\\\"arrow left-arrow\\\\\">&#10094;<\\/button>\\r\\n    <button class=\\\\\"arrow right-arrow\\\\\">&#10095;<\\/button>\\r\\n    <div class=\\\\\"carousel-dots\\\\\"><\\/div>\\r\\n<\\/div>\\\",\\\"css\\\":\\\".carousel {\\r\\n            position: relative;\\r\\n            width: 100%;\\r\\n            height: 55vh;\\r\\n            min-height: 400px;\\r\\n            overflow: hidden;\\r\\n        }\\r\\n\\r\\n        .carousel-track {\\r\\n            position: relative;\\r\\n            height: 100%;\\r\\n            display: flex;\\r\\n            transition: transform 0.5s ease-in-out;\\r\\n        }\\r\\n\\r\\n        .slide {\\r\\n            flex: 1 0 100%;\\r\\n            width: 100%;\\r\\n            height: 100%;\\r\\n            background-size: cover;\\r\\n            background-position: center;\\r\\n            display: flex;\\r\\n            justify-content: center;\\r\\n            align-items: center;\\r\\n        }\\r\\n\\r\\n        .hero-text {\\r\\n            text-align: center;\\r\\n            color: white;\\r\\n            max-width: 90%;\\r\\n            text-shadow: 0 2px 8px rgba(0, 0, 0, 0.5);\\r\\n            opacity: 0;\\r\\n            transform: translateY(20px);\\r\\n            transition: opacity 0.5s ease-out 0.4s, transform 0.5s ease-out 0.4s;\\r\\n        }\\r\\n\\r\\n        .current-slide .hero-text {\\r\\n            opacity: 1;\\r\\n            transform: translateY(0);\\r\\n        }\\r\\n\\r\\n        .hero-text h1 {\\r\\n            font-size: 2.75rem;\\r\\n            font-weight: 500;\\r\\n            margin-bottom: 0.5rem;\\r\\n        }\\r\\n\\r\\n        .hero-text p {\\r\\n            font-size: 1.1rem;\\r\\n            font-weight: 300;\\r\\n        }\\r\\n        \\r\\n        .arrow {\\r\\n            position: absolute;\\r\\n            top: 50%;\\r\\n            transform: translateY(-50%);\\r\\n            background: rgba(0, 0, 0, 0.1);\\r\\n            border: 1px solid rgba(255, 255, 255, 0.2);\\r\\n            color: rgba(255, 255, 255, 0.8);\\r\\n            font-size: 1.25rem;\\r\\n            padding: 8px 14px;\\r\\n            border-radius: 50%;\\r\\n            cursor: pointer;\\r\\n            z-index: 10;\\r\\n            transition: all 0.2s ease-in-out;\\r\\n        }\\r\\n        \\r\\n        .arrow:hover {\\r\\n            background-color: rgba(0, 0, 0, 0.4);\\r\\n            color: white;\\r\\n            border-color: transparent;\\r\\n        }\\r\\n\\r\\n        .left-arrow { left: 30px; }\\r\\n        .right-arrow { right: 30px; }\\r\\n        \\r\\n        .carousel-dots {\\r\\n            position: absolute;\\r\\n            bottom: 20px;\\r\\n            left: 50%;\\r\\n            transform: translateX(-50%);\\r\\n            display: flex;\\r\\n            gap: 12px;\\r\\n            z-index: 10;\\r\\n        }\\r\\n\\r\\n        .dot {\\r\\n            border: none;\\r\\n            width: 10px;\\r\\n            height: 10px;\\r\\n            border-radius: 50%;\\r\\n            background-color: rgba(255, 255, 255, 0.5);\\r\\n            cursor: pointer;\\r\\n            transition: all 0.2s ease;\\r\\n        }\\r\\n\\r\\n        .dot.current-dot {\\r\\n            background-color: white;\\r\\n            transform: scale(1.2);\\r\\n        }\\r\\n        \\r\\n        @media (max-width: 768px) {\\r\\n            .hero-text h1 { font-size: 1.8rem; }\\r\\n            .hero-text p { font-size: 1rem; }\\r\\n            .arrow { display: none; }\\r\\n        }\\\"}' where `id` = 196", "duration": 2.15, "duration_str": "2.15s", "connection": "mlk"}]}, {"name": "Webkul\\User", "models": ["Webkul\\User\\Models\\Admin (1)", "Webkul\\User\\Models\\Role (1)"], "views": [], "queries": [{"sql": "select count(*) as aggregate from `admins`", "duration": 0.3, "duration_str": "300ms", "connection": "mlk"}, {"sql": "select count(*) as aggregate from `admins`", "duration": 0.2, "duration_str": "200ms", "connection": "mlk"}, {"sql": "select * from `admins` where `id` = 1 limit 1", "duration": 0.2, "duration_str": "200ms", "connection": "mlk"}, {"sql": "select * from `roles` where `roles`.`id` = 1 limit 1", "duration": 2, "duration_str": "2s", "connection": "mlk"}]}]}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.100991, "end": **********.442014, "duration": 0.34102296829223633, "duration_str": "341ms", "measures": [{"label": "Booting", "start": **********.100991, "relative_start": 0, "end": **********.32834, "relative_end": **********.32834, "duration": 0.*****************, "duration_str": "227ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.32835, "relative_start": 0.*****************, "end": **********.442015, "relative_end": 9.5367431640625e-07, "duration": 0.*****************, "duration_str": "114ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.341181, "relative_start": 0.****************, "end": **********.343503, "relative_end": **********.343503, "duration": 0.002321958541870117, "duration_str": "2.32ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.432748, "relative_start": 0.****************, "end": **********.433068, "relative_end": **********.433068, "duration": 0.0003199577331542969, "duration_str": "320μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "39MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.2.0", "Environment": "local", "Debug Mode": "Enabled", "URL": "mlk.test", "Timezone": "Africa/Lagos", "Locale": "zh_CN"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 13, "nb_statements": 13, "nb_visible_statements": 13, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.018709999999999997, "accumulated_duration_str": "18.71ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select exists (select 1 from information_schema.tables where table_schema = 'mlk' and table_name = 'admins' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 32}, {"index": 14, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 44}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 1004}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 832}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 1078}], "start": **********.3566961, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "DatabaseManager.php:32", "source": {"index": 13, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 32}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FInstaller%2Fsrc%2FHelpers%2FDatabaseManager.php&line=32", "ajax": false, "filename": "DatabaseManager.php", "line": "32"}, "connection": "mlk", "explain": null, "start_percent": 0, "width_percent": 2.565}, {"sql": "select count(*) as aggregate from `admins`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 38}, {"index": 16, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 44}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 1004}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 832}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 1078}], "start": **********.360861, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "DatabaseManager.php:38", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 38}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FInstaller%2Fsrc%2FHelpers%2FDatabaseManager.php&line=38", "ajax": false, "filename": "DatabaseManager.php", "line": "38"}, "connection": "mlk", "explain": null, "start_percent": 2.565, "width_percent": 1.603}, {"sql": "select * from `channels` where `hostname` in ('mlk.test', 'http://mlk.test', 'https://mlk.test')", "type": "query", "params": [], "bindings": ["mlk.test", "http://mlk.test", "https://mlk.test"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 578}, {"index": 16, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Core.php", "line": 143}, {"index": 17, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 127}, {"index": 18, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 45}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 1004}], "start": **********.368552, "duration": 0.0019199999999999998, "duration_str": "1.92ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:578", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 578}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=578", "ajax": false, "filename": "BaseRepository.php", "line": "578"}, "connection": "mlk", "explain": null, "start_percent": 4.169, "width_percent": 10.262}, {"sql": "select exists (select 1 from information_schema.tables where table_schema = 'mlk' and table_name = 'admins' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 32}, {"index": 14, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 59}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 16, "namespace": "middleware", "name": "admin_locale", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Middleware\\AdminLocale.php", "line": 46}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.372358, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "DatabaseManager.php:32", "source": {"index": 13, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 32}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FInstaller%2Fsrc%2FHelpers%2FDatabaseManager.php&line=32", "ajax": false, "filename": "DatabaseManager.php", "line": "32"}, "connection": "mlk", "explain": null, "start_percent": 14.431, "width_percent": 1.657}, {"sql": "select count(*) as aggregate from `admins`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 38}, {"index": 16, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 18, "namespace": "middleware", "name": "admin_locale", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Middleware\\AdminLocale.php", "line": 46}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.373869, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "DatabaseManager.php:38", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 38}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FInstaller%2Fsrc%2FHelpers%2FDatabaseManager.php&line=38", "ajax": false, "filename": "DatabaseManager.php", "line": "38"}, "connection": "mlk", "explain": null, "start_percent": 16.088, "width_percent": 1.069}, {"sql": "select * from `admins` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "admin", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\User\\src\\Http\\Middleware\\Bouncer.php", "line": 18}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.37822, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "mlk", "explain": null, "start_percent": 17.157, "width_percent": 1.069}, {"sql": "select * from `roles` where `roles`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": "middleware", "name": "admin", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\User\\src\\Http\\Middleware\\Bouncer.php", "line": 54}, {"index": 22, "namespace": "middleware", "name": "admin", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\User\\src\\Http\\Middleware\\Bouncer.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 24, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 119}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.3806498, "duration": 0.002, "duration_str": "2ms", "memory": 0, "memory_str": null, "filename": "admin:54", "source": {"index": 21, "namespace": "middleware", "name": "admin", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\User\\src\\Http\\Middleware\\Bouncer.php", "line": 54}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FUser%2Fsrc%2FHttp%2FMiddleware%2FBouncer.php&line=54", "ajax": false, "filename": "Bouncer.php", "line": "54"}, "connection": "mlk", "explain": null, "start_percent": 18.226, "width_percent": 10.689}, {"sql": "select * from `theme_customizations` where `theme_customizations`.`id` = 45 limit 1", "type": "query", "params": [], "bindings": [45], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, {"index": 21, "namespace": null, "name": "packages/Webkul/Theme/src/Repositories/ThemeCustomizationRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Theme\\src\\Repositories\\ThemeCustomizationRepository.php", "line": 42}, {"index": 22, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Settings/ThemeController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Settings\\ThemeController.php", "line": 117}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.386297, "duration": 0.00174, "duration_str": "1.74ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:695", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=695", "ajax": false, "filename": "BaseRepository.php", "line": "695"}, "connection": "mlk", "explain": null, "start_percent": 28.915, "width_percent": 9.3}, {"sql": "select * from `theme_customization_translations` where `theme_customization_translations`.`theme_customization_id` in (45)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 25, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, {"index": 26, "namespace": null, "name": "packages/Webkul/Theme/src/Repositories/ThemeCustomizationRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Theme\\src\\Repositories\\ThemeCustomizationRepository.php", "line": 42}, {"index": 27, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Settings/ThemeController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Settings\\ThemeController.php", "line": 117}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.390254, "duration": 0.0018700000000000001, "duration_str": "1.87ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:695", "source": {"index": 25, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=695", "ajax": false, "filename": "BaseRepository.php", "line": "695"}, "connection": "mlk", "explain": null, "start_percent": 38.215, "width_percent": 9.995}, {"sql": "update `theme_customizations` set `status` = 1, `theme_customizations`.`updated_at` = '2025-08-07 17:00:08' where `id` = 45", "type": "query", "params": [], "bindings": [1, "2025-08-07 17:00:08", 45], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 700}, {"index": 15, "namespace": null, "name": "packages/Webkul/Theme/src/Repositories/ThemeCustomizationRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Theme\\src\\Repositories\\ThemeCustomizationRepository.php", "line": 42}, {"index": 16, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Settings/ThemeController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Settings\\ThemeController.php", "line": 117}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.404402, "duration": 0.0038599999999999997, "duration_str": "3.86ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:700", "source": {"index": 14, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 700}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=700", "ajax": false, "filename": "BaseRepository.php", "line": "700"}, "connection": "mlk", "explain": null, "start_percent": 48.21, "width_percent": 20.631}, {"sql": "update `theme_customization_translations` set `options` = '{\\\"html\\\":\\\"<div class=\\\\\\\"carousel\\\\\\\">\\\\r\\\\n    <div class=\\\\\\\"carousel-track\\\\\\\">\\\\r\\\\n        <div class=\\\\\\\"slide current-slide\\\\\\\" style=\\\\\\\"background-image: url(\\'https:\\\\/\\\\/images.unsplash.com\\\\/photo-1506744038136-46273834b3fb?q=80&w=2070&auto=format&fit=crop\\');\\\\\\\">\\\\r\\\\n            <div class=\\\\\\\"hero-text\\\\\\\">\\\\r\\\\n                 <h1>Like gentle sunshine<\\\\/h1>\\\\r\\\\n\\\\r\\\\n                <p>on quiet leaves<\\\\/p>\\\\r\\\\n            <\\\\/div>\\\\r\\\\n        <\\\\/div>\\\\r\\\\n        <div class=\\\\\\\"slide\\\\\\\" style=\\\\\\\"background-image: url(\\'https:\\\\/\\\\/images.unsplash.com\\\\/photo-1470770841072-f978cf4d019e?q=80&w=2070&auto=format&fit=crop\\');\\\\\\\">\\\\r\\\\n            <div class=\\\\\\\"hero-text\\\\\\\">\\\\r\\\\n                 <h1>Whispers of the Forest<\\\\/h1>\\\\r\\\\n\\\\r\\\\n                <p>a story in every branch<\\\\/p>\\\\r\\\\n            <\\\\/div>\\\\r\\\\n        <\\\\/div>\\\\r\\\\n        <div class=\\\\\\\"slide\\\\\\\" style=\\\\\\\"background-image: url(\\'https:\\\\/\\\\/images.unsplash.com\\\\/photo-1439405326853-58f2724f4d31?q=80&w=2070&auto=format&fit=crop\\');\\\\\\\">\\\\r\\\\n            <div class=\\\\\\\"hero-text\\\\\\\">\\\\r\\\\n                 <h1>Echoes of the Ocean<\\\\/h1>\\\\r\\\\n\\\\r\\\\n                <p>deep and serene<\\\\/p>\\\\r\\\\n            <\\\\/div>\\\\r\\\\n        <\\\\/div>\\\\r\\\\n    <\\\\/div>\\\\r\\\\n    <button class=\\\\\\\"arrow left-arrow\\\\\\\">&#10094;<\\\\/button>\\\\r\\\\n    <button class=\\\\\\\"arrow right-arrow\\\\\\\">&#10095;<\\\\/button>\\\\r\\\\n    <div class=\\\\\\\"carousel-dots\\\\\\\"><\\\\/div>\\\\r\\\\n<\\\\/div>\\\",\\\"css\\\":\\\".carousel {\\\\r\\\\n            position: relative;\\\\r\\\\n            width: 100%;\\\\r\\\\n            height: 55vh;\\\\r\\\\n            min-height: 400px;\\\\r\\\\n            overflow: hidden;\\\\r\\\\n        }\\\\r\\\\n\\\\r\\\\n        .carousel-track {\\\\r\\\\n            position: relative;\\\\r\\\\n            height: 100%;\\\\r\\\\n            display: flex;\\\\r\\\\n            transition: transform 0.5s ease-in-out;\\\\r\\\\n        }\\\\r\\\\n\\\\r\\\\n        .slide {\\\\r\\\\n            flex: 1 0 100%;\\\\r\\\\n            width: 100%;\\\\r\\\\n            height: 100%;\\\\r\\\\n            background-size: cover;\\\\r\\\\n            background-position: center;\\\\r\\\\n            display: flex;\\\\r\\\\n            justify-content: center;\\\\r\\\\n            align-items: center;\\\\r\\\\n        }\\\\r\\\\n\\\\r\\\\n        .hero-text {\\\\r\\\\n            text-align: center;\\\\r\\\\n            color: white;\\\\r\\\\n            max-width: 90%;\\\\r\\\\n            text-shadow: 0 2px 8px rgba(0, 0, 0, 0.5);\\\\r\\\\n            opacity: 0;\\\\r\\\\n            transform: translateY(20px);\\\\r\\\\n            transition: opacity 0.5s ease-out 0.4s, transform 0.5s ease-out 0.4s;\\\\r\\\\n        }\\\\r\\\\n\\\\r\\\\n        .current-slide .hero-text {\\\\r\\\\n            opacity: 1;\\\\r\\\\n            transform: translateY(0);\\\\r\\\\n        }\\\\r\\\\n\\\\r\\\\n        .hero-text h1 {\\\\r\\\\n            font-size: 2.75rem;\\\\r\\\\n            font-weight: 500;\\\\r\\\\n            margin-bottom: 0.5rem;\\\\r\\\\n        }\\\\r\\\\n\\\\r\\\\n        .hero-text p {\\\\r\\\\n            font-size: 1.1rem;\\\\r\\\\n            font-weight: 300;\\\\r\\\\n        }\\\\r\\\\n        \\\\r\\\\n        .arrow {\\\\r\\\\n            position: absolute;\\\\r\\\\n            top: 50%;\\\\r\\\\n            transform: translateY(-50%);\\\\r\\\\n            background: rgba(0, 0, 0, 0.1);\\\\r\\\\n            border: 1px solid rgba(255, 255, 255, 0.2);\\\\r\\\\n            color: rgba(255, 255, 255, 0.8);\\\\r\\\\n            font-size: 1.25rem;\\\\r\\\\n            padding: 8px 14px;\\\\r\\\\n            border-radius: 50%;\\\\r\\\\n            cursor: pointer;\\\\r\\\\n            z-index: 10;\\\\r\\\\n            transition: all 0.2s ease-in-out;\\\\r\\\\n        }\\\\r\\\\n        \\\\r\\\\n        .arrow:hover {\\\\r\\\\n            background-color: rgba(0, 0, 0, 0.4);\\\\r\\\\n            color: white;\\\\r\\\\n            border-color: transparent;\\\\r\\\\n        }\\\\r\\\\n\\\\r\\\\n        .left-arrow { left: 30px; }\\\\r\\\\n        .right-arrow { right: 30px; }\\\\r\\\\n        \\\\r\\\\n        .carousel-dots {\\\\r\\\\n            position: absolute;\\\\r\\\\n            bottom: 20px;\\\\r\\\\n            left: 50%;\\\\r\\\\n            transform: translateX(-50%);\\\\r\\\\n            display: flex;\\\\r\\\\n            gap: 12px;\\\\r\\\\n            z-index: 10;\\\\r\\\\n        }\\\\r\\\\n\\\\r\\\\n        .dot {\\\\r\\\\n            border: none;\\\\r\\\\n            width: 10px;\\\\r\\\\n            height: 10px;\\\\r\\\\n            border-radius: 50%;\\\\r\\\\n            background-color: rgba(255, 255, 255, 0.5);\\\\r\\\\n            cursor: pointer;\\\\r\\\\n            transition: all 0.2s ease;\\\\r\\\\n        }\\\\r\\\\n\\\\r\\\\n        .dot.current-dot {\\\\r\\\\n            background-color: white;\\\\r\\\\n            transform: scale(1.2);\\\\r\\\\n        }\\\\r\\\\n        \\\\r\\\\n        @media (max-width: 768px) {\\\\r\\\\n            .hero-text h1 { font-size: 1.8rem; }\\\\r\\\\n            .hero-text p { font-size: 1rem; }\\\\r\\\\n            .arrow { display: none; }\\\\r\\\\n        }\\\"}' where `id` = 196", "type": "query", "params": [], "bindings": ["{\"html\":\"<div class=\\\"carousel\\\">\\r\\n    <div class=\\\"carousel-track\\\">\\r\\n        <div class=\\\"slide current-slide\\\" style=\\\"background-image: url('https:\\/\\/images.unsplash.com\\/photo-1506744038136-46273834b3fb?q=80&w=2070&auto=format&fit=crop');\\\">\\r\\n            <div class=\\\"hero-text\\\">\\r\\n                 <h1>Like gentle sunshine<\\/h1>\\r\\n\\r\\n                <p>on quiet leaves<\\/p>\\r\\n            <\\/div>\\r\\n        <\\/div>\\r\\n        <div class=\\\"slide\\\" style=\\\"background-image: url('https:\\/\\/images.unsplash.com\\/photo-1470770841072-f978cf4d019e?q=80&w=2070&auto=format&fit=crop');\\\">\\r\\n            <div class=\\\"hero-text\\\">\\r\\n                 <h1>Whispers of the Forest<\\/h1>\\r\\n\\r\\n                <p>a story in every branch<\\/p>\\r\\n            <\\/div>\\r\\n        <\\/div>\\r\\n        <div class=\\\"slide\\\" style=\\\"background-image: url('https:\\/\\/images.unsplash.com\\/photo-1439405326853-58f2724f4d31?q=80&w=2070&auto=format&fit=crop');\\\">\\r\\n            <div class=\\\"hero-text\\\">\\r\\n                 <h1>Echoes of the Ocean<\\/h1>\\r\\n\\r\\n                <p>deep and serene<\\/p>\\r\\n            <\\/div>\\r\\n        <\\/div>\\r\\n    <\\/div>\\r\\n    <button class=\\\"arrow left-arrow\\\">&#10094;<\\/button>\\r\\n    <button class=\\\"arrow right-arrow\\\">&#10095;<\\/button>\\r\\n    <div class=\\\"carousel-dots\\\"><\\/div>\\r\\n<\\/div>\",\"css\":\".carousel {\\r\\n            position: relative;\\r\\n            width: 100%;\\r\\n            height: 55vh;\\r\\n            min-height: 400px;\\r\\n            overflow: hidden;\\r\\n        }\\r\\n\\r\\n        .carousel-track {\\r\\n            position: relative;\\r\\n            height: 100%;\\r\\n            display: flex;\\r\\n            transition: transform 0.5s ease-in-out;\\r\\n        }\\r\\n\\r\\n        .slide {\\r\\n            flex: 1 0 100%;\\r\\n            width: 100%;\\r\\n            height: 100%;\\r\\n            background-size: cover;\\r\\n            background-position: center;\\r\\n            display: flex;\\r\\n            justify-content: center;\\r\\n            align-items: center;\\r\\n        }\\r\\n\\r\\n        .hero-text {\\r\\n            text-align: center;\\r\\n            color: white;\\r\\n            max-width: 90%;\\r\\n            text-shadow: 0 2px 8px rgba(0, 0, 0, 0.5);\\r\\n            opacity: 0;\\r\\n            transform: translateY(20px);\\r\\n            transition: opacity 0.5s ease-out 0.4s, transform 0.5s ease-out 0.4s;\\r\\n        }\\r\\n\\r\\n        .current-slide .hero-text {\\r\\n            opacity: 1;\\r\\n            transform: translateY(0);\\r\\n        }\\r\\n\\r\\n        .hero-text h1 {\\r\\n            font-size: 2.75rem;\\r\\n            font-weight: 500;\\r\\n            margin-bottom: 0.5rem;\\r\\n        }\\r\\n\\r\\n        .hero-text p {\\r\\n            font-size: 1.1rem;\\r\\n            font-weight: 300;\\r\\n        }\\r\\n        \\r\\n        .arrow {\\r\\n            position: absolute;\\r\\n            top: 50%;\\r\\n            transform: translateY(-50%);\\r\\n            background: rgba(0, 0, 0, 0.1);\\r\\n            border: 1px solid rgba(255, 255, 255, 0.2);\\r\\n            color: rgba(255, 255, 255, 0.8);\\r\\n            font-size: 1.25rem;\\r\\n            padding: 8px 14px;\\r\\n            border-radius: 50%;\\r\\n            cursor: pointer;\\r\\n            z-index: 10;\\r\\n            transition: all 0.2s ease-in-out;\\r\\n        }\\r\\n        \\r\\n        .arrow:hover {\\r\\n            background-color: rgba(0, 0, 0, 0.4);\\r\\n            color: white;\\r\\n            border-color: transparent;\\r\\n        }\\r\\n\\r\\n        .left-arrow { left: 30px; }\\r\\n        .right-arrow { right: 30px; }\\r\\n        \\r\\n        .carousel-dots {\\r\\n            position: absolute;\\r\\n            bottom: 20px;\\r\\n            left: 50%;\\r\\n            transform: translateX(-50%);\\r\\n            display: flex;\\r\\n            gap: 12px;\\r\\n            z-index: 10;\\r\\n        }\\r\\n\\r\\n        .dot {\\r\\n            border: none;\\r\\n            width: 10px;\\r\\n            height: 10px;\\r\\n            border-radius: 50%;\\r\\n            background-color: rgba(255, 255, 255, 0.5);\\r\\n            cursor: pointer;\\r\\n            transition: all 0.2s ease;\\r\\n        }\\r\\n\\r\\n        .dot.current-dot {\\r\\n            background-color: white;\\r\\n            transform: scale(1.2);\\r\\n        }\\r\\n        \\r\\n        @media (max-width: 768px) {\\r\\n            .hero-text h1 { font-size: 1.8rem; }\\r\\n            .hero-text p { font-size: 1rem; }\\r\\n            .arrow { display: none; }\\r\\n        }\"}", 196], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 387}, {"index": 15, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 36}, {"index": 22, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 700}, {"index": 23, "namespace": null, "name": "packages/Webkul/Theme/src/Repositories/ThemeCustomizationRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Theme\\src\\Repositories\\ThemeCustomizationRepository.php", "line": 42}, {"index": 24, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Settings/ThemeController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Settings\\ThemeController.php", "line": 117}], "start": **********.4095309, "duration": 0.00215, "duration_str": "2.15ms", "memory": 0, "memory_str": null, "filename": "Translatable.php:387", "source": {"index": 14, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 387}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=387", "ajax": false, "filename": "Translatable.php", "line": "387"}, "connection": "mlk", "explain": null, "start_percent": 68.84, "width_percent": 11.491}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "packages/Webkul/Theme/src/Repositories/ThemeCustomizationRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Theme\\src\\Repositories\\ThemeCustomizationRepository.php", "line": 149}, {"index": 22, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Settings/ThemeController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Settings\\ThemeController.php", "line": 120}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.416847, "duration": 0.0019, "duration_str": "1.9ms", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 80.331, "width_percent": 10.155}, {"sql": "select * from `currencies` where `currencies`.`id` = 2 limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 22, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Core.php", "line": 390}, {"index": 23, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Core.php", "line": 433}, {"index": 24, "namespace": null, "name": "packages/Webkul/FPC/src/Hasher/DefaultHasher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\FPC\\src\\Hasher\\DefaultHasher.php", "line": 29}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-responsecache/src/Hasher/DefaultHasher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\spatie\\laravel-responsecache\\src\\Hasher\\DefaultHasher.php", "line": 18}], "start": **********.42523, "duration": 0.0017800000000000001, "duration_str": "1.78ms", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 20, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 90.486, "width_percent": 9.514}]}, "models": {"data": {"Webkul\\Theme\\Models\\ThemeCustomizationTranslation": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FTheme%2Fsrc%2FModels%2FThemeCustomizationTranslation.php&line=1", "ajax": false, "filename": "ThemeCustomizationTranslation.php", "line": "?"}}, "Webkul\\Core\\Models\\Locale": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FModels%2FLocale.php&line=1", "ajax": false, "filename": "Locale.php", "line": "?"}}, "Webkul\\Core\\Models\\Channel": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FModels%2FChannel.php&line=1", "ajax": false, "filename": "Channel.php", "line": "?"}}, "Webkul\\User\\Models\\Admin": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FUser%2Fsrc%2FModels%2FAdmin.php&line=1", "ajax": false, "filename": "Admin.php", "line": "?"}}, "Webkul\\User\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FUser%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "Webkul\\Theme\\Models\\ThemeCustomization": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FTheme%2Fsrc%2FModels%2FThemeCustomization.php&line=1", "ajax": false, "filename": "ThemeCustomization.php", "line": "?"}}, "Webkul\\Core\\Models\\Currency": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FModels%2FCurrency.php&line=1", "ajax": false, "filename": "Currency.php", "line": "?"}}}, "count": 15, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "302 Found", "full_url": "http://mlk.test/admin/settings/themes/edit/45", "action_name": "admin.settings.themes.update", "controller_action": "Webkul\\Admin\\Http\\Controllers\\Settings\\ThemeController@update", "uri": "POST admin/settings/themes/edit/{id}", "controller": "Webkul\\Admin\\Http\\Controllers\\Settings\\ThemeController@update<a href=\"phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FHttp%2FControllers%2FSettings%2FThemeController.php&line=90\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "admin/settings/themes", "file": "<a href=\"phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FHttp%2FControllers%2FSettings%2FThemeController.php&line=90\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">packages/Webkul/Admin/src/Http/Controllers/Settings/ThemeController.php:90-127</a>", "middleware": "web, admin_locale, Webkul\\Core\\Http\\Middleware\\PreventRequestsDuringMaintenance, admin, Webkul\\Core\\Http\\Middleware\\NoCacheMiddleware", "duration": "343ms", "peak_memory": "42MB", "response": "Redirect to http://mlk.test/admin/settings/themes", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1824434046 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1824434046\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1734739103 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">RL8eYWcLOX1nKpLrVKRAtWfTglgDAXuESTadrPQu</span>\"\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>en</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>options</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>html</span>\" => \"\"\"\n        <span class=sf-dump-str title=\"1211 characters\">&lt;div class=&quot;carousel&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"1211 characters\">    &lt;div class=&quot;carousel-track&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"1211 characters\">        &lt;div class=&quot;slide current-slide&quot; style=&quot;background-image: url(&#039;https://images.unsplash.com/photo-1506744038136-46273834b3fb?q=80&amp;w=2070&amp;auto=format&amp;fit=crop&#039;);&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"1211 characters\">            &lt;div class=&quot;hero-text&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"1211 characters\">                 &lt;h1&gt;Like gentle sunshine&lt;/h1&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"1211 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"1211 characters\">                &lt;p&gt;on quiet leaves&lt;/p&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"1211 characters\">            &lt;/div&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"1211 characters\">        &lt;/div&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"1211 characters\">        &lt;div class=&quot;slide&quot; style=&quot;background-image: url(&#039;https://images.unsplash.com/photo-1470770841072-f978cf4d019e?q=80&amp;w=2070&amp;auto=format&amp;fit=crop&#039;);&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"1211 characters\">            &lt;div class=&quot;hero-text&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"1211 characters\">                 &lt;h1&gt;Whispers of the Forest&lt;/h1&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"1211 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"1211 characters\">                &lt;p&gt;a story in every branch&lt;/p&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"1211 characters\">            &lt;/div&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"1211 characters\">        &lt;/div&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"1211 characters\">        &lt;div class=&quot;slide&quot; style=&quot;background-image: url(&#039;https://images.unsplash.com/photo-1439405326853-58f2724f4d31?q=80&amp;w=2070&amp;auto=format&amp;fit=crop&#039;);&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"1211 characters\">            &lt;div class=&quot;hero-text&quot;&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"1211 characters\">                 &lt;h1&gt;Echoes of the Ocean&lt;/h1&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"1211 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"1211 characters\">                &lt;p&gt;deep and serene&lt;/p&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"1211 characters\">            &lt;/div&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"1211 characters\">        &lt;/div&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"1211 characters\">    &lt;/div&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"1211 characters\">    &lt;button class=&quot;arrow left-arrow&quot;&gt;&amp;#10094;&lt;/button&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"1211 characters\">    &lt;button class=&quot;arrow right-arrow&quot;&gt;&amp;#10095;&lt;/button&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"1211 characters\">    &lt;div class=&quot;carousel-dots&quot;&gt;&lt;/div&gt;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"1211 characters\">&lt;/div&gt;</span>\n        \"\"\"\n      \"<span class=sf-dump-key>css</span>\" => \"\"\"\n        <span class=sf-dump-str title=\"2882 characters\">.carousel {<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2882 characters\">            position: relative;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2882 characters\">            width: 100%;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2882 characters\">            height: 55vh;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2882 characters\">            min-height: 400px;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2882 characters\">            overflow: hidden;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2882 characters\">        }<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2882 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2882 characters\">        .carousel-track {<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2882 characters\">            position: relative;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2882 characters\">            height: 100%;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2882 characters\">            display: flex;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2882 characters\">            transition: transform 0.5s ease-in-out;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2882 characters\">        }<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2882 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2882 characters\">        .slide {<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2882 characters\">            flex: 1 0 100%;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2882 characters\">            width: 100%;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2882 characters\">            height: 100%;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2882 characters\">            background-size: cover;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2882 characters\">            background-position: center;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2882 characters\">            display: flex;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2882 characters\">            justify-content: center;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2882 characters\">            align-items: center;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2882 characters\">        }<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2882 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2882 characters\">        .hero-text {<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2882 characters\">            text-align: center;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2882 characters\">            color: white;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2882 characters\">            max-width: 90%;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2882 characters\">            text-shadow: 0 2px 8px rgba(0, 0, 0, 0.5);<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2882 characters\">            opacity: 0;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2882 characters\">            transform: translateY(20px);<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2882 characters\">            transition: opacity 0.5s ease-out 0.4s, transform 0.5s ease-out 0.4s;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2882 characters\">        }<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2882 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2882 characters\">        .current-slide .hero-text {<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2882 characters\">            opacity: 1;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2882 characters\">            transform: translateY(0);<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2882 characters\">        }<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2882 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2882 characters\">        .hero-text h1 {<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2882 characters\">            font-size: 2.75rem;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2882 characters\">            font-weight: 500;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2882 characters\">            margin-bottom: 0.5rem;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2882 characters\">        }<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2882 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2882 characters\">        .hero-text p {<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2882 characters\">            font-size: 1.1rem;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2882 characters\">            font-weight: 300;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2882 characters\">        }<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2882 characters\">        <span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2882 characters\">        .arrow {<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2882 characters\">            position: absolute;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2882 characters\">            top: 50%;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2882 characters\">            transform: translateY(-50%);<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2882 characters\">            background: rgba(0, 0, 0, 0.1);<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2882 characters\">            border: 1px solid rgba(255, 255, 255, 0.2);<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2882 characters\">            color: rgba(255, 255, 255, 0.8);<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2882 characters\">            font-size: 1.25rem;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2882 characters\">            padding: 8px 14px;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2882 characters\">            border-radius: 50%;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2882 characters\">            cursor: pointer;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2882 characters\">            z-index: 10;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2882 characters\">            transition: all 0.2s ease-in-out;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2882 characters\">        }<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2882 characters\">        <span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2882 characters\">        .arrow:hover {<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2882 characters\">            background-color: rgba(0, 0, 0, 0.4);<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2882 characters\">            color: white;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2882 characters\">            border-color: transparent;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2882 characters\">        }<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2882 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2882 characters\">        .left-arrow { left: 30px; }<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2882 characters\">        .right-arrow { right: 30px; }<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2882 characters\">        <span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2882 characters\">        .carousel-dots {<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2882 characters\">            position: absolute;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2882 characters\">            bottom: 20px;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2882 characters\">            left: 50%;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2882 characters\">            transform: translateX(-50%);<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2882 characters\">            display: flex;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2882 characters\">            gap: 12px;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2882 characters\">            z-index: 10;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2882 characters\">        }<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2882 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2882 characters\">        .dot {<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2882 characters\">            border: none;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2882 characters\">            width: 10px;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2882 characters\">            height: 10px;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2882 characters\">            border-radius: 50%;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2882 characters\">            background-color: rgba(255, 255, 255, 0.5);<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2882 characters\">            cursor: pointer;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2882 characters\">            transition: all 0.2s ease;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2882 characters\">        }<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2882 characters\"><span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2882 characters\">        .dot.current-dot {<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2882 characters\">            background-color: white;<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2882 characters\">            transform: scale(1.2);<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2882 characters\">        }<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2882 characters\">        <span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2882 characters\">        @media (max-width: 768px) {<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2882 characters\">            .hero-text h1 { font-size: 1.8rem; }<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2882 characters\">            .hero-text p { font-size: 1rem; }<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2882 characters\">            .arrow { display: none; }<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n        <span class=sf-dump-str title=\"2882 characters\">        }</span>\n        \"\"\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"14 characters\">static_content</span>\"\n  \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">slogan</span>\"\n  \"<span class=sf-dump-key>sort_order</span>\" => \"<span class=sf-dump-str>8</span>\"\n  \"<span class=sf-dump-key>channel_id</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>theme_code</span>\" => \"<span class=sf-dump-str title=\"7 characters\">default</span>\"\n  \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"2 characters\">on</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1734739103\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-214473325 data-indent-pad=\"  \"><span class=sf-dump-note>array:13</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1018 characters\">admin_locale=eyJpdiI6InlVTlI4cytSTEhwU1oxNXhFNmszTHc9PSIsInZhbHVlIjoiL1djczhIanIvN1ZXTlhGdmpwMWJhNkpqMnNiMHcxMjBic21GQ0ltRzRZV25DczdBcXZaVUNJWlFEakx1bjV6byIsIm1hYyI6IjhmNjlhYzdkYWNjMTVkNjA5ZThmMzlhYmViM2UwYTFiZDQxZDY4ZjEwZTg3NGVjZGNhOTAzODA3ODhhNjllMzQiLCJ0YWciOiIifQ%3D%3D; sidebar_collapsed=0; dark_mode=0; XSRF-TOKEN=eyJpdiI6IjdoSnZRdUd4cDBsdzBXcTU3WitzZkE9PSIsInZhbHVlIjoiWEk1N0Z2Yk9oejhNOGZ6ZXZIS25lMFFmYVZ5YzZWTi9UdDRDcnRYaTJ4bTczUUV5SVQwMVQwZXkreGpYWTJIT1ZQeURpZklJMzhaWm5ERDhiNzhwZ3FFQ3VDREFwRitpK3ZST052UTFOMkgxU2w5cjIvd3pDeXY1ckZZRi9lMUciLCJtYWMiOiJmNTYzMmQwYzQzMWI2YTQxZTc4MzkyYzU3OTE1OTRhZTg1NDRmNTU2ZjRiMDk0NzQ1MGQyMmNlNGZmZmViNDQ4IiwidGFnIjoiIn0%3D; mlk_session=eyJpdiI6Ik56VWdxUERQWFdrd1dKSGxxQ0pLMkE9PSIsInZhbHVlIjoiRHZhRFdNaVh6YUpvL1hTNVBRK3ZhaXRTSGJtRDBQaXJWWXpybnlYeEVjUmhZWElyMi94YVhqcE1LOTVVK1p4MGRtWVhhNnhrWVhjMjA0RnpjVXJ2Y2FhRm44cHRXZzc1eFoxeWFHRk1ZK3pHNCtWUW9ZdW15QmJHR3dsV2F4UkEiLCJtYWMiOiJlNjhkNDFiMDFjMDQ5YTc3NTgwMzhiNWIyNzI0NWFlZTZjYWFlYzA5M2FkM2U5YTljYmNiYWU2MWFjZGY1NTdkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">zh-CN,zh;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"71 characters\">http://mlk.test/admin/settings/themes/edit/45?channel=default&amp;locale=en</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">multipart/form-data; boundary=----WebKitFormBoundaryR8vCfbJvOIU3jBMN</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"15 characters\">http://mlk.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">5174</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">mlk.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-214473325\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-30466098 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>admin_locale</span>\" => \"<span class=sf-dump-str title=\"5 characters\">zh_CN</span>\"\n  \"<span class=sf-dump-key>sidebar_collapsed</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>dark_mode</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">RL8eYWcLOX1nKpLrVKRAtWfTglgDAXuESTadrPQu</span>\"\n  \"<span class=sf-dump-key>mlk_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Zao9ptAmXhjVm7WD2j4izotzmKLg3wYf4OgogoiC</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-30466098\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1170216045 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 07 Aug 2025 16:00:08 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://mlk.test/admin/settings/themes</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>0</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1170216045\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2062372278 data-indent-pad=\"  \"><span class=sf-dump-note>array:10</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">RL8eYWcLOX1nKpLrVKRAtWfTglgDAXuESTadrPQu</span>\"\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">it</span>\"\n  \"<span class=sf-dump-key>currency</span>\" => \"<span class=sf-dump-str title=\"3 characters\">EUR</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"71 characters\">http://mlk.test/admin/settings/themes/edit/45?channel=default&amp;locale=en</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>admin_locale</span>\" => \"<span class=sf-dump-str title=\"5 characters\">zh_CN</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>success</span>\" => \"<span class=sf-dump-str title=\"6 characters\">&#25104;&#21151;&#26356;&#26032;&#20027;&#39064;</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2062372278\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "302 Found", "full_url": "http://mlk.test/admin/settings/themes/edit/45", "action_name": "admin.settings.themes.update", "controller_action": "Webkul\\Admin\\Http\\Controllers\\Settings\\ThemeController@update"}, "badge": "302 Found"}}